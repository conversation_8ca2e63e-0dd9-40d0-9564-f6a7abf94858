{"remainingRequest": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\admin\\student\\StudentEdit.vue?vue&type=template&id=65e55107", "dependencies": [{"path": "I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\admin\\student\\StudentEdit.vue", "mtime": 1749045631741}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\babel.config.js", "mtime": 1749034452000}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749043011899}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749043012919}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749043013784}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749043011899}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749043013130}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["style", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_form", "model", "$data", "formData", "ref", "rules", "add<PERSON><PERSON>", "align", "_component_el_form_item", "label", "prop", "_component_el_input", "sno", "$event", "placeholder", "password", "stname", "_component_el_radio_group", "sex", "onChange", "$options", "onGenderChange", "_component_el_radio", "phone", "_component_el_select", "dbid", "size", "onBuildingChange", "_Fragment", "_renderList", "dormbuildingList", "item", "_createBlock", "_component_el_option", "key", "dbname", "value", "doro", "disabled", "filteredDormitoryList", "specialty", "clsname", "_component_el_button", "type", "onClick", "save", "loading", "btnLoading", "icon", "goBack"], "sources": ["I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\admin\\student\\StudentEdit.vue"], "sourcesContent": ["<template>\r\n    <div style=\"width: 100%;line-height: 30px;text-align: left;\">\r\n       <el-form :model=\"formData\" label-width=\"20%\" ref=\"formDataRef\" :rules=\"addrules\"  align=\"left\">\r\n<el-form-item label=\"学号\" prop=\"sno\">\r\n<el-input v-model=\"formData.sno\" placeholder=\"学号\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item label=\"登录密码\" prop=\"password\">\r\n<el-input v-model=\"formData.password\" placeholder=\"登录密码\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item label=\"姓名\" prop=\"stname\">\r\n<el-input v-model=\"formData.stname\" placeholder=\"姓名\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item label=\"性别\" prop=\"sex\">\r\n<el-radio-group v-model=\"formData.sex\" @change=\"onGenderChange\">\r\n<el-radio label=\"男\">\r\n男\r\n</el-radio>\r\n<el-radio label=\"女\">\r\n女\r\n</el-radio>\r\n</el-radio-group>\r\n</el-form-item>\r\n<el-form-item label=\"手机号码\" prop=\"phone\">\r\n<el-input v-model=\"formData.phone\" placeholder=\"手机号码\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item label=\"宿舍楼\" prop=\"dbid\">\r\n<el-select v-model=\"formData.dbid\" placeholder=\"请选择\"  size=\"small\" @change=\"onBuildingChange\">\r\n<el-option v-for=\"item in dormbuildingList\" :key=\"item.dbid\" :label=\"item.dbname\" :value=\"item.dbid\"></el-option>\r\n</el-select>\r\n</el-form-item>\r\n<el-form-item label=\"宿舍\" prop=\"doro\">\r\n<el-select v-model=\"formData.doro\" placeholder=\"请先选择宿舍楼和性别\"  size=\"small\" :disabled=\"!formData.dbid || !formData.sex\">\r\n<el-option v-for=\"item in filteredDormitoryList\" :key=\"item.doro\" :label=\"item.doro\" :value=\"item.doro\"></el-option>\r\n</el-select>\r\n</el-form-item>\r\n<el-form-item label=\"专业\" prop=\"specialty\">\r\n<el-input v-model=\"formData.specialty\" placeholder=\"专业\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item label=\"班级\" prop=\"clsname\">\r\n<el-input v-model=\"formData.clsname\" placeholder=\"班级\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item>\r\n<el-button type=\"primary\" size=\"small\" @click=\"save\" :loading=\"btnLoading\" icon=\"el-icon-upload\">提 交</el-button>\r\n<el-button type=\"info\" size=\"small\" @click=\"goBack\" icon=\"el-icon-back\">返 回</el-button>\r\n</el-form-item>\r\n</el-form>\r\n\r\n\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport request, { base } from \"../../../../utils/http\";\r\n\r\nexport default {\r\n  name: 'StudentEdit',\r\n  components: {\r\n    \r\n  },  \r\n    data() {\r\n      return {\r\n        id: '',\r\n        isClear: false,\r\n        uploadVisible: false,\r\n        btnLoading: false, //保存按钮加载状态\r\n        formData: {}, //表单数据\r\n        dormbuildingList: [], //宿舍楼列表\r\n        dormitoryList: [], //所有宿舍列表\r\n        filteredDormitoryList: [], //过滤后的宿舍列表\r\n        addrules: {\r\n          sno: [{ required: true, message: '请输入学号', trigger: 'blur' },\r\n],          password: [{ required: true, message: '请输入登录密码', trigger: 'blur' },\r\n],          stname: [{ required: true, message: '请输入姓名', trigger: 'blur' },\r\n],          sex: [{ required: true, message: '请输入性别', trigger: 'blur' },\r\n],          phone: [{ required: true, message: '请输入手机号码', trigger: 'blur' },\r\n        { pattern: /^1[3456789]\\d{9}$/, message: '手机号码格式不正确', trigger: 'blur' },\r\n],          doro: [{ required: true, message: '请选择宿舍', trigger: 'onchange' }],\r\n          specialty: [{ required: true, message: '请输入专业', trigger: 'blur' },\r\n],          clsname: [{ required: true, message: '请输入班级', trigger: 'blur' },\r\n],        },\r\n\r\n      };\r\n    },\r\n    created() {\r\n    this.id = this.$route.query.id;\r\n      this.getDatas();\r\n      this.getdormbuildingList();\r\n      this.getdormitoryList();\r\n    },\r\n\r\n \r\n    methods: {    \r\n\r\n//获取列表数据\r\n        getDatas() {\r\n          let para = {\r\n          };\r\n          this.listLoading = true;\r\n          let url = base + \"/student/get?id=\" + this.id;\r\n          request.post(url, para).then((res) => {\r\n            this.formData = JSON.parse(JSON.stringify(res.resdata));\r\n            this.listLoading = false;\r\n\r\n            this.dbid = this.formData.dbid;\r\n            this.formData.dbid = this.formData.dbname;\r\n            this.doro = this.formData.doro;\r\n            this.formData.doro = this.formData.doro;\r\n\r\n            // 加载完数据后更新宿舍列表\r\n            this.$nextTick(() => {\r\n              this.updateDormitoryList();\r\n            });\r\n          });\r\n        },\r\n    \r\n        // 添加\r\n        save() {\r\n          this.$refs[\"formDataRef\"].validate((valid) => { //验证表单\r\n            if (valid) {\r\n              let url = base + \"/student/update\";\r\n              this.btnLoading = true;\r\n                        this.formData.dbid = this.formData.dbid==this.formData.dbname?this.dbid:this.formData.dbid;\r\n          this.formData.doro = this.formData.doro==this.formData.doro?this.doro:this.formData.doro;\r\n\r\n              request.post(url, this.formData).then((res) => { //发送请求         \r\n                if (res.code == 200) {\r\n                  this.$message({\r\n                    message: \"操作成功\",\r\n                    type: \"success\",\r\n                    offset: 320,\r\n                  });\r\n                  this.$router.push({\r\n                    path: \"/StudentManage\",\r\n                  });\r\n                } else {\r\n                  this.$message({\r\n                    message:res.msg,\r\n                    type: \"error\",\r\n                    offset: 320,\r\n                  });\r\n                }\r\n                this.btnLoading = false;\r\n              });\r\n            }\r\n    \r\n          });\r\n        },\r\n        \r\n       // 返回\r\n        goBack() {\r\n          this.$router.push({\r\n            path: \"/StudentManage\",\r\n          });\r\n        },       \r\n              \r\n            \r\n    getdormbuildingList() {\r\n      let para = {};\r\n      this.listLoading = true;\r\n      let url = base + \"/dormbuilding/list?currentPage=1&pageSize=1000\";\r\n      request.post(url, para).then((res) => {\r\n        this.dormbuildingList = res.resdata;\r\n      });\r\n    },\r\n    \r\n    getdormitoryList() {\r\n      let para = {};\r\n      this.listLoading = true;\r\n      let url = base + \"/dormitory/list?currentPage=1&pageSize=1000\";\r\n      request.post(url, para).then((res) => {\r\n        this.dormitoryList = res.resdata;\r\n      });\r\n    },\r\n\r\n    // 宿舍楼变化时的处理\r\n    onBuildingChange() {\r\n      this.formData.doro = ''; // 清空宿舍选择\r\n      this.updateDormitoryList();\r\n    },\r\n\r\n    // 性别变化时的处理\r\n    onGenderChange() {\r\n      this.formData.doro = ''; // 清空宿舍选择\r\n      this.updateDormitoryList();\r\n    },\r\n\r\n    // 更新宿舍列表\r\n    updateDormitoryList() {\r\n      if (!this.formData.dbid || !this.formData.sex) {\r\n        this.filteredDormitoryList = [];\r\n        return;\r\n      }\r\n\r\n      let para = {\r\n        dbid: this.formData.dbid,\r\n        dorgender: this.formData.sex\r\n      };\r\n      let url = base + \"/dormitory/list?cu\";\r\n      request.post(url, para).then((res) => {\r\n        if (res.code == 200) {\r\n          this.filteredDormitoryList = res.resdata;\r\n        } else {\r\n          this.filteredDormitoryList = [];\r\n          this.$message({\r\n            message: res.msg || \"获取宿舍列表失败\",\r\n            type: \"error\",\r\n            offset: 320,\r\n          });\r\n        }\r\n      }).catch(() => {\r\n        this.filteredDormitoryList = [];\r\n        this.$message({\r\n          message: \"获取宿舍列表失败\",\r\n          type: \"error\",\r\n          offset: 320,\r\n        });\r\n      });\r\n    },\r\n  \r\n           \r\n           \r\n      },\r\n}\r\n\r\n</script>\r\n<style scoped>\r\n</style>\r\n \r\n\r\n"], "mappings": ";;EACSA,KAAuD,EAAvD;IAAA;IAAA;IAAA;EAAA;AAAuD;iDAa5C,KAEpB;iDACoB,KAEpB;iDAuBiG,KAAG;iDAC5B,KAAG;;;;;;;;;;uBA1CvEC,mBAAA,CA+CM,OA/CNC,UA+CM,GA9CHC,YAAA,CA2CGC,kBAAA;IA3COC,KAAK,EAAEC,KAAA,CAAAC,QAAQ;IAAE,aAAW,EAAC,KAAK;IAACC,GAAG,EAAC,aAAa;IAAEC,KAAK,EAAEH,KAAA,CAAAI,QAAQ;IAAGC,KAAK,EAAC;;sBAC/F,MAEe,CAFfR,YAAA,CAEeS,uBAAA;MAFDC,KAAK,EAAC,IAAI;MAACC,IAAI,EAAC;;wBAC9B,MAAkF,CAAlFX,YAAA,CAAkFY,mBAAA;oBAA/DT,KAAA,CAAAC,QAAQ,CAACS,GAAG;mEAAZV,KAAA,CAAAC,QAAQ,CAACS,GAAG,GAAAC,MAAA;QAAEC,WAAW,EAAC,IAAI;QAAElB,KAAkB,EAAlB;UAAA;QAAA;;;QAEnDG,YAAA,CAEeS,uBAAA;MAFDC,KAAK,EAAC,MAAM;MAACC,IAAI,EAAC;;wBAChC,MAAyF,CAAzFX,YAAA,CAAyFY,mBAAA;oBAAtET,KAAA,CAAAC,QAAQ,CAACY,QAAQ;mEAAjBb,KAAA,CAAAC,QAAQ,CAACY,QAAQ,GAAAF,MAAA;QAAEC,WAAW,EAAC,MAAM;QAAElB,KAAkB,EAAlB;UAAA;QAAA;;;QAE1DG,YAAA,CAEeS,uBAAA;MAFDC,KAAK,EAAC,IAAI;MAACC,IAAI,EAAC;;wBAC9B,MAAqF,CAArFX,YAAA,CAAqFY,mBAAA;oBAAlET,KAAA,CAAAC,QAAQ,CAACa,MAAM;mEAAfd,KAAA,CAAAC,QAAQ,CAACa,MAAM,GAAAH,MAAA;QAAEC,WAAW,EAAC,IAAI;QAAElB,KAAkB,EAAlB;UAAA;QAAA;;;QAEtDG,YAAA,CASeS,uBAAA;MATDC,KAAK,EAAC,IAAI;MAACC,IAAI,EAAC;;wBAC9B,MAOiB,CAPjBX,YAAA,CAOiBkB,yBAAA;oBAPQf,KAAA,CAAAC,QAAQ,CAACe,GAAG;mEAAZhB,KAAA,CAAAC,QAAQ,CAACe,GAAG,GAAAL,MAAA;QAAGM,QAAM,EAAEC,QAAA,CAAAC;;0BAChD,MAEW,CAFXtB,YAAA,CAEWuB,mBAAA;UAFDb,KAAK,EAAC;QAAG;4BAAC,MAEpB,C;;YACAV,YAAA,CAEWuB,mBAAA;UAFDb,KAAK,EAAC;QAAG;4BAAC,MAEpB,C;;;;;;;QAGAV,YAAA,CAEeS,uBAAA;MAFDC,KAAK,EAAC,MAAM;MAACC,IAAI,EAAC;;wBAChC,MAAsF,CAAtFX,YAAA,CAAsFY,mBAAA;oBAAnET,KAAA,CAAAC,QAAQ,CAACoB,KAAK;mEAAdrB,KAAA,CAAAC,QAAQ,CAACoB,KAAK,GAAAV,MAAA;QAAEC,WAAW,EAAC,MAAM;QAAElB,KAAkB,EAAlB;UAAA;QAAA;;;QAEvDG,YAAA,CAIeS,uBAAA;MAJDC,KAAK,EAAC,KAAK;MAACC,IAAI,EAAC;;wBAC/B,MAEY,CAFZX,YAAA,CAEYyB,oBAAA;oBAFQtB,KAAA,CAAAC,QAAQ,CAACsB,IAAI;mEAAbvB,KAAA,CAAAC,QAAQ,CAACsB,IAAI,GAAAZ,MAAA;QAAEC,WAAW,EAAC,KAAK;QAAEY,IAAI,EAAC,OAAO;QAAEP,QAAM,EAAEC,QAAA,CAAAO;;0BACjE,MAAgC,E,kBAA3C9B,mBAAA,CAAiH+B,SAAA,QAAAC,WAAA,CAAvF3B,KAAA,CAAA4B,gBAAgB,EAAxBC,IAAI;+BAAtBC,YAAA,CAAiHC,oBAAA;YAApEC,GAAG,EAAEH,IAAI,CAACN,IAAI;YAAGhB,KAAK,EAAEsB,IAAI,CAACI,MAAM;YAAGC,KAAK,EAAEL,IAAI,CAACN;;;;;;;QAG/F1B,YAAA,CAIeS,uBAAA;MAJDC,KAAK,EAAC,IAAI;MAACC,IAAI,EAAC;;wBAC9B,MAEY,CAFZX,YAAA,CAEYyB,oBAAA;oBAFQtB,KAAA,CAAAC,QAAQ,CAACkC,IAAI;mEAAbnC,KAAA,CAAAC,QAAQ,CAACkC,IAAI,GAAAxB,MAAA;QAAEC,WAAW,EAAC,YAAY;QAAEY,IAAI,EAAC,OAAO;QAAEY,QAAQ,GAAGpC,KAAA,CAAAC,QAAQ,CAACsB,IAAI,KAAKvB,KAAA,CAAAC,QAAQ,CAACe;;0BACtG,MAAqC,E,kBAAhDrB,mBAAA,CAAoH+B,SAAA,QAAAC,WAAA,CAA1F3B,KAAA,CAAAqC,qBAAqB,EAA7BR,IAAI;+BAAtBC,YAAA,CAAoHC,oBAAA;YAAlEC,GAAG,EAAEH,IAAI,CAACM,IAAI;YAAG5B,KAAK,EAAEsB,IAAI,CAACM,IAAI;YAAGD,KAAK,EAAEL,IAAI,CAACM;;;;;;;QAGlGtC,YAAA,CAEeS,uBAAA;MAFDC,KAAK,EAAC,IAAI;MAACC,IAAI,EAAC;;wBAC9B,MAAwF,CAAxFX,YAAA,CAAwFY,mBAAA;oBAArET,KAAA,CAAAC,QAAQ,CAACqC,SAAS;mEAAlBtC,KAAA,CAAAC,QAAQ,CAACqC,SAAS,GAAA3B,MAAA;QAAEC,WAAW,EAAC,IAAI;QAAElB,KAAkB,EAAlB;UAAA;QAAA;;;QAEzDG,YAAA,CAEeS,uBAAA;MAFDC,KAAK,EAAC,IAAI;MAACC,IAAI,EAAC;;wBAC9B,MAAsF,CAAtFX,YAAA,CAAsFY,mBAAA;oBAAnET,KAAA,CAAAC,QAAQ,CAACsC,OAAO;mEAAhBvC,KAAA,CAAAC,QAAQ,CAACsC,OAAO,GAAA5B,MAAA;QAAEC,WAAW,EAAC,IAAI;QAAElB,KAAkB,EAAlB;UAAA;QAAA;;;QAEvDG,YAAA,CAGeS,uBAAA;wBAFf,MAAgH,CAAhHT,YAAA,CAAgH2C,oBAAA;QAArGC,IAAI,EAAC,SAAS;QAACjB,IAAI,EAAC,OAAO;QAAEkB,OAAK,EAAExB,QAAA,CAAAyB,IAAI;QAAGC,OAAO,EAAE5C,KAAA,CAAA6C,UAAU;QAAEC,IAAI,EAAC;;0BAAiB,MAAG,C;;iDACpGjD,YAAA,CAAuF2C,oBAAA;QAA5EC,IAAI,EAAC,MAAM;QAACjB,IAAI,EAAC,OAAO;QAAEkB,OAAK,EAAExB,QAAA,CAAA6B,MAAM;QAAED,IAAI,EAAC;;0BAAe,MAAG,C"}]}