{"remainingRequest": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\admin\\total\\Total1.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\admin\\total\\Total1.vue", "mtime": 1749034452000}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749043011899}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749043012919}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749043011899}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749043013130}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\admin\\total\\Total1.vue"], "names": [], "mappings": ";;CASC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CACtD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;CAElC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;GACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;KACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;OACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;OACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;OACT,CAAC;OACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;OACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;KACvE,CAAC;GACH,CAAC;GACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;KACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;GAChB,CAAC;GACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;KAEP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;OACR,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;OAE1C,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE;OACX,CAAC;;OAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;SACpC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;WACnB,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;WAEpB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;aAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;eAChB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;eAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aACjB,CAAC;;aAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;WACpB;SACF,EAAE,CAAC,CAAC,CAAC,EAAE;WACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SAC9B;OACF,CAAC,CAAC;;KAEJ,CAAC;;;KAGD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;OACT,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;SAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;OACxC;KACF,CAAC;KACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;OACZ,CAAC,EAAE,CAAC;OACJ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;SACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;WACN,CAAC,EAAE,CAAC;WACJ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;WAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;WACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;WACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SACnB,CAAC;SACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;WAEL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;WAChB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;WACV,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SACf,CAAC;SACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;WACN;aACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;aACX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;eACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;eACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aACrD,CAAC;aACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;aACpB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;WACnB;SACF;OACF,CAAC;OACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;OAC5B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;SACjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;SACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;SACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;WACN;aACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aACrB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;WACb;SACF;OACF,CAAC;OACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;OAC/D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;OAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;OACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;SACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;OACvB,CAAC,CAAC;KACJ;GACF;CACF,CAAC", "file": "I:/product4/B7839DormManager/DormManager-web/src/views/admin/total/Total1.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n    <div style=\"width: 100%;line-height: 30px;text-align: left;\">\r\n       <div class=\"echart\" id=\"mychart\" :style=\"myChartStyle\"></div>\n \r\n    </div>\r\n</template>\r\n\r\n<script>\r\n\n import request, { base } from \"../../../../utils/http\";\n import * as echarts from \"echarts\";\n \n export default {\n   data() {\n     return {\n       myChart: {},\n       pieData: [\n       ],\n       pieName: [],\n       myChartStyle: { float: \"left\", width: \"100%\", height: \"550px\" } //图表样式\n     };\n   },\n   mounted() {\n     this.getdata();\n   },\n   methods: {\n \n     //数据初始化\n     getdata() {\n       let url = base + \"/ReportData/queryReport\";\n \n       let para = {\n       };\n \n       request.post(url, para).then((res) => {\n         if (res.code == 200) {\n           var ss = res.resdata;\n \n           for (let i = 0; i < ss.length; i++) {\n             this.pieData[i] = {\n               name: ss[i].name,\n               value: ss[i].num\n             };\n \n             this.initDate();\n             this.initEcharts();\n           }\n         } else {\n           this.$message.error(res.msg);\n         }\n       });\n \n     },\n \n \n     initDate() {\n       for (let i = 0; i < this.pieData.length; i++) {\n         this.pieName[i] = this.pieData[i].name;\n       }\n     },\n     initEcharts() {\n       // 饼图\n       const option = {\n         legend: {\n           // 图例\n           data: this.pieName,\n           right: \"10%\",\n           top: \"10%\",\n           orient: \"vertical\"\n         },\n         title: {\n \n           text: \"产品分类销售统计\",\n           top: \"10%\",\n           left: \"center\"\n         },\n         series: [\n           {\n             type: \"pie\",\n             label: {\n               show: true,\n               formatter: \"{b} : {c} ({d}%)\" // b代表名称，c代表对应值，d代表百分比\n             },\n             radius: \"30%\", //饼图半径\n             data: this.pieData\n           }\n         ]\n       };\n       console.log(this.seriesData);\n       const optionFree = {\n         xAxis: {},\n         yAxis: {},\n         series: [\n           {\n             data: this.seriesData,\n             type: \"line\",\n             smooth: true\n           }\n         ]\n       };\n       this.myChart = echarts.init(document.getElementById(\"mychart\"));\n       this.myChart.setOption(option);\n       //随着屏幕大小调节图表\n       window.addEventListener(\"resize\", () => {\n         this.myChart.resize();\n       });\n     }\n   }\n };\r\n</script>\r\n<style scoped>\r\n</style>\r\n \r\n\r\n"]}]}