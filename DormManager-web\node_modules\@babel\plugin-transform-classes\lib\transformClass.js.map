{"version": 3, "names": ["_helperFunctionName", "require", "_helperReplaceSupers", "_helperEnvironmentVisitor", "_helperOptimiseCallExpression", "_core", "_helperAnnotateAsPure", "_inlineCreateSuperHelpers", "buildConstructor", "classRef", "constructorBody", "node", "func", "t", "functionDeclaration", "cloneNode", "inherits", "transformClass", "path", "file", "builtinClasses", "isLoose", "assumptions", "supportUnicodeId", "classState", "parent", "undefined", "scope", "classId", "superFnId", "superName", "superReturns", "isDerived", "extendsNative", "construct", "userConstructor", "userConstructorPath", "hasConstructor", "body", "superThises", "pushedConstructor", "pushedInherits", "pushedCreateClass", "protoAlias", "dynamic<PERSON>eys", "Map", "methods", "instance", "hasComputed", "list", "map", "static", "setState", "newState", "Object", "assign", "findThisesVisitor", "traverse", "visitors", "merge", "environmentVisitor", "ThisExpression", "push", "createClassHelper", "args", "callExpression", "addHelper", "maybeCreateConstructor", "classBodyPath", "get", "isClassMethod", "kind", "params", "constructor", "template", "expression", "ast", "blockStatement", "unshiftContainer", "classMethod", "identifier", "buildBody", "pushBody", "verifyConstructor", "pushDescriptors", "classBodyPaths", "isClassProperty", "buildCodeFrameError", "decorators", "isConstructor", "replaceSupers", "ReplaceSupers", "methodPath", "objectRef", "superRef", "constant<PERSON>uper", "refToPreserve", "replace", "ReturnStatement", "getFunctionParent", "isArrowFunctionExpression", "pushConstructor", "pushMethod", "pushInheritsToBody", "props", "placement", "length", "desc", "obj", "objectExpression", "objectProperty", "key", "properties", "arrayExpression", "nullLiteral", "lastNonNullIndex", "i", "is<PERSON>ull<PERSON>iteral", "slice", "expressionStatement", "wrapSuperCall", "bareSuper", "thisRef", "bareSuperNode", "call", "superIsCallableConstructor", "arguments", "unshift", "thisExpression", "isSpreadElement", "isIdentifier", "argument", "name", "callee", "memberExpression", "logicalExpression", "optimiseCall", "parentPath", "isExpressionStatement", "container", "assignmentExpression", "replaceWith", "returnStatement", "ref", "generateDeclaredUidIdentifier", "thisPath", "isMemberExpression", "object", "bareSupers", "Super", "isCallExpression", "guaranteedSuperBefore<PERSON>inish", "find", "isLoop", "isConditional", "wrapReturn", "returnArg", "thisExpr", "returnParams", "bodyPaths", "pop", "isReturnStatement", "pushContainer", "returnPath", "processMethod", "<PERSON><PERSON><PERSON><PERSON>", "isNumericLiteral", "isBigIntLiteral", "stringLiteral", "String", "value", "toCom<PERSON><PERSON>ey", "fn", "toExpression", "isStringLiteral", "_nameFunction", "nameFunction", "id", "descriptor", "has", "set", "setClassMethods", "insertProtoAliasOnce", "methodName", "computed", "isLiteral", "functionExpression", "generator", "async", "_nameFunction2", "expr", "inheritsComments", "generateUidIdentifier", "classProto", "protoDeclaration", "variableDeclaration", "variableDeclarator", "method", "directives", "pushConstructorToBody", "hasInstanceDescriptors", "hasStaticDescriptors", "addCreateSuperHelper", "extractDynamicKeys", "elem", "isPure", "generateUidIdentifierBasedOnNode", "setupClosureParamsArgs", "closureParams", "closureArgs", "arg", "annotateAsPure", "param", "classTransformer", "superClass", "hasBinding", "noClassCalls", "isStrict", "isInStrictMode", "constructorOnly", "directive", "directiveLiteral", "arrowFunctionExpression"], "sources": ["../src/transformClass.ts"], "sourcesContent": ["import type { Node<PERSON><PERSON>, Scope, Visitor } from \"@babel/traverse\";\nimport nameFunction from \"@babel/helper-function-name\";\nimport ReplaceSupers from \"@babel/helper-replace-supers\";\nimport environmentVisitor from \"@babel/helper-environment-visitor\";\nimport optimiseCall from \"@babel/helper-optimise-call-expression\";\nimport { traverse, template, types as t, type File } from \"@babel/core\";\nimport annotateAsPure from \"@babel/helper-annotate-as-pure\";\n\nimport addCreateSuperHelper from \"./inline-createSuper-helpers.ts\";\n\ntype ClassAssumptions = {\n  setClassMethods: boolean;\n  constantSuper: boolean;\n  superIsCallableConstructor: boolean;\n  noClassCalls: boolean;\n};\n\ntype ClassConstructor = t.ClassMethod & { kind: \"constructor\" };\n\nfunction buildConstructor(\n  classRef: t.Identifier,\n  constructorBody: t.BlockStatement,\n  node: t.Class,\n) {\n  const func = t.functionDeclaration(\n    t.cloneNode(classRef),\n    [],\n    constructorBody,\n  );\n  t.inherits(func, node);\n  return func;\n}\n\ntype Descriptor = {\n  key: t.Expression;\n  get?: t.Expression | null;\n  set?: t.Expression | null;\n  value?: t.Expression | null;\n  constructor?: t.Expression | null;\n};\n\ntype State = {\n  parent: t.Node;\n  scope: Scope;\n  node: t.Class;\n  path: NodePath<t.Class>;\n  file: File;\n\n  classId: t.Identifier | void;\n  classRef: t.Identifier;\n  superFnId: t.Identifier;\n  superName: t.Expression | null;\n  superReturns: NodePath<t.ReturnStatement>[];\n  isDerived: boolean;\n  extendsNative: boolean;\n\n  construct: t.FunctionDeclaration;\n  constructorBody: t.BlockStatement;\n  userConstructor: ClassConstructor;\n  userConstructorPath: NodePath<ClassConstructor>;\n  hasConstructor: boolean;\n\n  body: t.Statement[];\n  superThises: NodePath<t.ThisExpression>[];\n  pushedConstructor: boolean;\n  pushedInherits: boolean;\n  pushedCreateClass: boolean;\n  protoAlias: t.Identifier | null;\n  isLoose: boolean;\n\n  dynamicKeys: Map<string, t.Expression>;\n\n  methods: {\n    // 'list' is in the same order as the elements appear in the class body.\n    // if there aren't computed keys, we can safely reorder class elements\n    // and use 'map' to merge duplicates.\n    instance: {\n      hasComputed: boolean;\n      list: Descriptor[];\n      map: Map<string, Descriptor>;\n    };\n    static: {\n      hasComputed: boolean;\n      list: Descriptor[];\n      map: Map<string, Descriptor>;\n    };\n  };\n};\n\ntype PropertyInfo = {\n  instance: t.ObjectExpression[] | null;\n  static: t.ObjectExpression[] | null;\n};\n\nexport default function transformClass(\n  path: NodePath<t.Class>,\n  file: File,\n  builtinClasses: ReadonlySet<string>,\n  isLoose: boolean,\n  assumptions: ClassAssumptions,\n  supportUnicodeId: boolean,\n) {\n  const classState: State = {\n    parent: undefined,\n    scope: undefined,\n    node: undefined,\n    path: undefined,\n    file: undefined,\n\n    classId: undefined,\n    classRef: undefined,\n    superFnId: undefined,\n    superName: null,\n    superReturns: [],\n    isDerived: false,\n    extendsNative: false,\n\n    construct: undefined,\n    constructorBody: undefined,\n    userConstructor: undefined,\n    userConstructorPath: undefined,\n    hasConstructor: false,\n\n    body: [],\n    superThises: [],\n    pushedConstructor: false,\n    pushedInherits: false,\n    pushedCreateClass: false,\n    protoAlias: null,\n    isLoose: false,\n\n    dynamicKeys: new Map(),\n\n    methods: {\n      instance: {\n        hasComputed: false,\n        list: [],\n        map: new Map(),\n      },\n      static: {\n        hasComputed: false,\n        list: [],\n        map: new Map(),\n      },\n    },\n  };\n\n  const setState = (newState: Partial<State>) => {\n    Object.assign(classState, newState);\n  };\n\n  const findThisesVisitor = traverse.visitors.merge([\n    environmentVisitor,\n    {\n      ThisExpression(path) {\n        classState.superThises.push(path);\n      },\n    },\n  ]);\n\n  function createClassHelper(args: t.Expression[]) {\n    return t.callExpression(classState.file.addHelper(\"createClass\"), args);\n  }\n\n  /**\n   * Creates a class constructor or bail out if there is one\n   */\n  function maybeCreateConstructor() {\n    const classBodyPath = classState.path.get(\"body\");\n    for (const path of classBodyPath.get(\"body\")) {\n      if (path.isClassMethod({ kind: \"constructor\" })) return;\n    }\n\n    let params: t.FunctionExpression[\"params\"], body;\n\n    if (classState.isDerived) {\n      const constructor = template.expression.ast`\n        (function () {\n          super(...arguments);\n        })\n      ` as t.FunctionExpression;\n      params = constructor.params;\n      body = constructor.body;\n    } else {\n      params = [];\n      body = t.blockStatement([]);\n    }\n\n    classBodyPath.unshiftContainer(\n      \"body\",\n      t.classMethod(\"constructor\", t.identifier(\"constructor\"), params, body),\n    );\n  }\n\n  function buildBody() {\n    maybeCreateConstructor();\n    pushBody();\n    verifyConstructor();\n\n    if (classState.userConstructor) {\n      const { constructorBody, userConstructor, construct } = classState;\n\n      constructorBody.body.push(...userConstructor.body.body);\n      t.inherits(construct, userConstructor);\n      t.inherits(constructorBody, userConstructor.body);\n    }\n\n    pushDescriptors();\n  }\n\n  function pushBody() {\n    const classBodyPaths: Array<any> = classState.path.get(\"body.body\");\n\n    for (const path of classBodyPaths) {\n      const node = path.node;\n\n      if (path.isClassProperty()) {\n        throw path.buildCodeFrameError(\"Missing class properties transform.\");\n      }\n\n      if (node.decorators) {\n        throw path.buildCodeFrameError(\n          \"Method has decorators, put the decorator plugin before the classes one.\",\n        );\n      }\n\n      if (t.isClassMethod(node)) {\n        const isConstructor = node.kind === \"constructor\";\n\n        const replaceSupers = new ReplaceSupers({\n          methodPath: path,\n          objectRef: classState.classRef,\n          superRef: classState.superName,\n          constantSuper: assumptions.constantSuper,\n          file: classState.file,\n          refToPreserve: classState.classRef,\n        });\n\n        replaceSupers.replace();\n\n        const superReturns: NodePath<t.ReturnStatement>[] = [];\n        path.traverse(\n          traverse.visitors.merge([\n            environmentVisitor,\n            {\n              ReturnStatement(path) {\n                if (!path.getFunctionParent().isArrowFunctionExpression()) {\n                  superReturns.push(path);\n                }\n              },\n            },\n          ]),\n        );\n\n        if (isConstructor) {\n          pushConstructor(superReturns, node as ClassConstructor, path);\n        } else {\n          pushMethod(node, path);\n        }\n      }\n    }\n  }\n\n  function pushDescriptors() {\n    pushInheritsToBody();\n\n    const { body } = classState;\n\n    const props: PropertyInfo = {\n      instance: null,\n      static: null,\n    };\n\n    for (const placement of [\"static\", \"instance\"] as const) {\n      if (classState.methods[placement].list.length) {\n        props[placement] = classState.methods[placement].list.map(desc => {\n          const obj = t.objectExpression([\n            t.objectProperty(t.identifier(\"key\"), desc.key),\n          ]);\n\n          for (const kind of [\"get\", \"set\", \"value\"] as const) {\n            if (desc[kind] != null) {\n              obj.properties.push(\n                t.objectProperty(t.identifier(kind), desc[kind]),\n              );\n            }\n          }\n\n          return obj;\n        });\n      }\n    }\n\n    if (props.instance || props.static) {\n      let args = [\n        t.cloneNode(classState.classRef), // Constructor\n        props.instance ? t.arrayExpression(props.instance) : t.nullLiteral(), // instanceDescriptors\n        props.static ? t.arrayExpression(props.static) : t.nullLiteral(), // staticDescriptors\n      ];\n\n      let lastNonNullIndex = 0;\n      for (let i = 0; i < args.length; i++) {\n        if (!t.isNullLiteral(args[i])) lastNonNullIndex = i;\n      }\n      args = args.slice(0, lastNonNullIndex + 1);\n\n      body.push(t.expressionStatement(createClassHelper(args)));\n      classState.pushedCreateClass = true;\n    }\n  }\n\n  function wrapSuperCall(\n    bareSuper: NodePath<t.CallExpression>,\n    superRef: t.Expression,\n    thisRef: () => t.Identifier,\n    body: NodePath<t.BlockStatement>,\n  ) {\n    const bareSuperNode = bareSuper.node;\n    let call;\n\n    if (assumptions.superIsCallableConstructor) {\n      bareSuperNode.arguments.unshift(t.thisExpression());\n      if (\n        bareSuperNode.arguments.length === 2 &&\n        t.isSpreadElement(bareSuperNode.arguments[1]) &&\n        t.isIdentifier(bareSuperNode.arguments[1].argument, {\n          name: \"arguments\",\n        })\n      ) {\n        // special case single arguments spread\n        bareSuperNode.arguments[1] = bareSuperNode.arguments[1].argument;\n        bareSuperNode.callee = t.memberExpression(\n          t.cloneNode(superRef),\n          t.identifier(\"apply\"),\n        );\n      } else {\n        bareSuperNode.callee = t.memberExpression(\n          t.cloneNode(superRef),\n          t.identifier(\"call\"),\n        );\n      }\n\n      call = t.logicalExpression(\"||\", bareSuperNode, t.thisExpression());\n    } else {\n      call = optimiseCall(\n        t.cloneNode(classState.superFnId),\n        t.thisExpression(),\n        bareSuperNode.arguments,\n        false,\n      );\n    }\n\n    if (\n      bareSuper.parentPath.isExpressionStatement() &&\n      bareSuper.parentPath.container === body.node.body &&\n      body.node.body.length - 1 === bareSuper.parentPath.key\n    ) {\n      // this super call is the last statement in the body so we can just straight up\n      // turn it into a return\n\n      if (classState.superThises.length) {\n        call = t.assignmentExpression(\"=\", thisRef(), call);\n      }\n\n      bareSuper.parentPath.replaceWith(t.returnStatement(call));\n    } else {\n      bareSuper.replaceWith(t.assignmentExpression(\"=\", thisRef(), call));\n    }\n  }\n\n  function verifyConstructor() {\n    if (!classState.isDerived) return;\n\n    const path = classState.userConstructorPath;\n    const body = path.get(\"body\");\n\n    path.traverse(findThisesVisitor);\n\n    let thisRef = function () {\n      const ref = path.scope.generateDeclaredUidIdentifier(\"this\");\n      thisRef = () => t.cloneNode(ref);\n      return ref;\n    };\n\n    for (const thisPath of classState.superThises) {\n      const { node, parentPath } = thisPath;\n      if (parentPath.isMemberExpression({ object: node })) {\n        thisPath.replaceWith(thisRef());\n        continue;\n      }\n      thisPath.replaceWith(\n        t.callExpression(classState.file.addHelper(\"assertThisInitialized\"), [\n          thisRef(),\n        ]),\n      );\n    }\n\n    const bareSupers: NodePath<t.CallExpression>[] = [];\n    path.traverse(\n      traverse.visitors.merge([\n        environmentVisitor,\n        {\n          Super(path) {\n            const { node, parentPath } = path;\n            if (parentPath.isCallExpression({ callee: node })) {\n              bareSupers.unshift(parentPath);\n            }\n          },\n        } as Visitor,\n      ]),\n    );\n\n    let guaranteedSuperBeforeFinish = !!bareSupers.length;\n\n    for (const bareSuper of bareSupers) {\n      wrapSuperCall(bareSuper, classState.superName, thisRef, body);\n\n      if (guaranteedSuperBeforeFinish) {\n        bareSuper.find(function (parentPath) {\n          // hit top so short circuit\n          if (parentPath === path) {\n            return true;\n          }\n\n          if (\n            parentPath.isLoop() ||\n            parentPath.isConditional() ||\n            parentPath.isArrowFunctionExpression()\n          ) {\n            guaranteedSuperBeforeFinish = false;\n            return true;\n          }\n        });\n      }\n    }\n\n    let wrapReturn;\n\n    if (classState.isLoose) {\n      wrapReturn = (returnArg: t.Expression | void) => {\n        const thisExpr = t.callExpression(\n          classState.file.addHelper(\"assertThisInitialized\"),\n          [thisRef()],\n        );\n        return returnArg\n          ? t.logicalExpression(\"||\", returnArg, thisExpr)\n          : thisExpr;\n      };\n    } else {\n      wrapReturn = (returnArg: t.Expression | undefined) => {\n        const returnParams: t.Expression[] = [thisRef()];\n        if (returnArg != null) {\n          returnParams.push(returnArg);\n        }\n        return t.callExpression(\n          classState.file.addHelper(\"possibleConstructorReturn\"),\n          returnParams,\n        );\n      };\n    }\n\n    // if we have a return as the last node in the body then we've already caught that\n    // return\n    const bodyPaths = body.get(\"body\");\n    if (!bodyPaths.length || !bodyPaths.pop().isReturnStatement()) {\n      body.pushContainer(\n        \"body\",\n        t.returnStatement(\n          guaranteedSuperBeforeFinish ? thisRef() : wrapReturn(),\n        ),\n      );\n    }\n\n    for (const returnPath of classState.superReturns) {\n      returnPath\n        .get(\"argument\")\n        .replaceWith(wrapReturn(returnPath.node.argument));\n    }\n  }\n\n  /**\n   * Push a method to its respective mutatorMap.\n   */\n  function pushMethod(node: t.ClassMethod, path?: NodePath) {\n    const scope = path ? path.scope : classState.scope;\n\n    if (node.kind === \"method\") {\n      if (processMethod(node, scope)) return;\n    }\n\n    const placement = node.static ? \"static\" : \"instance\";\n    const methods = classState.methods[placement];\n\n    const descKey = node.kind === \"method\" ? \"value\" : node.kind;\n    const key =\n      t.isNumericLiteral(node.key) || t.isBigIntLiteral(node.key)\n        ? t.stringLiteral(String(node.key.value))\n        : t.toComputedKey(node);\n\n    let fn: t.Expression = t.toExpression(node);\n\n    if (t.isStringLiteral(key)) {\n      // infer function name\n      if (node.kind === \"method\") {\n        // @ts-expect-error Fixme: we are passing a ClassMethod to nameFunction, but nameFunction\n        // does not seem to support it\n        fn =\n          nameFunction(\n            // @ts-expect-error Fixme: we are passing a ClassMethod to nameFunction, but nameFunction\n            // does not seem to support it\n            { id: key, node: node, scope },\n            undefined,\n            supportUnicodeId,\n          ) ?? fn;\n      }\n    } else {\n      // todo(flow->ts) find a way to avoid \"key as t.StringLiteral\" below which relies on this assignment\n      methods.hasComputed = true;\n    }\n\n    let descriptor: Descriptor;\n    if (\n      !methods.hasComputed &&\n      methods.map.has((key as t.StringLiteral).value)\n    ) {\n      descriptor = methods.map.get((key as t.StringLiteral).value);\n      descriptor[descKey] = fn;\n\n      if (descKey === \"value\") {\n        descriptor.get = null;\n        descriptor.set = null;\n      } else {\n        descriptor.value = null;\n      }\n    } else {\n      descriptor = {\n        key:\n          // private name has been handled in class-properties transform\n          key as t.Expression,\n        [descKey]: fn,\n      } as Descriptor;\n      methods.list.push(descriptor);\n\n      if (!methods.hasComputed) {\n        methods.map.set((key as t.StringLiteral).value, descriptor);\n      }\n    }\n  }\n\n  function processMethod(node: t.ClassMethod, scope: Scope) {\n    if (assumptions.setClassMethods && !node.decorators) {\n      // use assignments instead of define properties for loose classes\n      let { classRef } = classState;\n      if (!node.static) {\n        insertProtoAliasOnce();\n        classRef = classState.protoAlias;\n      }\n      const methodName = t.memberExpression(\n        t.cloneNode(classRef),\n        node.key,\n        node.computed || t.isLiteral(node.key),\n      );\n\n      let func: t.Expression = t.functionExpression(\n        null,\n        // @ts-expect-error Fixme: should throw when we see TSParameterProperty\n        node.params,\n        node.body,\n        node.generator,\n        node.async,\n      );\n      t.inherits(func, node);\n\n      const key = t.toComputedKey(node, node.key);\n      if (t.isStringLiteral(key)) {\n        // @ts-expect-error: requires strictNullCheck\n        func =\n          nameFunction(\n            {\n              node: func,\n              id: key,\n              scope,\n            },\n            undefined,\n            supportUnicodeId,\n          ) ?? func;\n      }\n\n      const expr = t.expressionStatement(\n        t.assignmentExpression(\"=\", methodName, func),\n      );\n      t.inheritsComments(expr, node);\n      classState.body.push(expr);\n      return true;\n    }\n\n    return false;\n  }\n\n  function insertProtoAliasOnce() {\n    if (classState.protoAlias === null) {\n      setState({ protoAlias: classState.scope.generateUidIdentifier(\"proto\") });\n      const classProto = t.memberExpression(\n        classState.classRef,\n        t.identifier(\"prototype\"),\n      );\n      const protoDeclaration = t.variableDeclaration(\"var\", [\n        t.variableDeclarator(classState.protoAlias, classProto),\n      ]);\n\n      classState.body.push(protoDeclaration);\n    }\n  }\n\n  /**\n   * Replace the constructor body of our class.\n   */\n  function pushConstructor(\n    superReturns: NodePath<t.ReturnStatement>[],\n    method: ClassConstructor,\n    path: NodePath<ClassConstructor>,\n  ) {\n    setState({\n      userConstructorPath: path,\n      userConstructor: method,\n      hasConstructor: true,\n      superReturns,\n    });\n\n    const { construct } = classState;\n\n    t.inheritsComments(construct, method);\n\n    // @ts-expect-error Fixme: should throw when we see TSParameterProperty\n    construct.params = method.params;\n\n    t.inherits(construct.body, method.body);\n    construct.body.directives = method.body.directives;\n\n    pushConstructorToBody();\n  }\n\n  function pushConstructorToBody() {\n    if (classState.pushedConstructor) return;\n    classState.pushedConstructor = true;\n\n    // we haven't pushed any descriptors yet\n    // @ts-expect-error todo(flow->ts) maybe remove this block - properties from condition are not used anywhere else\n    if (classState.hasInstanceDescriptors || classState.hasStaticDescriptors) {\n      pushDescriptors();\n    }\n\n    classState.body.push(classState.construct);\n\n    pushInheritsToBody();\n  }\n\n  /**\n   * Push inherits helper to body.\n   */\n  function pushInheritsToBody() {\n    if (!classState.isDerived || classState.pushedInherits) return;\n\n    const superFnId = path.scope.generateUidIdentifier(\"super\");\n\n    setState({ pushedInherits: true, superFnId });\n\n    // Unshift to ensure that the constructor inheritance is set up before\n    // any properties can be assigned to the prototype.\n\n    if (!assumptions.superIsCallableConstructor) {\n      classState.body.unshift(\n        t.variableDeclaration(\"var\", [\n          t.variableDeclarator(\n            superFnId,\n            t.callExpression(addCreateSuperHelper(classState.file), [\n              t.cloneNode(classState.classRef),\n            ]),\n          ),\n        ]),\n      );\n    }\n\n    classState.body.unshift(\n      t.expressionStatement(\n        t.callExpression(\n          classState.file.addHelper(\n            classState.isLoose ? \"inheritsLoose\" : \"inherits\",\n          ),\n          [t.cloneNode(classState.classRef), t.cloneNode(classState.superName)],\n        ),\n      ),\n    );\n  }\n\n  function extractDynamicKeys() {\n    const { dynamicKeys, node, scope } = classState;\n\n    for (const elem of node.body.body) {\n      if (!t.isClassMethod(elem) || !elem.computed) continue;\n      if (scope.isPure(elem.key, /* constants only*/ true)) continue;\n\n      const id = scope.generateUidIdentifierBasedOnNode(elem.key);\n      dynamicKeys.set(id.name, elem.key);\n\n      elem.key = id;\n    }\n  }\n\n  function setupClosureParamsArgs() {\n    const { superName, dynamicKeys } = classState;\n    const closureParams = [];\n    const closureArgs = [];\n\n    if (classState.isDerived) {\n      let arg = t.cloneNode(superName);\n      if (classState.extendsNative) {\n        arg = t.callExpression(classState.file.addHelper(\"wrapNativeSuper\"), [\n          arg,\n        ]);\n        annotateAsPure(arg);\n      }\n\n      const param =\n        classState.scope.generateUidIdentifierBasedOnNode(superName);\n\n      closureParams.push(param);\n      closureArgs.push(arg);\n\n      setState({ superName: t.cloneNode(param) });\n    }\n\n    for (const [name, value] of dynamicKeys) {\n      closureParams.push(t.identifier(name));\n      closureArgs.push(value);\n    }\n\n    return { closureParams, closureArgs };\n  }\n\n  function classTransformer(\n    path: NodePath<t.Class>,\n    file: File,\n    builtinClasses: ReadonlySet<string>,\n    isLoose: boolean,\n  ) {\n    setState({\n      parent: path.parent,\n      scope: path.scope,\n      node: path.node,\n      path,\n      file,\n      isLoose,\n    });\n\n    setState({\n      classId: classState.node.id,\n      // this is the name of the binding that will **always** reference the class we've constructed\n      classRef: classState.node.id\n        ? t.identifier(classState.node.id.name)\n        : classState.scope.generateUidIdentifier(\"class\"),\n      superName: classState.node.superClass,\n      isDerived: !!classState.node.superClass,\n      constructorBody: t.blockStatement([]),\n    });\n\n    setState({\n      extendsNative:\n        t.isIdentifier(classState.superName) &&\n        builtinClasses.has(classState.superName.name) &&\n        !classState.scope.hasBinding(\n          classState.superName.name,\n          /* noGlobals */ true,\n        ),\n    });\n\n    const { classRef, node, constructorBody } = classState;\n\n    setState({\n      construct: buildConstructor(classRef, constructorBody, node),\n    });\n\n    extractDynamicKeys();\n\n    const { body } = classState;\n    const { closureParams, closureArgs } = setupClosureParamsArgs();\n\n    buildBody();\n\n    // make sure this class isn't directly called (with A() instead new A())\n    if (!assumptions.noClassCalls) {\n      constructorBody.body.unshift(\n        t.expressionStatement(\n          t.callExpression(classState.file.addHelper(\"classCallCheck\"), [\n            t.thisExpression(),\n            t.cloneNode(classState.classRef),\n          ]),\n        ),\n      );\n    }\n\n    const isStrict = path.isInStrictMode();\n    let constructorOnly = classState.classId && body.length === 1;\n    if (constructorOnly && !isStrict) {\n      for (const param of classState.construct.params) {\n        // It's illegal to put a use strict directive into the body of a function\n        // with non-simple parameters for some reason. So, we have to use a strict\n        // wrapper function.\n        if (!t.isIdentifier(param)) {\n          constructorOnly = false;\n          break;\n        }\n      }\n    }\n\n    const directives = constructorOnly\n      ? (body[0] as t.FunctionExpression | t.FunctionDeclaration).body\n          .directives\n      : [];\n    if (!isStrict) {\n      directives.push(t.directive(t.directiveLiteral(\"use strict\")));\n    }\n\n    if (constructorOnly) {\n      // named class with only a constructor\n      const expr = t.toExpression(\n        body[0] as t.FunctionExpression | t.FunctionDeclaration,\n      );\n      return classState.isLoose ? expr : createClassHelper([expr]);\n    }\n\n    let returnArg: t.Expression = t.cloneNode(classState.classRef);\n    if (!classState.pushedCreateClass && !classState.isLoose) {\n      returnArg = createClassHelper([returnArg]);\n    }\n\n    body.push(t.returnStatement(returnArg));\n    const container = t.arrowFunctionExpression(\n      closureParams,\n      t.blockStatement(body, directives),\n    );\n    return t.callExpression(container, closureArgs);\n  }\n\n  return classTransformer(path, file, builtinClasses, isLoose);\n}\n"], "mappings": ";;;;;;AACA,IAAAA,mBAAA,GAAAC,OAAA;AACA,IAAAC,oBAAA,GAAAD,OAAA;AACA,IAAAE,yBAAA,GAAAF,OAAA;AACA,IAAAG,6BAAA,GAAAH,OAAA;AACA,IAAAI,KAAA,GAAAJ,OAAA;AACA,IAAAK,qBAAA,GAAAL,OAAA;AAEA,IAAAM,yBAAA,GAAAN,OAAA;AAWA,SAASO,gBAAgBA,CACvBC,QAAsB,EACtBC,eAAiC,EACjCC,IAAa,EACb;EACA,MAAMC,IAAI,GAAGC,WAAC,CAACC,mBAAmB,CAChCD,WAAC,CAACE,SAAS,CAACN,QAAQ,CAAC,EACrB,EAAE,EACFC,eACF,CAAC;EACDG,WAAC,CAACG,QAAQ,CAACJ,IAAI,EAAED,IAAI,CAAC;EACtB,OAAOC,IAAI;AACb;AA+De,SAASK,cAAcA,CACpCC,IAAuB,EACvBC,IAAU,EACVC,cAAmC,EACnCC,OAAgB,EAChBC,WAA6B,EAC7BC,gBAAyB,EACzB;EACA,MAAMC,UAAiB,GAAG;IACxBC,MAAM,EAAEC,SAAS;IACjBC,KAAK,EAAED,SAAS;IAChBf,IAAI,EAAEe,SAAS;IACfR,IAAI,EAAEQ,SAAS;IACfP,IAAI,EAAEO,SAAS;IAEfE,OAAO,EAAEF,SAAS;IAClBjB,QAAQ,EAAEiB,SAAS;IACnBG,SAAS,EAAEH,SAAS;IACpBI,SAAS,EAAE,IAAI;IACfC,YAAY,EAAE,EAAE;IAChBC,SAAS,EAAE,KAAK;IAChBC,aAAa,EAAE,KAAK;IAEpBC,SAAS,EAAER,SAAS;IACpBhB,eAAe,EAAEgB,SAAS;IAC1BS,eAAe,EAAET,SAAS;IAC1BU,mBAAmB,EAAEV,SAAS;IAC9BW,cAAc,EAAE,KAAK;IAErBC,IAAI,EAAE,EAAE;IACRC,WAAW,EAAE,EAAE;IACfC,iBAAiB,EAAE,KAAK;IACxBC,cAAc,EAAE,KAAK;IACrBC,iBAAiB,EAAE,KAAK;IACxBC,UAAU,EAAE,IAAI;IAChBtB,OAAO,EAAE,KAAK;IAEduB,WAAW,EAAE,IAAIC,GAAG,CAAC,CAAC;IAEtBC,OAAO,EAAE;MACPC,QAAQ,EAAE;QACRC,WAAW,EAAE,KAAK;QAClBC,IAAI,EAAE,EAAE;QACRC,GAAG,EAAE,IAAIL,GAAG,CAAC;MACf,CAAC;MACDM,MAAM,EAAE;QACNH,WAAW,EAAE,KAAK;QAClBC,IAAI,EAAE,EAAE;QACRC,GAAG,EAAE,IAAIL,GAAG,CAAC;MACf;IACF;EACF,CAAC;EAED,MAAMO,QAAQ,GAAIC,QAAwB,IAAK;IAC7CC,MAAM,CAACC,MAAM,CAAC/B,UAAU,EAAE6B,QAAQ,CAAC;EACrC,CAAC;EAED,MAAMG,iBAAiB,GAAGC,cAAQ,CAACC,QAAQ,CAACC,KAAK,CAAC,CAChDC,iCAAkB,EAClB;IACEC,cAAcA,CAAC3C,IAAI,EAAE;MACnBM,UAAU,CAACe,WAAW,CAACuB,IAAI,CAAC5C,IAAI,CAAC;IACnC;EACF,CAAC,CACF,CAAC;EAEF,SAAS6C,iBAAiBA,CAACC,IAAoB,EAAE;IAC/C,OAAOnD,WAAC,CAACoD,cAAc,CAACzC,UAAU,CAACL,IAAI,CAAC+C,SAAS,CAAC,aAAa,CAAC,EAAEF,IAAI,CAAC;EACzE;EAKA,SAASG,sBAAsBA,CAAA,EAAG;IAChC,MAAMC,aAAa,GAAG5C,UAAU,CAACN,IAAI,CAACmD,GAAG,CAAC,MAAM,CAAC;IACjD,KAAK,MAAMnD,IAAI,IAAIkD,aAAa,CAACC,GAAG,CAAC,MAAM,CAAC,EAAE;MAC5C,IAAInD,IAAI,CAACoD,aAAa,CAAC;QAAEC,IAAI,EAAE;MAAc,CAAC,CAAC,EAAE;IACnD;IAEA,IAAIC,MAAsC,EAAElC,IAAI;IAEhD,IAAId,UAAU,CAACQ,SAAS,EAAE;MACxB,MAAMyC,WAAW,GAAGC,cAAQ,CAACC,UAAU,CAACC,GAAI;AAClD;AACA;AACA;AACA,OAA+B;MACzBJ,MAAM,GAAGC,WAAW,CAACD,MAAM;MAC3BlC,IAAI,GAAGmC,WAAW,CAACnC,IAAI;IACzB,CAAC,MAAM;MACLkC,MAAM,GAAG,EAAE;MACXlC,IAAI,GAAGzB,WAAC,CAACgE,cAAc,CAAC,EAAE,CAAC;IAC7B;IAEAT,aAAa,CAACU,gBAAgB,CAC5B,MAAM,EACNjE,WAAC,CAACkE,WAAW,CAAC,aAAa,EAAElE,WAAC,CAACmE,UAAU,CAAC,aAAa,CAAC,EAAER,MAAM,EAAElC,IAAI,CACxE,CAAC;EACH;EAEA,SAAS2C,SAASA,CAAA,EAAG;IACnBd,sBAAsB,CAAC,CAAC;IACxBe,QAAQ,CAAC,CAAC;IACVC,iBAAiB,CAAC,CAAC;IAEnB,IAAI3D,UAAU,CAACW,eAAe,EAAE;MAC9B,MAAM;QAAEzB,eAAe;QAAEyB,eAAe;QAAED;MAAU,CAAC,GAAGV,UAAU;MAElEd,eAAe,CAAC4B,IAAI,CAACwB,IAAI,CAAC,GAAG3B,eAAe,CAACG,IAAI,CAACA,IAAI,CAAC;MACvDzB,WAAC,CAACG,QAAQ,CAACkB,SAAS,EAAEC,eAAe,CAAC;MACtCtB,WAAC,CAACG,QAAQ,CAACN,eAAe,EAAEyB,eAAe,CAACG,IAAI,CAAC;IACnD;IAEA8C,eAAe,CAAC,CAAC;EACnB;EAEA,SAASF,QAAQA,CAAA,EAAG;IAClB,MAAMG,cAA0B,GAAG7D,UAAU,CAACN,IAAI,CAACmD,GAAG,CAAC,WAAW,CAAC;IAEnE,KAAK,MAAMnD,IAAI,IAAImE,cAAc,EAAE;MACjC,MAAM1E,IAAI,GAAGO,IAAI,CAACP,IAAI;MAEtB,IAAIO,IAAI,CAACoE,eAAe,CAAC,CAAC,EAAE;QAC1B,MAAMpE,IAAI,CAACqE,mBAAmB,CAAC,qCAAqC,CAAC;MACvE;MAEA,IAAI5E,IAAI,CAAC6E,UAAU,EAAE;QACnB,MAAMtE,IAAI,CAACqE,mBAAmB,CAC5B,yEACF,CAAC;MACH;MAEA,IAAI1E,WAAC,CAACyD,aAAa,CAAC3D,IAAI,CAAC,EAAE;QACzB,MAAM8E,aAAa,GAAG9E,IAAI,CAAC4D,IAAI,KAAK,aAAa;QAEjD,MAAMmB,aAAa,GAAG,IAAIC,4BAAa,CAAC;UACtCC,UAAU,EAAE1E,IAAI;UAChB2E,SAAS,EAAErE,UAAU,CAACf,QAAQ;UAC9BqF,QAAQ,EAAEtE,UAAU,CAACM,SAAS;UAC9BiE,aAAa,EAAEzE,WAAW,CAACyE,aAAa;UACxC5E,IAAI,EAAEK,UAAU,CAACL,IAAI;UACrB6E,aAAa,EAAExE,UAAU,CAACf;QAC5B,CAAC,CAAC;QAEFiF,aAAa,CAACO,OAAO,CAAC,CAAC;QAEvB,MAAMlE,YAA2C,GAAG,EAAE;QACtDb,IAAI,CAACuC,QAAQ,CACXA,cAAQ,CAACC,QAAQ,CAACC,KAAK,CAAC,CACtBC,iCAAkB,EAClB;UACEsC,eAAeA,CAAChF,IAAI,EAAE;YACpB,IAAI,CAACA,IAAI,CAACiF,iBAAiB,CAAC,CAAC,CAACC,yBAAyB,CAAC,CAAC,EAAE;cACzDrE,YAAY,CAAC+B,IAAI,CAAC5C,IAAI,CAAC;YACzB;UACF;QACF,CAAC,CACF,CACH,CAAC;QAED,IAAIuE,aAAa,EAAE;UACjBY,eAAe,CAACtE,YAAY,EAAEpB,IAAI,EAAsBO,IAAI,CAAC;QAC/D,CAAC,MAAM;UACLoF,UAAU,CAAC3F,IAAI,EAAEO,IAAI,CAAC;QACxB;MACF;IACF;EACF;EAEA,SAASkE,eAAeA,CAAA,EAAG;IACzBmB,kBAAkB,CAAC,CAAC;IAEpB,MAAM;MAAEjE;IAAK,CAAC,GAAGd,UAAU;IAE3B,MAAMgF,KAAmB,GAAG;MAC1BzD,QAAQ,EAAE,IAAI;MACdI,MAAM,EAAE;IACV,CAAC;IAED,KAAK,MAAMsD,SAAS,IAAI,CAAC,QAAQ,EAAE,UAAU,CAAC,EAAW;MACvD,IAAIjF,UAAU,CAACsB,OAAO,CAAC2D,SAAS,CAAC,CAACxD,IAAI,CAACyD,MAAM,EAAE;QAC7CF,KAAK,CAACC,SAAS,CAAC,GAAGjF,UAAU,CAACsB,OAAO,CAAC2D,SAAS,CAAC,CAACxD,IAAI,CAACC,GAAG,CAACyD,IAAI,IAAI;UAChE,MAAMC,GAAG,GAAG/F,WAAC,CAACgG,gBAAgB,CAAC,CAC7BhG,WAAC,CAACiG,cAAc,CAACjG,WAAC,CAACmE,UAAU,CAAC,KAAK,CAAC,EAAE2B,IAAI,CAACI,GAAG,CAAC,CAChD,CAAC;UAEF,KAAK,MAAMxC,IAAI,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,CAAC,EAAW;YACnD,IAAIoC,IAAI,CAACpC,IAAI,CAAC,IAAI,IAAI,EAAE;cACtBqC,GAAG,CAACI,UAAU,CAAClD,IAAI,CACjBjD,WAAC,CAACiG,cAAc,CAACjG,WAAC,CAACmE,UAAU,CAACT,IAAI,CAAC,EAAEoC,IAAI,CAACpC,IAAI,CAAC,CACjD,CAAC;YACH;UACF;UAEA,OAAOqC,GAAG;QACZ,CAAC,CAAC;MACJ;IACF;IAEA,IAAIJ,KAAK,CAACzD,QAAQ,IAAIyD,KAAK,CAACrD,MAAM,EAAE;MAClC,IAAIa,IAAI,GAAG,CACTnD,WAAC,CAACE,SAAS,CAACS,UAAU,CAACf,QAAQ,CAAC,EAChC+F,KAAK,CAACzD,QAAQ,GAAGlC,WAAC,CAACoG,eAAe,CAACT,KAAK,CAACzD,QAAQ,CAAC,GAAGlC,WAAC,CAACqG,WAAW,CAAC,CAAC,EACpEV,KAAK,CAACrD,MAAM,GAAGtC,WAAC,CAACoG,eAAe,CAACT,KAAK,CAACrD,MAAM,CAAC,GAAGtC,WAAC,CAACqG,WAAW,CAAC,CAAC,CACjE;MAED,IAAIC,gBAAgB,GAAG,CAAC;MACxB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGpD,IAAI,CAAC0C,MAAM,EAAEU,CAAC,EAAE,EAAE;QACpC,IAAI,CAACvG,WAAC,CAACwG,aAAa,CAACrD,IAAI,CAACoD,CAAC,CAAC,CAAC,EAAED,gBAAgB,GAAGC,CAAC;MACrD;MACApD,IAAI,GAAGA,IAAI,CAACsD,KAAK,CAAC,CAAC,EAAEH,gBAAgB,GAAG,CAAC,CAAC;MAE1C7E,IAAI,CAACwB,IAAI,CAACjD,WAAC,CAAC0G,mBAAmB,CAACxD,iBAAiB,CAACC,IAAI,CAAC,CAAC,CAAC;MACzDxC,UAAU,CAACkB,iBAAiB,GAAG,IAAI;IACrC;EACF;EAEA,SAAS8E,aAAaA,CACpBC,SAAqC,EACrC3B,QAAsB,EACtB4B,OAA2B,EAC3BpF,IAAgC,EAChC;IACA,MAAMqF,aAAa,GAAGF,SAAS,CAAC9G,IAAI;IACpC,IAAIiH,IAAI;IAER,IAAItG,WAAW,CAACuG,0BAA0B,EAAE;MAC1CF,aAAa,CAACG,SAAS,CAACC,OAAO,CAAClH,WAAC,CAACmH,cAAc,CAAC,CAAC,CAAC;MACnD,IACEL,aAAa,CAACG,SAAS,CAACpB,MAAM,KAAK,CAAC,IACpC7F,WAAC,CAACoH,eAAe,CAACN,aAAa,CAACG,SAAS,CAAC,CAAC,CAAC,CAAC,IAC7CjH,WAAC,CAACqH,YAAY,CAACP,aAAa,CAACG,SAAS,CAAC,CAAC,CAAC,CAACK,QAAQ,EAAE;QAClDC,IAAI,EAAE;MACR,CAAC,CAAC,EACF;QAEAT,aAAa,CAACG,SAAS,CAAC,CAAC,CAAC,GAAGH,aAAa,CAACG,SAAS,CAAC,CAAC,CAAC,CAACK,QAAQ;QAChER,aAAa,CAACU,MAAM,GAAGxH,WAAC,CAACyH,gBAAgB,CACvCzH,WAAC,CAACE,SAAS,CAAC+E,QAAQ,CAAC,EACrBjF,WAAC,CAACmE,UAAU,CAAC,OAAO,CACtB,CAAC;MACH,CAAC,MAAM;QACL2C,aAAa,CAACU,MAAM,GAAGxH,WAAC,CAACyH,gBAAgB,CACvCzH,WAAC,CAACE,SAAS,CAAC+E,QAAQ,CAAC,EACrBjF,WAAC,CAACmE,UAAU,CAAC,MAAM,CACrB,CAAC;MACH;MAEA4C,IAAI,GAAG/G,WAAC,CAAC0H,iBAAiB,CAAC,IAAI,EAAEZ,aAAa,EAAE9G,WAAC,CAACmH,cAAc,CAAC,CAAC,CAAC;IACrE,CAAC,MAAM;MACLJ,IAAI,GAAG,IAAAY,qCAAY,EACjB3H,WAAC,CAACE,SAAS,CAACS,UAAU,CAACK,SAAS,CAAC,EACjChB,WAAC,CAACmH,cAAc,CAAC,CAAC,EAClBL,aAAa,CAACG,SAAS,EACvB,KACF,CAAC;IACH;IAEA,IACEL,SAAS,CAACgB,UAAU,CAACC,qBAAqB,CAAC,CAAC,IAC5CjB,SAAS,CAACgB,UAAU,CAACE,SAAS,KAAKrG,IAAI,CAAC3B,IAAI,CAAC2B,IAAI,IACjDA,IAAI,CAAC3B,IAAI,CAAC2B,IAAI,CAACoE,MAAM,GAAG,CAAC,KAAKe,SAAS,CAACgB,UAAU,CAAC1B,GAAG,EACtD;MAIA,IAAIvF,UAAU,CAACe,WAAW,CAACmE,MAAM,EAAE;QACjCkB,IAAI,GAAG/G,WAAC,CAAC+H,oBAAoB,CAAC,GAAG,EAAElB,OAAO,CAAC,CAAC,EAAEE,IAAI,CAAC;MACrD;MAEAH,SAAS,CAACgB,UAAU,CAACI,WAAW,CAAChI,WAAC,CAACiI,eAAe,CAAClB,IAAI,CAAC,CAAC;IAC3D,CAAC,MAAM;MACLH,SAAS,CAACoB,WAAW,CAAChI,WAAC,CAAC+H,oBAAoB,CAAC,GAAG,EAAElB,OAAO,CAAC,CAAC,EAAEE,IAAI,CAAC,CAAC;IACrE;EACF;EAEA,SAASzC,iBAAiBA,CAAA,EAAG;IAC3B,IAAI,CAAC3D,UAAU,CAACQ,SAAS,EAAE;IAE3B,MAAMd,IAAI,GAAGM,UAAU,CAACY,mBAAmB;IAC3C,MAAME,IAAI,GAAGpB,IAAI,CAACmD,GAAG,CAAC,MAAM,CAAC;IAE7BnD,IAAI,CAACuC,QAAQ,CAACD,iBAAiB,CAAC;IAEhC,IAAIkE,OAAO,GAAG,SAAAA,CAAA,EAAY;MACxB,MAAMqB,GAAG,GAAG7H,IAAI,CAACS,KAAK,CAACqH,6BAA6B,CAAC,MAAM,CAAC;MAC5DtB,OAAO,GAAGA,CAAA,KAAM7G,WAAC,CAACE,SAAS,CAACgI,GAAG,CAAC;MAChC,OAAOA,GAAG;IACZ,CAAC;IAED,KAAK,MAAME,QAAQ,IAAIzH,UAAU,CAACe,WAAW,EAAE;MAC7C,MAAM;QAAE5B,IAAI;QAAE8H;MAAW,CAAC,GAAGQ,QAAQ;MACrC,IAAIR,UAAU,CAACS,kBAAkB,CAAC;QAAEC,MAAM,EAAExI;MAAK,CAAC,CAAC,EAAE;QACnDsI,QAAQ,CAACJ,WAAW,CAACnB,OAAO,CAAC,CAAC,CAAC;QAC/B;MACF;MACAuB,QAAQ,CAACJ,WAAW,CAClBhI,WAAC,CAACoD,cAAc,CAACzC,UAAU,CAACL,IAAI,CAAC+C,SAAS,CAAC,uBAAuB,CAAC,EAAE,CACnEwD,OAAO,CAAC,CAAC,CACV,CACH,CAAC;IACH;IAEA,MAAM0B,UAAwC,GAAG,EAAE;IACnDlI,IAAI,CAACuC,QAAQ,CACXA,cAAQ,CAACC,QAAQ,CAACC,KAAK,CAAC,CACtBC,iCAAkB,EAClB;MACEyF,KAAKA,CAACnI,IAAI,EAAE;QACV,MAAM;UAAEP,IAAI;UAAE8H;QAAW,CAAC,GAAGvH,IAAI;QACjC,IAAIuH,UAAU,CAACa,gBAAgB,CAAC;UAAEjB,MAAM,EAAE1H;QAAK,CAAC,CAAC,EAAE;UACjDyI,UAAU,CAACrB,OAAO,CAACU,UAAU,CAAC;QAChC;MACF;IACF,CAAC,CACF,CACH,CAAC;IAED,IAAIc,2BAA2B,GAAG,CAAC,CAACH,UAAU,CAAC1C,MAAM;IAErD,KAAK,MAAMe,SAAS,IAAI2B,UAAU,EAAE;MAClC5B,aAAa,CAACC,SAAS,EAAEjG,UAAU,CAACM,SAAS,EAAE4F,OAAO,EAAEpF,IAAI,CAAC;MAE7D,IAAIiH,2BAA2B,EAAE;QAC/B9B,SAAS,CAAC+B,IAAI,CAAC,UAAUf,UAAU,EAAE;UAEnC,IAAIA,UAAU,KAAKvH,IAAI,EAAE;YACvB,OAAO,IAAI;UACb;UAEA,IACEuH,UAAU,CAACgB,MAAM,CAAC,CAAC,IACnBhB,UAAU,CAACiB,aAAa,CAAC,CAAC,IAC1BjB,UAAU,CAACrC,yBAAyB,CAAC,CAAC,EACtC;YACAmD,2BAA2B,GAAG,KAAK;YACnC,OAAO,IAAI;UACb;QACF,CAAC,CAAC;MACJ;IACF;IAEA,IAAII,UAAU;IAEd,IAAInI,UAAU,CAACH,OAAO,EAAE;MACtBsI,UAAU,GAAIC,SAA8B,IAAK;QAC/C,MAAMC,QAAQ,GAAGhJ,WAAC,CAACoD,cAAc,CAC/BzC,UAAU,CAACL,IAAI,CAAC+C,SAAS,CAAC,uBAAuB,CAAC,EAClD,CAACwD,OAAO,CAAC,CAAC,CACZ,CAAC;QACD,OAAOkC,SAAS,GACZ/I,WAAC,CAAC0H,iBAAiB,CAAC,IAAI,EAAEqB,SAAS,EAAEC,QAAQ,CAAC,GAC9CA,QAAQ;MACd,CAAC;IACH,CAAC,MAAM;MACLF,UAAU,GAAIC,SAAmC,IAAK;QACpD,MAAME,YAA4B,GAAG,CAACpC,OAAO,CAAC,CAAC,CAAC;QAChD,IAAIkC,SAAS,IAAI,IAAI,EAAE;UACrBE,YAAY,CAAChG,IAAI,CAAC8F,SAAS,CAAC;QAC9B;QACA,OAAO/I,WAAC,CAACoD,cAAc,CACrBzC,UAAU,CAACL,IAAI,CAAC+C,SAAS,CAAC,2BAA2B,CAAC,EACtD4F,YACF,CAAC;MACH,CAAC;IACH;IAIA,MAAMC,SAAS,GAAGzH,IAAI,CAAC+B,GAAG,CAAC,MAAM,CAAC;IAClC,IAAI,CAAC0F,SAAS,CAACrD,MAAM,IAAI,CAACqD,SAAS,CAACC,GAAG,CAAC,CAAC,CAACC,iBAAiB,CAAC,CAAC,EAAE;MAC7D3H,IAAI,CAAC4H,aAAa,CAChB,MAAM,EACNrJ,WAAC,CAACiI,eAAe,CACfS,2BAA2B,GAAG7B,OAAO,CAAC,CAAC,GAAGiC,UAAU,CAAC,CACvD,CACF,CAAC;IACH;IAEA,KAAK,MAAMQ,UAAU,IAAI3I,UAAU,CAACO,YAAY,EAAE;MAChDoI,UAAU,CACP9F,GAAG,CAAC,UAAU,CAAC,CACfwE,WAAW,CAACc,UAAU,CAACQ,UAAU,CAACxJ,IAAI,CAACwH,QAAQ,CAAC,CAAC;IACtD;EACF;EAKA,SAAS7B,UAAUA,CAAC3F,IAAmB,EAAEO,IAAe,EAAE;IACxD,MAAMS,KAAK,GAAGT,IAAI,GAAGA,IAAI,CAACS,KAAK,GAAGH,UAAU,CAACG,KAAK;IAElD,IAAIhB,IAAI,CAAC4D,IAAI,KAAK,QAAQ,EAAE;MAC1B,IAAI6F,aAAa,CAACzJ,IAAI,EAAEgB,KAAK,CAAC,EAAE;IAClC;IAEA,MAAM8E,SAAS,GAAG9F,IAAI,CAACwC,MAAM,GAAG,QAAQ,GAAG,UAAU;IACrD,MAAML,OAAO,GAAGtB,UAAU,CAACsB,OAAO,CAAC2D,SAAS,CAAC;IAE7C,MAAM4D,OAAO,GAAG1J,IAAI,CAAC4D,IAAI,KAAK,QAAQ,GAAG,OAAO,GAAG5D,IAAI,CAAC4D,IAAI;IAC5D,MAAMwC,GAAG,GACPlG,WAAC,CAACyJ,gBAAgB,CAAC3J,IAAI,CAACoG,GAAG,CAAC,IAAIlG,WAAC,CAAC0J,eAAe,CAAC5J,IAAI,CAACoG,GAAG,CAAC,GACvDlG,WAAC,CAAC2J,aAAa,CAACC,MAAM,CAAC9J,IAAI,CAACoG,GAAG,CAAC2D,KAAK,CAAC,CAAC,GACvC7J,WAAC,CAAC8J,aAAa,CAAChK,IAAI,CAAC;IAE3B,IAAIiK,EAAgB,GAAG/J,WAAC,CAACgK,YAAY,CAAClK,IAAI,CAAC;IAE3C,IAAIE,WAAC,CAACiK,eAAe,CAAC/D,GAAG,CAAC,EAAE;MAE1B,IAAIpG,IAAI,CAAC4D,IAAI,KAAK,QAAQ,EAAE;QAAA,IAAAwG,aAAA;QAG1BH,EAAE,IAAAG,aAAA,GACA,IAAAC,2BAAY,EAGV;UAAEC,EAAE,EAAElE,GAAG;UAAEpG,IAAI,EAAEA,IAAI;UAAEgB;QAAM,CAAC,EAC9BD,SAAS,EACTH,gBACF,CAAC,YAAAwJ,aAAA,GAAIH,EAAE;MACX;IACF,CAAC,MAAM;MAEL9H,OAAO,CAACE,WAAW,GAAG,IAAI;IAC5B;IAEA,IAAIkI,UAAsB;IAC1B,IACE,CAACpI,OAAO,CAACE,WAAW,IACpBF,OAAO,CAACI,GAAG,CAACiI,GAAG,CAAEpE,GAAG,CAAqB2D,KAAK,CAAC,EAC/C;MACAQ,UAAU,GAAGpI,OAAO,CAACI,GAAG,CAACmB,GAAG,CAAE0C,GAAG,CAAqB2D,KAAK,CAAC;MAC5DQ,UAAU,CAACb,OAAO,CAAC,GAAGO,EAAE;MAExB,IAAIP,OAAO,KAAK,OAAO,EAAE;QACvBa,UAAU,CAAC7G,GAAG,GAAG,IAAI;QACrB6G,UAAU,CAACE,GAAG,GAAG,IAAI;MACvB,CAAC,MAAM;QACLF,UAAU,CAACR,KAAK,GAAG,IAAI;MACzB;IACF,CAAC,MAAM;MACLQ,UAAU,GAAG;QACXnE,GAAG,EAEDA,GAAmB;QACrB,CAACsD,OAAO,GAAGO;MACb,CAAe;MACf9H,OAAO,CAACG,IAAI,CAACa,IAAI,CAACoH,UAAU,CAAC;MAE7B,IAAI,CAACpI,OAAO,CAACE,WAAW,EAAE;QACxBF,OAAO,CAACI,GAAG,CAACkI,GAAG,CAAErE,GAAG,CAAqB2D,KAAK,EAAEQ,UAAU,CAAC;MAC7D;IACF;EACF;EAEA,SAASd,aAAaA,CAACzJ,IAAmB,EAAEgB,KAAY,EAAE;IACxD,IAAIL,WAAW,CAAC+J,eAAe,IAAI,CAAC1K,IAAI,CAAC6E,UAAU,EAAE;MAEnD,IAAI;QAAE/E;MAAS,CAAC,GAAGe,UAAU;MAC7B,IAAI,CAACb,IAAI,CAACwC,MAAM,EAAE;QAChBmI,oBAAoB,CAAC,CAAC;QACtB7K,QAAQ,GAAGe,UAAU,CAACmB,UAAU;MAClC;MACA,MAAM4I,UAAU,GAAG1K,WAAC,CAACyH,gBAAgB,CACnCzH,WAAC,CAACE,SAAS,CAACN,QAAQ,CAAC,EACrBE,IAAI,CAACoG,GAAG,EACRpG,IAAI,CAAC6K,QAAQ,IAAI3K,WAAC,CAAC4K,SAAS,CAAC9K,IAAI,CAACoG,GAAG,CACvC,CAAC;MAED,IAAInG,IAAkB,GAAGC,WAAC,CAAC6K,kBAAkB,CAC3C,IAAI,EAEJ/K,IAAI,CAAC6D,MAAM,EACX7D,IAAI,CAAC2B,IAAI,EACT3B,IAAI,CAACgL,SAAS,EACdhL,IAAI,CAACiL,KACP,CAAC;MACD/K,WAAC,CAACG,QAAQ,CAACJ,IAAI,EAAED,IAAI,CAAC;MAEtB,MAAMoG,GAAG,GAAGlG,WAAC,CAAC8J,aAAa,CAAChK,IAAI,EAAEA,IAAI,CAACoG,GAAG,CAAC;MAC3C,IAAIlG,WAAC,CAACiK,eAAe,CAAC/D,GAAG,CAAC,EAAE;QAAA,IAAA8E,cAAA;QAE1BjL,IAAI,IAAAiL,cAAA,GACF,IAAAb,2BAAY,EACV;UACErK,IAAI,EAAEC,IAAI;UACVqK,EAAE,EAAElE,GAAG;UACPpF;QACF,CAAC,EACDD,SAAS,EACTH,gBACF,CAAC,YAAAsK,cAAA,GAAIjL,IAAI;MACb;MAEA,MAAMkL,IAAI,GAAGjL,WAAC,CAAC0G,mBAAmB,CAChC1G,WAAC,CAAC+H,oBAAoB,CAAC,GAAG,EAAE2C,UAAU,EAAE3K,IAAI,CAC9C,CAAC;MACDC,WAAC,CAACkL,gBAAgB,CAACD,IAAI,EAAEnL,IAAI,CAAC;MAC9Ba,UAAU,CAACc,IAAI,CAACwB,IAAI,CAACgI,IAAI,CAAC;MAC1B,OAAO,IAAI;IACb;IAEA,OAAO,KAAK;EACd;EAEA,SAASR,oBAAoBA,CAAA,EAAG;IAC9B,IAAI9J,UAAU,CAACmB,UAAU,KAAK,IAAI,EAAE;MAClCS,QAAQ,CAAC;QAAET,UAAU,EAAEnB,UAAU,CAACG,KAAK,CAACqK,qBAAqB,CAAC,OAAO;MAAE,CAAC,CAAC;MACzE,MAAMC,UAAU,GAAGpL,WAAC,CAACyH,gBAAgB,CACnC9G,UAAU,CAACf,QAAQ,EACnBI,WAAC,CAACmE,UAAU,CAAC,WAAW,CAC1B,CAAC;MACD,MAAMkH,gBAAgB,GAAGrL,WAAC,CAACsL,mBAAmB,CAAC,KAAK,EAAE,CACpDtL,WAAC,CAACuL,kBAAkB,CAAC5K,UAAU,CAACmB,UAAU,EAAEsJ,UAAU,CAAC,CACxD,CAAC;MAEFzK,UAAU,CAACc,IAAI,CAACwB,IAAI,CAACoI,gBAAgB,CAAC;IACxC;EACF;EAKA,SAAS7F,eAAeA,CACtBtE,YAA2C,EAC3CsK,MAAwB,EACxBnL,IAAgC,EAChC;IACAkC,QAAQ,CAAC;MACPhB,mBAAmB,EAAElB,IAAI;MACzBiB,eAAe,EAAEkK,MAAM;MACvBhK,cAAc,EAAE,IAAI;MACpBN;IACF,CAAC,CAAC;IAEF,MAAM;MAAEG;IAAU,CAAC,GAAGV,UAAU;IAEhCX,WAAC,CAACkL,gBAAgB,CAAC7J,SAAS,EAAEmK,MAAM,CAAC;IAGrCnK,SAAS,CAACsC,MAAM,GAAG6H,MAAM,CAAC7H,MAAM;IAEhC3D,WAAC,CAACG,QAAQ,CAACkB,SAAS,CAACI,IAAI,EAAE+J,MAAM,CAAC/J,IAAI,CAAC;IACvCJ,SAAS,CAACI,IAAI,CAACgK,UAAU,GAAGD,MAAM,CAAC/J,IAAI,CAACgK,UAAU;IAElDC,qBAAqB,CAAC,CAAC;EACzB;EAEA,SAASA,qBAAqBA,CAAA,EAAG;IAC/B,IAAI/K,UAAU,CAACgB,iBAAiB,EAAE;IAClChB,UAAU,CAACgB,iBAAiB,GAAG,IAAI;IAInC,IAAIhB,UAAU,CAACgL,sBAAsB,IAAIhL,UAAU,CAACiL,oBAAoB,EAAE;MACxErH,eAAe,CAAC,CAAC;IACnB;IAEA5D,UAAU,CAACc,IAAI,CAACwB,IAAI,CAACtC,UAAU,CAACU,SAAS,CAAC;IAE1CqE,kBAAkB,CAAC,CAAC;EACtB;EAKA,SAASA,kBAAkBA,CAAA,EAAG;IAC5B,IAAI,CAAC/E,UAAU,CAACQ,SAAS,IAAIR,UAAU,CAACiB,cAAc,EAAE;IAExD,MAAMZ,SAAS,GAAGX,IAAI,CAACS,KAAK,CAACqK,qBAAqB,CAAC,OAAO,CAAC;IAE3D5I,QAAQ,CAAC;MAAEX,cAAc,EAAE,IAAI;MAAEZ;IAAU,CAAC,CAAC;IAK7C,IAAI,CAACP,WAAW,CAACuG,0BAA0B,EAAE;MAC3CrG,UAAU,CAACc,IAAI,CAACyF,OAAO,CACrBlH,WAAC,CAACsL,mBAAmB,CAAC,KAAK,EAAE,CAC3BtL,WAAC,CAACuL,kBAAkB,CAClBvK,SAAS,EACThB,WAAC,CAACoD,cAAc,CAAC,IAAAyI,iCAAoB,EAAClL,UAAU,CAACL,IAAI,CAAC,EAAE,CACtDN,WAAC,CAACE,SAAS,CAACS,UAAU,CAACf,QAAQ,CAAC,CACjC,CACH,CAAC,CACF,CACH,CAAC;IACH;IAEAe,UAAU,CAACc,IAAI,CAACyF,OAAO,CACrBlH,WAAC,CAAC0G,mBAAmB,CACnB1G,WAAC,CAACoD,cAAc,CACdzC,UAAU,CAACL,IAAI,CAAC+C,SAAS,CACvB1C,UAAU,CAACH,OAAO,GAAG,eAAe,GAAG,UACzC,CAAC,EACD,CAACR,WAAC,CAACE,SAAS,CAACS,UAAU,CAACf,QAAQ,CAAC,EAAEI,WAAC,CAACE,SAAS,CAACS,UAAU,CAACM,SAAS,CAAC,CACtE,CACF,CACF,CAAC;EACH;EAEA,SAAS6K,kBAAkBA,CAAA,EAAG;IAC5B,MAAM;MAAE/J,WAAW;MAAEjC,IAAI;MAAEgB;IAAM,CAAC,GAAGH,UAAU;IAE/C,KAAK,MAAMoL,IAAI,IAAIjM,IAAI,CAAC2B,IAAI,CAACA,IAAI,EAAE;MACjC,IAAI,CAACzB,WAAC,CAACyD,aAAa,CAACsI,IAAI,CAAC,IAAI,CAACA,IAAI,CAACpB,QAAQ,EAAE;MAC9C,IAAI7J,KAAK,CAACkL,MAAM,CAACD,IAAI,CAAC7F,GAAG,EAAsB,IAAI,CAAC,EAAE;MAEtD,MAAMkE,EAAE,GAAGtJ,KAAK,CAACmL,gCAAgC,CAACF,IAAI,CAAC7F,GAAG,CAAC;MAC3DnE,WAAW,CAACwI,GAAG,CAACH,EAAE,CAAC7C,IAAI,EAAEwE,IAAI,CAAC7F,GAAG,CAAC;MAElC6F,IAAI,CAAC7F,GAAG,GAAGkE,EAAE;IACf;EACF;EAEA,SAAS8B,sBAAsBA,CAAA,EAAG;IAChC,MAAM;MAAEjL,SAAS;MAAEc;IAAY,CAAC,GAAGpB,UAAU;IAC7C,MAAMwL,aAAa,GAAG,EAAE;IACxB,MAAMC,WAAW,GAAG,EAAE;IAEtB,IAAIzL,UAAU,CAACQ,SAAS,EAAE;MACxB,IAAIkL,GAAG,GAAGrM,WAAC,CAACE,SAAS,CAACe,SAAS,CAAC;MAChC,IAAIN,UAAU,CAACS,aAAa,EAAE;QAC5BiL,GAAG,GAAGrM,WAAC,CAACoD,cAAc,CAACzC,UAAU,CAACL,IAAI,CAAC+C,SAAS,CAAC,iBAAiB,CAAC,EAAE,CACnEgJ,GAAG,CACJ,CAAC;QACF,IAAAC,6BAAc,EAACD,GAAG,CAAC;MACrB;MAEA,MAAME,KAAK,GACT5L,UAAU,CAACG,KAAK,CAACmL,gCAAgC,CAAChL,SAAS,CAAC;MAE9DkL,aAAa,CAAClJ,IAAI,CAACsJ,KAAK,CAAC;MACzBH,WAAW,CAACnJ,IAAI,CAACoJ,GAAG,CAAC;MAErB9J,QAAQ,CAAC;QAAEtB,SAAS,EAAEjB,WAAC,CAACE,SAAS,CAACqM,KAAK;MAAE,CAAC,CAAC;IAC7C;IAEA,KAAK,MAAM,CAAChF,IAAI,EAAEsC,KAAK,CAAC,IAAI9H,WAAW,EAAE;MACvCoK,aAAa,CAAClJ,IAAI,CAACjD,WAAC,CAACmE,UAAU,CAACoD,IAAI,CAAC,CAAC;MACtC6E,WAAW,CAACnJ,IAAI,CAAC4G,KAAK,CAAC;IACzB;IAEA,OAAO;MAAEsC,aAAa;MAAEC;IAAY,CAAC;EACvC;EAEA,SAASI,gBAAgBA,CACvBnM,IAAuB,EACvBC,IAAU,EACVC,cAAmC,EACnCC,OAAgB,EAChB;IACA+B,QAAQ,CAAC;MACP3B,MAAM,EAAEP,IAAI,CAACO,MAAM;MACnBE,KAAK,EAAET,IAAI,CAACS,KAAK;MACjBhB,IAAI,EAAEO,IAAI,CAACP,IAAI;MACfO,IAAI;MACJC,IAAI;MACJE;IACF,CAAC,CAAC;IAEF+B,QAAQ,CAAC;MACPxB,OAAO,EAAEJ,UAAU,CAACb,IAAI,CAACsK,EAAE;MAE3BxK,QAAQ,EAAEe,UAAU,CAACb,IAAI,CAACsK,EAAE,GACxBpK,WAAC,CAACmE,UAAU,CAACxD,UAAU,CAACb,IAAI,CAACsK,EAAE,CAAC7C,IAAI,CAAC,GACrC5G,UAAU,CAACG,KAAK,CAACqK,qBAAqB,CAAC,OAAO,CAAC;MACnDlK,SAAS,EAAEN,UAAU,CAACb,IAAI,CAAC2M,UAAU;MACrCtL,SAAS,EAAE,CAAC,CAACR,UAAU,CAACb,IAAI,CAAC2M,UAAU;MACvC5M,eAAe,EAAEG,WAAC,CAACgE,cAAc,CAAC,EAAE;IACtC,CAAC,CAAC;IAEFzB,QAAQ,CAAC;MACPnB,aAAa,EACXpB,WAAC,CAACqH,YAAY,CAAC1G,UAAU,CAACM,SAAS,CAAC,IACpCV,cAAc,CAAC+J,GAAG,CAAC3J,UAAU,CAACM,SAAS,CAACsG,IAAI,CAAC,IAC7C,CAAC5G,UAAU,CAACG,KAAK,CAAC4L,UAAU,CAC1B/L,UAAU,CAACM,SAAS,CAACsG,IAAI,EACT,IAClB;IACJ,CAAC,CAAC;IAEF,MAAM;MAAE3H,QAAQ;MAAEE,IAAI;MAAED;IAAgB,CAAC,GAAGc,UAAU;IAEtD4B,QAAQ,CAAC;MACPlB,SAAS,EAAE1B,gBAAgB,CAACC,QAAQ,EAAEC,eAAe,EAAEC,IAAI;IAC7D,CAAC,CAAC;IAEFgM,kBAAkB,CAAC,CAAC;IAEpB,MAAM;MAAErK;IAAK,CAAC,GAAGd,UAAU;IAC3B,MAAM;MAAEwL,aAAa;MAAEC;IAAY,CAAC,GAAGF,sBAAsB,CAAC,CAAC;IAE/D9H,SAAS,CAAC,CAAC;IAGX,IAAI,CAAC3D,WAAW,CAACkM,YAAY,EAAE;MAC7B9M,eAAe,CAAC4B,IAAI,CAACyF,OAAO,CAC1BlH,WAAC,CAAC0G,mBAAmB,CACnB1G,WAAC,CAACoD,cAAc,CAACzC,UAAU,CAACL,IAAI,CAAC+C,SAAS,CAAC,gBAAgB,CAAC,EAAE,CAC5DrD,WAAC,CAACmH,cAAc,CAAC,CAAC,EAClBnH,WAAC,CAACE,SAAS,CAACS,UAAU,CAACf,QAAQ,CAAC,CACjC,CACH,CACF,CAAC;IACH;IAEA,MAAMgN,QAAQ,GAAGvM,IAAI,CAACwM,cAAc,CAAC,CAAC;IACtC,IAAIC,eAAe,GAAGnM,UAAU,CAACI,OAAO,IAAIU,IAAI,CAACoE,MAAM,KAAK,CAAC;IAC7D,IAAIiH,eAAe,IAAI,CAACF,QAAQ,EAAE;MAChC,KAAK,MAAML,KAAK,IAAI5L,UAAU,CAACU,SAAS,CAACsC,MAAM,EAAE;QAI/C,IAAI,CAAC3D,WAAC,CAACqH,YAAY,CAACkF,KAAK,CAAC,EAAE;UAC1BO,eAAe,GAAG,KAAK;UACvB;QACF;MACF;IACF;IAEA,MAAMrB,UAAU,GAAGqB,eAAe,GAC7BrL,IAAI,CAAC,CAAC,CAAC,CAAkDA,IAAI,CAC3DgK,UAAU,GACb,EAAE;IACN,IAAI,CAACmB,QAAQ,EAAE;MACbnB,UAAU,CAACxI,IAAI,CAACjD,WAAC,CAAC+M,SAAS,CAAC/M,WAAC,CAACgN,gBAAgB,CAAC,YAAY,CAAC,CAAC,CAAC;IAChE;IAEA,IAAIF,eAAe,EAAE;MAEnB,MAAM7B,IAAI,GAAGjL,WAAC,CAACgK,YAAY,CACzBvI,IAAI,CAAC,CAAC,CACR,CAAC;MACD,OAAOd,UAAU,CAACH,OAAO,GAAGyK,IAAI,GAAG/H,iBAAiB,CAAC,CAAC+H,IAAI,CAAC,CAAC;IAC9D;IAEA,IAAIlC,SAAuB,GAAG/I,WAAC,CAACE,SAAS,CAACS,UAAU,CAACf,QAAQ,CAAC;IAC9D,IAAI,CAACe,UAAU,CAACkB,iBAAiB,IAAI,CAAClB,UAAU,CAACH,OAAO,EAAE;MACxDuI,SAAS,GAAG7F,iBAAiB,CAAC,CAAC6F,SAAS,CAAC,CAAC;IAC5C;IAEAtH,IAAI,CAACwB,IAAI,CAACjD,WAAC,CAACiI,eAAe,CAACc,SAAS,CAAC,CAAC;IACvC,MAAMjB,SAAS,GAAG9H,WAAC,CAACiN,uBAAuB,CACzCd,aAAa,EACbnM,WAAC,CAACgE,cAAc,CAACvC,IAAI,EAAEgK,UAAU,CACnC,CAAC;IACD,OAAOzL,WAAC,CAACoD,cAAc,CAAC0E,SAAS,EAAEsE,WAAW,CAAC;EACjD;EAEA,OAAOI,gBAAgB,CAACnM,IAAI,EAAEC,IAAI,EAAEC,cAAc,EAAEC,OAAO,CAAC;AAC9D"}