{"remainingRequest": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\admin\\student\\MyDormitory.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\admin\\student\\MyDormitory.vue", "mtime": 1749046559931}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\babel.config.js", "mtime": 1749034452000}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749043011899}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749043012919}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749043011899}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749043013130}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHJlcXVlc3QsIHsgYmFzZSB9IGZyb20gIi4uLy4uLy4uLy4uL3V0aWxzL2h0dHAiOwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ015RG9ybWl0b3J5JywKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgY3VycmVudERvcm1pdG9yeToge30sCiAgICAgIC8vIOW9k+WJjeWuv+iIjeS/oeaBrwogICAgICBzaG93QXBwbHlGb3JtOiBmYWxzZSwKICAgICAgLy8g5piv5ZCm5pi+56S655Sz6K+36KGo5Y2VCiAgICAgIGJ0bkxvYWRpbmc6IGZhbHNlLAogICAgICAvLyDmj5DkuqTmjInpkq7liqDovb3nirbmgIEKICAgICAgbGlzdExvYWRpbmc6IGZhbHNlLAogICAgICAvLyDliJfooajliqDovb3nirbmgIEKCiAgICAgIC8vIOeUs+ivt+ihqOWNlQogICAgICBhcHBseUZvcm06IHsKICAgICAgICBzbm86ICcnLAogICAgICAgIGRiaWQ6IG51bGwsCiAgICAgICAgLy8g5Y6f5a6/6IiN5qW8SUQKICAgICAgICBkb3JvOiAnJywKICAgICAgICAvLyDljp/lrr/oiI3nvJblj7cKICAgICAgICBkYmlkMjogbnVsbCwKICAgICAgICAvLyDmlrDlrr/oiI3mpbxJRAogICAgICAgIGRvcm8yOiAnJywKICAgICAgICAvLyDmlrDlrr/oiI3nvJblj7cKICAgICAgICBhcHBsaWNhdGlvbnJlYXNvbjogJycKICAgICAgfSwKICAgICAgLy8g6KGo5Y2V6aqM6K+B6KeE5YiZCiAgICAgIGFwcGx5UnVsZXM6IHsKICAgICAgICBkYmlkMjogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogJ+ivt+mAieaLqeaWsOWuv+iIjealvCcsCiAgICAgICAgICB0cmlnZ2VyOiAnY2hhbmdlJwogICAgICAgIH1dLAogICAgICAgIGRvcm8yOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAn6K+36YCJ5oup5paw5a6/6IiNJywKICAgICAgICAgIHRyaWdnZXI6ICdjaGFuZ2UnCiAgICAgICAgfV0sCiAgICAgICAgYXBwbGljYXRpb25yZWFzb246IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICfor7fovpPlhaXnlLPor7fljp/lm6AnLAogICAgICAgICAgdHJpZ2dlcjogJ2JsdXInCiAgICAgICAgfV0KICAgICAgfSwKICAgICAgLy8g5pWw5o2u5YiX6KGoCiAgICAgIGRvcm1idWlsZGluZ0xpc3Q6IFtdLAogICAgICAvLyDlrr/oiI3mpbzliJfooagKICAgICAgZmlsdGVyZWREb3JtaXRvcnlMaXN0OiBbXSwKICAgICAgLy8g6L+H5ruk5ZCO55qE5a6/6IiN5YiX6KGoCiAgICAgIGFwcGxpY2F0aW9uTGlzdDogW10sCiAgICAgIC8vIOeUs+ivt+WOhuWPsuWIl+ihqAoKICAgICAgLy8g5a2m55Sf5L+h5oGvCiAgICAgIHN0dWRlbnRJbmZvOiB7fQogICAgfTsKICB9LAogIGNyZWF0ZWQoKSB7CiAgICB0aGlzLmdldFN0dWRlbnRJbmZvKCk7CiAgICB0aGlzLmdldERvcm1idWlsZGluZ0xpc3QoKTsKICAgIHRoaXMuZ2V0QXBwbGljYXRpb25IaXN0b3J5KCk7CiAgfSwKICBtZXRob2RzOiB7CiAgICAvLyDojrflj5blrabnlJ/kv6Hmga/lkozlvZPliY3lrr/oiI3kv6Hmga8KICAgIGdldFN0dWRlbnRJbmZvKCkgewogICAgICBjb25zdCB1c2VyID0gSlNPTi5wYXJzZShzZXNzaW9uU3RvcmFnZS5nZXRJdGVtKCJ1c2VyIikpOwogICAgICB0aGlzLnN0dWRlbnRJbmZvID0gdXNlcjsKICAgICAgdGhpcy5hcHBseUZvcm0uc25vID0gdXNlci5zbm87CiAgICAgIGxldCB1cmwgPSBiYXNlICsgIi9zdHVkZW50L2dldD9pZD0iICsgdXNlci5zbm87CiAgICAgIHJlcXVlc3QucG9zdCh1cmwsIHt9KS50aGVuKHJlcyA9PiB7CiAgICAgICAgaWYgKHJlcy5jb2RlID09IDIwMCkgewogICAgICAgICAgdGhpcy5jdXJyZW50RG9ybWl0b3J5ID0gcmVzLnJlc2RhdGE7CiAgICAgICAgICAvLyDorr7nva7ljp/lrr/oiI3kv6Hmga8KICAgICAgICAgIHRoaXMuYXBwbHlGb3JtLmRiaWQgPSByZXMucmVzZGF0YS5kYmlkOwogICAgICAgICAgdGhpcy5hcHBseUZvcm0uZG9ybyA9IHJlcy5yZXNkYXRhLmRvcm87CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvLyDojrflj5blrr/oiI3mpbzliJfooagKICAgIGdldERvcm1idWlsZGluZ0xpc3QoKSB7CiAgICAgIGxldCB1cmwgPSBiYXNlICsgIi9kb3JtYnVpbGRpbmcvbGlzdD9jdXJyZW50UGFnZT0xJnBhZ2VTaXplPTEwMDAiOwogICAgICByZXF1ZXN0LnBvc3QodXJsLCB7fSkudGhlbihyZXMgPT4gewogICAgICAgIHRoaXMuZG9ybWJ1aWxkaW5nTGlzdCA9IHJlcy5yZXNkYXRhOwogICAgICB9KTsKICAgIH0sCiAgICAvLyDlrr/oiI3mpbzlj5jljJbml7bnmoTlpITnkIYKICAgIG9uQnVpbGRpbmdDaGFuZ2UoKSB7CiAgICAgIHRoaXMuYXBwbHlGb3JtLmRvcm8yID0gJyc7IC8vIOa4heepuuWuv+iIjemAieaLqQogICAgICB0aGlzLnVwZGF0ZURvcm1pdG9yeUxpc3QoKTsKICAgIH0sCiAgICAvLyDmm7TmlrDlrr/oiI3liJfooajvvIjmoLnmja7lrr/oiI3mpbzlkozlrabnlJ/mgKfliKvov4fmu6TvvIkKICAgIHVwZGF0ZURvcm1pdG9yeUxpc3QoKSB7CiAgICAgIGlmICghdGhpcy5hcHBseUZvcm0uZGJpZDIgfHwgIXRoaXMuc3R1ZGVudEluZm8uc2V4KSB7CiAgICAgICAgdGhpcy5maWx0ZXJlZERvcm1pdG9yeUxpc3QgPSBbXTsKICAgICAgICByZXR1cm47CiAgICAgIH0KICAgICAgbGV0IHBhcmEgPSB7CiAgICAgICAgZGJpZDogdGhpcy5hcHBseUZvcm0uZGJpZDIsCiAgICAgICAgZG9yZ2VuZGVyOiB0aGlzLnN0dWRlbnRJbmZvLnNleAogICAgICB9OwogICAgICBsZXQgdXJsID0gYmFzZSArICIvZG9ybWl0b3J5L2xpc3RCeUJ1aWxkaW5nQW5kR2VuZGVyIjsKICAgICAgcmVxdWVzdC5wb3N0KHVybCwgcGFyYSkudGhlbihyZXMgPT4gewogICAgICAgIGlmIChyZXMuY29kZSA9PSAyMDApIHsKICAgICAgICAgIC8vIOi/h+a7pOaOieW9k+WJjeWuv+iIjQogICAgICAgICAgdGhpcy5maWx0ZXJlZERvcm1pdG9yeUxpc3QgPSByZXMucmVzZGF0YS5maWx0ZXIoaXRlbSA9PiAhKGl0ZW0uZGJpZCA9PSB0aGlzLmFwcGx5Rm9ybS5kYmlkICYmIGl0ZW0uZG9ybyA9PSB0aGlzLmFwcGx5Rm9ybS5kb3JvKSk7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuZmlsdGVyZWREb3JtaXRvcnlMaXN0ID0gW107CiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgbWVzc2FnZTogcmVzLm1zZyB8fCAi6I635Y+W5a6/6IiN5YiX6KGo5aSx6LSlIiwKICAgICAgICAgICAgdHlwZTogImVycm9yIgogICAgICAgICAgfSk7CiAgICAgICAgfQogICAgICB9KS5jYXRjaCgoKSA9PiB7CiAgICAgICAgdGhpcy5maWx0ZXJlZERvcm1pdG9yeUxpc3QgPSBbXTsKICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgIG1lc3NhZ2U6ICLojrflj5blrr/oiI3liJfooajlpLHotKUiLAogICAgICAgICAgdHlwZTogImVycm9yIgogICAgICAgIH0pOwogICAgICB9KTsKICAgIH0sCiAgICAvLyDmj5DkuqTnlLPor7cKICAgIHN1Ym1pdEFwcGxpY2F0aW9uKCkgewogICAgICB0aGlzLiRyZWZzLmFwcGx5Rm9ybVJlZi52YWxpZGF0ZSh2YWxpZCA9PiB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICAvLyDmo4Dmn6XmmK/lkKbpgInmi6nkuobkuI3lkIznmoTlrr/oiI0KICAgICAgICAgIGlmICh0aGlzLmFwcGx5Rm9ybS5kYmlkID09IHRoaXMuYXBwbHlGb3JtLmRiaWQyICYmIHRoaXMuYXBwbHlGb3JtLmRvcm8gPT0gdGhpcy5hcHBseUZvcm0uZG9ybzIpIHsKICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgbWVzc2FnZTogIuaWsOWuv+iIjeS4jeiDveS4juW9k+WJjeWuv+iIjeebuOWQjCIsCiAgICAgICAgICAgICAgdHlwZTogIndhcm5pbmciCiAgICAgICAgICAgIH0pOwogICAgICAgICAgICByZXR1cm47CiAgICAgICAgICB9CiAgICAgICAgICB0aGlzLmJ0bkxvYWRpbmcgPSB0cnVlOwogICAgICAgICAgbGV0IHVybCA9IGJhc2UgKyAiL2Rvcm1pdG9yeWNoYW5nZS9hcHBseSI7CiAgICAgICAgICByZXF1ZXN0LnBvc3QodXJsLCB0aGlzLmFwcGx5Rm9ybSkudGhlbihyZXMgPT4gewogICAgICAgICAgICBpZiAocmVzLmNvZGUgPT0gMjAwKSB7CiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgICBtZXNzYWdlOiAi55Sz6K+35o+Q5Lqk5oiQ5Yqf77yM6K+3562J5b6F5a6h5qC4IiwKICAgICAgICAgICAgICAgIHR5cGU6ICJzdWNjZXNzIgogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgIHRoaXMucmVzZXRBcHBseUZvcm0oKTsKICAgICAgICAgICAgICB0aGlzLnNob3dBcHBseUZvcm0gPSBmYWxzZTsKICAgICAgICAgICAgICB0aGlzLmdldEFwcGxpY2F0aW9uSGlzdG9yeSgpOyAvLyDliLfmlrDnlLPor7fljoblj7IKICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5tc2cgfHwgIueUs+ivt+aPkOS6pOWksei0pSIsCiAgICAgICAgICAgICAgICB0eXBlOiAiZXJyb3IiCiAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIH0KICAgICAgICAgICAgdGhpcy5idG5Mb2FkaW5nID0gZmFsc2U7CiAgICAgICAgICB9KS5jYXRjaCgoKSA9PiB7CiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICAgIG1lc3NhZ2U6ICLnlLPor7fmj5DkuqTlpLHotKUiLAogICAgICAgICAgICAgIHR5cGU6ICJlcnJvciIKICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIHRoaXMuYnRuTG9hZGluZyA9IGZhbHNlOwogICAgICAgICAgfSk7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvLyDph43nva7nlLPor7fooajljZUKICAgIHJlc2V0QXBwbHlGb3JtKCkgewogICAgICB0aGlzLiRyZWZzLmFwcGx5Rm9ybVJlZi5yZXNldEZpZWxkcygpOwogICAgICB0aGlzLmFwcGx5Rm9ybS5kYmlkMiA9IG51bGw7CiAgICAgIHRoaXMuYXBwbHlGb3JtLmRvcm8yID0gJyc7CiAgICAgIHRoaXMuYXBwbHlGb3JtLmFwcGxpY2F0aW9ucmVhc29uID0gJyc7CiAgICAgIHRoaXMuZmlsdGVyZWREb3JtaXRvcnlMaXN0ID0gW107CiAgICB9LAogICAgLy8g6I635Y+W55Sz6K+35Y6G5Y+yCiAgICBnZXRBcHBsaWNhdGlvbkhpc3RvcnkoKSB7CiAgICAgIHRoaXMubGlzdExvYWRpbmcgPSB0cnVlOwogICAgICBsZXQgcGFyYSA9IHsKICAgICAgICBzbm86IHRoaXMuc3R1ZGVudEluZm8uc25vCiAgICAgIH07CiAgICAgIGxldCB1cmwgPSBiYXNlICsgIi9kb3JtaXRvcnljaGFuZ2UvbGlzdD9jdXJyZW50UGFnZT0xJnBhZ2VTaXplPTEwMCI7CiAgICAgIHJlcXVlc3QucG9zdCh1cmwsIHBhcmEpLnRoZW4ocmVzID0+IHsKICAgICAgICBpZiAocmVzLmNvZGUgPT0gMjAwKSB7CiAgICAgICAgICB0aGlzLmFwcGxpY2F0aW9uTGlzdCA9IHJlcy5yZXNkYXRhOwogICAgICAgIH0KICAgICAgICB0aGlzLmxpc3RMb2FkaW5nID0gZmFsc2U7CiAgICAgIH0pLmNhdGNoKCgpID0+IHsKICAgICAgICB0aGlzLmxpc3RMb2FkaW5nID0gZmFsc2U7CiAgICAgIH0pOwogICAgfSwKICAgIC8vIOiOt+WPlueKtuaAgeagh+etvuexu+WeiwogICAgZ2V0U3RhdHVzVHlwZShzdGF0dXMpIHsKICAgICAgc3dpdGNoIChzdGF0dXMpIHsKICAgICAgICBjYXNlICflvoXlrqHmoLgnOgogICAgICAgICAgcmV0dXJuICd3YXJuaW5nJzsKICAgICAgICBjYXNlICflrqHmoLjpgJrov4cnOgogICAgICAgICAgcmV0dXJuICdzdWNjZXNzJzsKICAgICAgICBjYXNlICflrqHmoLjkuI3pgJrov4cnOgogICAgICAgICAgcmV0dXJuICdkYW5nZXInOwogICAgICAgIGRlZmF1bHQ6CiAgICAgICAgICByZXR1cm4gJ2luZm8nOwogICAgICB9CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["request", "base", "name", "data", "currentDormitory", "showApplyForm", "btnLoading", "listLoading", "applyForm", "sno", "dbid", "doro", "dbid2", "doro2", "<PERSON><PERSON><PERSON>", "applyRules", "required", "message", "trigger", "dormbuildingList", "filteredDormitoryList", "applicationList", "studentInfo", "created", "getStudentInfo", "getDormbuildingList", "getApplicationHistory", "methods", "user", "JSON", "parse", "sessionStorage", "getItem", "url", "post", "then", "res", "code", "resdata", "onBuildingChange", "updateDormitoryList", "sex", "para", "<PERSON><PERSON><PERSON>", "filter", "item", "$message", "msg", "type", "catch", "submitApplication", "$refs", "applyFormRef", "validate", "valid", "resetApplyForm", "resetFields", "getStatusType", "status"], "sources": ["I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\admin\\student\\MyDormitory.vue"], "sourcesContent": ["<template>\n  <div>\n    <!-- 当前宿舍信息 -->\n    <el-card class=\"box-card\" style=\"margin-bottom: 20px;\">\n      <div slot=\"header\" class=\"clearfix\">\n        <span style=\"font-size: 18px; font-weight: bold;\">我的宿舍信息</span>\n      </div>\n      <div v-if=\"currentDormitory.doro\">\n        <el-row :gutter=\"20\">\n          <el-col :span=\"24\">\n            <p><strong>宿舍楼：</strong>{{ currentDormitory.dbname }}</p>\n            <p><strong>宿舍编号：</strong>{{ currentDormitory.doro }}</p>\n          </el-col>\n       \n        </el-row>\n      </div>\n      <div v-else>\n        <p style=\"color: #999;\">暂未分配宿舍</p>\n      </div>\n    </el-card>\n\n    <!-- 申请更换宿舍 -->\n    <el-card class=\"box-card\" style=\"margin-bottom: 20px;\">\n      <div slot=\"header\" class=\"clearfix\">\n        <span style=\"font-size: 18px; font-weight: bold;\">申请更换宿舍</span>\n        <el-button style=\"float: right; padding: 3px 0\" type=\"text\" @click=\"showApplyForm = !showApplyForm\">\n          {{ showApplyForm ? '收起' : '展开' }}\n        </el-button>\n      </div>\n      <div v-show=\"showApplyForm\">\n        <el-form :model=\"applyForm\" :rules=\"applyRules\" ref=\"applyFormRef\" label-width=\"120px\">\n          <el-form-item label=\"新宿舍楼\" prop=\"dbid2\">\n            <el-select v-model=\"applyForm.dbid2\" placeholder=\"请选择宿舍楼\" @change=\"onBuildingChange\" style=\"width: 300px;\">\n              <el-option v-for=\"item in dormbuildingList\" :key=\"item.dbid\" :label=\"item.dbname\" :value=\"item.dbid\"></el-option>\n            </el-select>\n          </el-form-item>\n          <el-form-item label=\"新宿舍\" prop=\"doro2\">\n            <el-select v-model=\"applyForm.doro2\" placeholder=\"请先选择宿舍楼\" :disabled=\"!applyForm.dbid2\" style=\"width: 300px;\">\n              <el-option v-for=\"item in filteredDormitoryList\" :key=\"item.doro\" :label=\"item.doro\" :value=\"item.doro\"></el-option>\n            </el-select>\n          </el-form-item>\n          <el-form-item label=\"申请原因\" prop=\"applicationreason\">\n            <el-input type=\"textarea\" v-model=\"applyForm.applicationreason\" placeholder=\"请输入申请更换宿舍的原因\" :rows=\"4\" style=\"width: 500px;\"></el-input>\n          </el-form-item>\n          <el-form-item>\n            <el-button type=\"primary\" @click=\"submitApplication\" :loading=\"btnLoading\">提交申请</el-button>\n            <el-button @click=\"resetApplyForm\">重置</el-button>\n          </el-form-item>\n        </el-form>\n      </div>\n    </el-card>\n\n    <!-- 申请历史 -->\n    <el-card class=\"box-card\">\n      <div slot=\"header\" class=\"clearfix\">\n        <span style=\"font-size: 18px; font-weight: bold;\">申请历史</span>\n        <el-button style=\"float: right; padding: 3px 0\" type=\"text\" @click=\"getApplicationHistory\">刷新</el-button>\n      </div>\n      <el-table :data=\"applicationList\" style=\"width: 100%\" v-loading=\"listLoading\">\n        <el-table-column prop=\"submissiontime\" label=\"申请时间\" width=\"150\"></el-table-column>\n        <el-table-column label=\"原宿舍\" width=\"150\">\n          <template slot-scope=\"scope\">\n            {{ scope.row.dbname }} - {{ scope.row.doro }}\n          </template>\n        </el-table-column>\n        <el-table-column label=\"目标宿舍\" width=\"150\">\n          <template slot-scope=\"scope\">\n            {{ scope.row.dbname2 }} - {{ scope.row.doro2 }}\n          </template>\n        </el-table-column>\n        <el-table-column prop=\"applicationreason\" label=\"申请原因\" show-overflow-tooltip></el-table-column>\n        <el-table-column prop=\"reviewstatus\" label=\"审核状态\" width=\"100\">\n          <template slot-scope=\"scope\">\n            <el-tag :type=\"getStatusType(scope.row.reviewstatus)\">{{ scope.row.reviewstatus }}</el-tag>\n          </template>\n        </el-table-column>\n        <el-table-column prop=\"reviewresponse\" label=\"审核回复\" show-overflow-tooltip></el-table-column>\n      </el-table>\n    </el-card>\n  </div>\n</template>\n\n<script>\nimport request, { base } from \"../../../../utils/http\";\n\nexport default {\n  name: 'MyDormitory',\n  data() {\n    return {\n      currentDormitory: {}, // 当前宿舍信息\n      showApplyForm: false, // 是否显示申请表单\n      btnLoading: false, // 提交按钮加载状态\n      listLoading: false, // 列表加载状态\n      \n      // 申请表单\n      applyForm: {\n        sno: '',\n        dbid: null, // 原宿舍楼ID\n        doro: '', // 原宿舍编号\n        dbid2: null, // 新宿舍楼ID\n        doro2: '', // 新宿舍编号\n        applicationreason: ''\n      },\n      \n      // 表单验证规则\n      applyRules: {\n        dbid2: [{ required: true, message: '请选择新宿舍楼', trigger: 'change' }],\n        doro2: [{ required: true, message: '请选择新宿舍', trigger: 'change' }],\n        applicationreason: [{ required: true, message: '请输入申请原因', trigger: 'blur' }]\n      },\n      \n      // 数据列表\n      dormbuildingList: [], // 宿舍楼列表\n      filteredDormitoryList: [], // 过滤后的宿舍列表\n      applicationList: [], // 申请历史列表\n      \n      // 学生信息\n      studentInfo: {}\n    };\n  },\n  \n  created() {\n    this.getStudentInfo();\n    this.getDormbuildingList();\n    this.getApplicationHistory();\n  },\n  \n  methods: {\n    // 获取学生信息和当前宿舍信息\n    getStudentInfo() {\n      const user = JSON.parse(sessionStorage.getItem(\"user\"));\n      this.studentInfo = user;\n      this.applyForm.sno = user.sno;\n      \n      let url = base + \"/student/get?id=\" + user.sno;\n      request.post(url, {}).then((res) => {\n        if (res.code == 200) {\n          this.currentDormitory = res.resdata;\n          // 设置原宿舍信息\n          this.applyForm.dbid = res.resdata.dbid;\n          this.applyForm.doro = res.resdata.doro;\n        }\n      });\n    },\n    \n    // 获取宿舍楼列表\n    getDormbuildingList() {\n      let url = base + \"/dormbuilding/list?currentPage=1&pageSize=1000\";\n      request.post(url, {}).then((res) => {\n        this.dormbuildingList = res.resdata;\n      });\n    },\n    \n    // 宿舍楼变化时的处理\n    onBuildingChange() {\n      this.applyForm.doro2 = ''; // 清空宿舍选择\n      this.updateDormitoryList();\n    },\n    \n    // 更新宿舍列表（根据宿舍楼和学生性别过滤）\n    updateDormitoryList() {\n      if (!this.applyForm.dbid2 || !this.studentInfo.sex) {\n        this.filteredDormitoryList = [];\n        return;\n      }\n\n      let para = {\n        dbid: this.applyForm.dbid2,\n        dorgender: this.studentInfo.sex\n      };\n      let url = base + \"/dormitory/listByBuildingAndGender\";\n      request.post(url, para).then((res) => {\n        if (res.code == 200) {\n          // 过滤掉当前宿舍\n          this.filteredDormitoryList = res.resdata.filter(item => \n            !(item.dbid == this.applyForm.dbid && item.doro == this.applyForm.doro)\n          );\n        } else {\n          this.filteredDormitoryList = [];\n          this.$message({\n            message: res.msg || \"获取宿舍列表失败\",\n            type: \"error\"\n          });\n        }\n      }).catch(() => {\n        this.filteredDormitoryList = [];\n        this.$message({\n          message: \"获取宿舍列表失败\",\n          type: \"error\"\n        });\n      });\n    },\n    \n    // 提交申请\n    submitApplication() {\n      this.$refs.applyFormRef.validate((valid) => {\n        if (valid) {\n          // 检查是否选择了不同的宿舍\n          if (this.applyForm.dbid == this.applyForm.dbid2 && this.applyForm.doro == this.applyForm.doro2) {\n            this.$message({\n              message: \"新宿舍不能与当前宿舍相同\",\n              type: \"warning\"\n            });\n            return;\n          }\n          \n          this.btnLoading = true;\n          let url = base + \"/dormitorychange/apply\";\n          request.post(url, this.applyForm).then((res) => {\n            if (res.code == 200) {\n              this.$message({\n                message: \"申请提交成功，请等待审核\",\n                type: \"success\"\n              });\n              this.resetApplyForm();\n              this.showApplyForm = false;\n              this.getApplicationHistory(); // 刷新申请历史\n            } else {\n              this.$message({\n                message: res.msg || \"申请提交失败\",\n                type: \"error\"\n              });\n            }\n            this.btnLoading = false;\n          }).catch(() => {\n            this.$message({\n              message: \"申请提交失败\",\n              type: \"error\"\n            });\n            this.btnLoading = false;\n          });\n        }\n      });\n    },\n    \n    // 重置申请表单\n    resetApplyForm() {\n      this.$refs.applyFormRef.resetFields();\n      this.applyForm.dbid2 = null;\n      this.applyForm.doro2 = '';\n      this.applyForm.applicationreason = '';\n      this.filteredDormitoryList = [];\n    },\n    \n    // 获取申请历史\n    getApplicationHistory() {\n      this.listLoading = true;\n      let para = {\n        sno: this.studentInfo.sno\n      };\n      let url = base + \"/dormitorychange/list?currentPage=1&pageSize=100\";\n      request.post(url, para).then((res) => {\n        if (res.code == 200) {\n          this.applicationList = res.resdata;\n        }\n        this.listLoading = false;\n      }).catch(() => {\n        this.listLoading = false;\n      });\n    },\n    \n    // 获取状态标签类型\n    getStatusType(status) {\n      switch (status) {\n        case '待审核':\n          return 'warning';\n        case '审核通过':\n          return 'success';\n        case '审核不通过':\n          return 'danger';\n        default:\n          return 'info';\n      }\n    }\n  }\n};\n</script>\n\n<style scoped>\n.box-card {\n  margin-bottom: 20px;\n}\n\n.clearfix:before,\n.clearfix:after {\n  display: table;\n  content: \"\";\n}\n\n.clearfix:after {\n  clear: both;\n}\n</style>\n"], "mappings": "AAmFA,OAAOA,OAAO,IAAIC,IAAG,QAAS,wBAAwB;AAEtD,eAAe;EACbC,IAAI,EAAE,aAAa;EACnBC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,gBAAgB,EAAE,CAAC,CAAC;MAAE;MACtBC,aAAa,EAAE,KAAK;MAAE;MACtBC,UAAU,EAAE,KAAK;MAAE;MACnBC,WAAW,EAAE,KAAK;MAAE;;MAEpB;MACAC,SAAS,EAAE;QACTC,GAAG,EAAE,EAAE;QACPC,IAAI,EAAE,IAAI;QAAE;QACZC,IAAI,EAAE,EAAE;QAAE;QACVC,KAAK,EAAE,IAAI;QAAE;QACbC,KAAK,EAAE,EAAE;QAAE;QACXC,iBAAiB,EAAE;MACrB,CAAC;MAED;MACAC,UAAU,EAAE;QACVH,KAAK,EAAE,CAAC;UAAEI,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAS,CAAC,CAAC;QAClEL,KAAK,EAAE,CAAC;UAAEG,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,QAAQ;UAAEC,OAAO,EAAE;QAAS,CAAC,CAAC;QACjEJ,iBAAiB,EAAE,CAAC;UAAEE,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAO,CAAC;MAC7E,CAAC;MAED;MACAC,gBAAgB,EAAE,EAAE;MAAE;MACtBC,qBAAqB,EAAE,EAAE;MAAE;MAC3BC,eAAe,EAAE,EAAE;MAAE;;MAErB;MACAC,WAAW,EAAE,CAAC;IAChB,CAAC;EACH,CAAC;EAEDC,OAAOA,CAAA,EAAG;IACR,IAAI,CAACC,cAAc,CAAC,CAAC;IACrB,IAAI,CAACC,mBAAmB,CAAC,CAAC;IAC1B,IAAI,CAACC,qBAAqB,CAAC,CAAC;EAC9B,CAAC;EAEDC,OAAO,EAAE;IACP;IACAH,cAAcA,CAAA,EAAG;MACf,MAAMI,IAAG,GAAIC,IAAI,CAACC,KAAK,CAACC,cAAc,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC;MACvD,IAAI,CAACV,WAAU,GAAIM,IAAI;MACvB,IAAI,CAACpB,SAAS,CAACC,GAAE,GAAImB,IAAI,CAACnB,GAAG;MAE7B,IAAIwB,GAAE,GAAIhC,IAAG,GAAI,kBAAiB,GAAI2B,IAAI,CAACnB,GAAG;MAC9CT,OAAO,CAACkC,IAAI,CAACD,GAAG,EAAE,CAAC,CAAC,CAAC,CAACE,IAAI,CAAEC,GAAG,IAAK;QAClC,IAAIA,GAAG,CAACC,IAAG,IAAK,GAAG,EAAE;UACnB,IAAI,CAACjC,gBAAe,GAAIgC,GAAG,CAACE,OAAO;UACnC;UACA,IAAI,CAAC9B,SAAS,CAACE,IAAG,GAAI0B,GAAG,CAACE,OAAO,CAAC5B,IAAI;UACtC,IAAI,CAACF,SAAS,CAACG,IAAG,GAAIyB,GAAG,CAACE,OAAO,CAAC3B,IAAI;QACxC;MACF,CAAC,CAAC;IACJ,CAAC;IAED;IACAc,mBAAmBA,CAAA,EAAG;MACpB,IAAIQ,GAAE,GAAIhC,IAAG,GAAI,gDAAgD;MACjED,OAAO,CAACkC,IAAI,CAACD,GAAG,EAAE,CAAC,CAAC,CAAC,CAACE,IAAI,CAAEC,GAAG,IAAK;QAClC,IAAI,CAACjB,gBAAe,GAAIiB,GAAG,CAACE,OAAO;MACrC,CAAC,CAAC;IACJ,CAAC;IAED;IACAC,gBAAgBA,CAAA,EAAG;MACjB,IAAI,CAAC/B,SAAS,CAACK,KAAI,GAAI,EAAE,EAAE;MAC3B,IAAI,CAAC2B,mBAAmB,CAAC,CAAC;IAC5B,CAAC;IAED;IACAA,mBAAmBA,CAAA,EAAG;MACpB,IAAI,CAAC,IAAI,CAAChC,SAAS,CAACI,KAAI,IAAK,CAAC,IAAI,CAACU,WAAW,CAACmB,GAAG,EAAE;QAClD,IAAI,CAACrB,qBAAoB,GAAI,EAAE;QAC/B;MACF;MAEA,IAAIsB,IAAG,GAAI;QACThC,IAAI,EAAE,IAAI,CAACF,SAAS,CAACI,KAAK;QAC1B+B,SAAS,EAAE,IAAI,CAACrB,WAAW,CAACmB;MAC9B,CAAC;MACD,IAAIR,GAAE,GAAIhC,IAAG,GAAI,oCAAoC;MACrDD,OAAO,CAACkC,IAAI,CAACD,GAAG,EAAES,IAAI,CAAC,CAACP,IAAI,CAAEC,GAAG,IAAK;QACpC,IAAIA,GAAG,CAACC,IAAG,IAAK,GAAG,EAAE;UACnB;UACA,IAAI,CAACjB,qBAAoB,GAAIgB,GAAG,CAACE,OAAO,CAACM,MAAM,CAACC,IAAG,IACjD,EAAEA,IAAI,CAACnC,IAAG,IAAK,IAAI,CAACF,SAAS,CAACE,IAAG,IAAKmC,IAAI,CAAClC,IAAG,IAAK,IAAI,CAACH,SAAS,CAACG,IAAI,CACxE,CAAC;QACH,OAAO;UACL,IAAI,CAACS,qBAAoB,GAAI,EAAE;UAC/B,IAAI,CAAC0B,QAAQ,CAAC;YACZ7B,OAAO,EAAEmB,GAAG,CAACW,GAAE,IAAK,UAAU;YAC9BC,IAAI,EAAE;UACR,CAAC,CAAC;QACJ;MACF,CAAC,CAAC,CAACC,KAAK,CAAC,MAAM;QACb,IAAI,CAAC7B,qBAAoB,GAAI,EAAE;QAC/B,IAAI,CAAC0B,QAAQ,CAAC;UACZ7B,OAAO,EAAE,UAAU;UACnB+B,IAAI,EAAE;QACR,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC;IAED;IACAE,iBAAiBA,CAAA,EAAG;MAClB,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,QAAQ,CAAEC,KAAK,IAAK;QAC1C,IAAIA,KAAK,EAAE;UACT;UACA,IAAI,IAAI,CAAC9C,SAAS,CAACE,IAAG,IAAK,IAAI,CAACF,SAAS,CAACI,KAAI,IAAK,IAAI,CAACJ,SAAS,CAACG,IAAG,IAAK,IAAI,CAACH,SAAS,CAACK,KAAK,EAAE;YAC9F,IAAI,CAACiC,QAAQ,CAAC;cACZ7B,OAAO,EAAE,cAAc;cACvB+B,IAAI,EAAE;YACR,CAAC,CAAC;YACF;UACF;UAEA,IAAI,CAAC1C,UAAS,GAAI,IAAI;UACtB,IAAI2B,GAAE,GAAIhC,IAAG,GAAI,wBAAwB;UACzCD,OAAO,CAACkC,IAAI,CAACD,GAAG,EAAE,IAAI,CAACzB,SAAS,CAAC,CAAC2B,IAAI,CAAEC,GAAG,IAAK;YAC9C,IAAIA,GAAG,CAACC,IAAG,IAAK,GAAG,EAAE;cACnB,IAAI,CAACS,QAAQ,CAAC;gBACZ7B,OAAO,EAAE,cAAc;gBACvB+B,IAAI,EAAE;cACR,CAAC,CAAC;cACF,IAAI,CAACO,cAAc,CAAC,CAAC;cACrB,IAAI,CAAClD,aAAY,GAAI,KAAK;cAC1B,IAAI,CAACqB,qBAAqB,CAAC,CAAC,EAAE;YAChC,OAAO;cACL,IAAI,CAACoB,QAAQ,CAAC;gBACZ7B,OAAO,EAAEmB,GAAG,CAACW,GAAE,IAAK,QAAQ;gBAC5BC,IAAI,EAAE;cACR,CAAC,CAAC;YACJ;YACA,IAAI,CAAC1C,UAAS,GAAI,KAAK;UACzB,CAAC,CAAC,CAAC2C,KAAK,CAAC,MAAM;YACb,IAAI,CAACH,QAAQ,CAAC;cACZ7B,OAAO,EAAE,QAAQ;cACjB+B,IAAI,EAAE;YACR,CAAC,CAAC;YACF,IAAI,CAAC1C,UAAS,GAAI,KAAK;UACzB,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;IACJ,CAAC;IAED;IACAiD,cAAcA,CAAA,EAAG;MACf,IAAI,CAACJ,KAAK,CAACC,YAAY,CAACI,WAAW,CAAC,CAAC;MACrC,IAAI,CAAChD,SAAS,CAACI,KAAI,GAAI,IAAI;MAC3B,IAAI,CAACJ,SAAS,CAACK,KAAI,GAAI,EAAE;MACzB,IAAI,CAACL,SAAS,CAACM,iBAAgB,GAAI,EAAE;MACrC,IAAI,CAACM,qBAAoB,GAAI,EAAE;IACjC,CAAC;IAED;IACAM,qBAAqBA,CAAA,EAAG;MACtB,IAAI,CAACnB,WAAU,GAAI,IAAI;MACvB,IAAImC,IAAG,GAAI;QACTjC,GAAG,EAAE,IAAI,CAACa,WAAW,CAACb;MACxB,CAAC;MACD,IAAIwB,GAAE,GAAIhC,IAAG,GAAI,kDAAkD;MACnED,OAAO,CAACkC,IAAI,CAACD,GAAG,EAAES,IAAI,CAAC,CAACP,IAAI,CAAEC,GAAG,IAAK;QACpC,IAAIA,GAAG,CAACC,IAAG,IAAK,GAAG,EAAE;UACnB,IAAI,CAAChB,eAAc,GAAIe,GAAG,CAACE,OAAO;QACpC;QACA,IAAI,CAAC/B,WAAU,GAAI,KAAK;MAC1B,CAAC,CAAC,CAAC0C,KAAK,CAAC,MAAM;QACb,IAAI,CAAC1C,WAAU,GAAI,KAAK;MAC1B,CAAC,CAAC;IACJ,CAAC;IAED;IACAkD,aAAaA,CAACC,MAAM,EAAE;MACpB,QAAQA,MAAM;QACZ,KAAK,KAAK;UACR,OAAO,SAAS;QAClB,KAAK,MAAM;UACT,OAAO,SAAS;QAClB,KAAK,OAAO;UACV,OAAO,QAAQ;QACjB;UACE,OAAO,MAAM;MACjB;IACF;EACF;AACF,CAAC"}]}