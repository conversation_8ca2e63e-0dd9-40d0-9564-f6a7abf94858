{"remainingRequest": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\Login.vue?vue&type=style&index=0&id=26084dc2&scoped=true&lang=css", "dependencies": [{"path": "I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\Login.vue", "mtime": 1749045164779}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749043012394}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\stylePostLoader.js", "mtime": 1749043013744}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749043012910}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749043011899}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749043013130}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CkBpbXBvcnQgdXJsKC4uL2Fzc2V0cy9jc3MvaHRzdHlsZS5jc3MpOwoK"}, {"version": 3, "sources": ["I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\Login.vue"], "names": [], "mappings": ";AA8VA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "I:/product4/B7839DormManager/DormManager-web/src/views/Login.vue", "sourceRoot": "", "sourcesContent": ["<template>\n\n  <div class=\"auth-wrapper aut-bg-img\">\n        <div class=\"auth-content\"  style=\"width: 450px;\">\n             <form action=\"#\">\n            <div class=\"card\" style=\"padding-top: 30px; padding-bottom: 30px; \">\n                <div class=\"card-body text-center\">\n                    <div class=\"mb-4\">\n                        <i class=\"feather icon-unlock auth-icon\"></i>\n                    </div>\n                    <h3 class=\"mb-4\">宿舍管理系统</h3>\n                    <div class=\"input-group mb-3\">\n                        <input type=\"text\" class=\"form-control\" placeholder=\"用户名\" v-model=\"loginModel.username\">\n                    </div>\n                    <div class=\"input-group mb-4\">\n                        <input type=\"password\" class=\"form-control\" placeholder=\"密码\" v-model=\"loginModel.password\">\n                    </div>\n                       <div class=\"form-group text-left\">\n                        <div class=\"checkbox checkbox-fill d-inline\">\n                          <el-radio label=\"管理员\" v-model=\"loginModel.radio\">管理员</el-radio>\n                                   <el-radio label=\"学生\" v-model=\"loginModel.radio\">学生</el-radio>\n      <el-radio label=\"维修员\" v-model=\"loginModel.radio\">维修员</el-radio>\n      <el-radio label=\"宿管阿姨\" v-model=\"loginModel.radio\">宿管阿姨</el-radio>\n\n\n                        </div>\n                    </div>\n\n\n            <el-button class=\"btn btn-primary shadow-2 mb-4\"  @click=\"login\">登录</el-button>\n                     <a href=\"#\"  @click=\"toreg\"> 新学生注册 </a>\n <el-dialog title=\"学生注册\"  v-model=\"formVisible\" width=\"40%\" :close-on-click-modal=\"false\">\n <el-form :model=\"formData\" label-width=\"20%\" ref=\"formDataRef\" :rules=\"rules\"  align=\"left\">\n<el-form-item label=\"学号\" prop=\"sno\">\n<el-input v-model=\"formData.sno\" placeholder=\"学号\"  style=\"width:50%;\" ></el-input>\n</el-form-item>\n<el-form-item label=\"登录密码\" prop=\"password\">\n<el-input type=\"password\" v-model=\"formData.password\" placeholder=\"登录密码\"  style=\"width:50%;\" ></el-input>\n</el-form-item>\n<el-form-item label=\"确认密码\" prop=\"password2\">\n<el-input type=\"password\" v-model=\"formData.password2\" placeholder=\"确认密码\"  style=\"width:50%;\" ></el-input>\n</el-form-item>\n<el-form-item label=\"姓名\" prop=\"stname\">\n<el-input v-model=\"formData.stname\" placeholder=\"姓名\"  style=\"width:50%;\" ></el-input>\n</el-form-item>\n<el-form-item label=\"性别\" prop=\"sex\">\n<el-radio-group v-model=\"formData.sex\" @change=\"onGenderChange\">\n<el-radio label=\"男\">\n男\n</el-radio>\n<el-radio label=\"女\">\n女\n</el-radio>\n</el-radio-group>\n</el-form-item>\n<el-form-item label=\"手机号码\" prop=\"phone\">\n<el-input v-model=\"formData.phone\" placeholder=\"手机号码\"  style=\"width:50%;\" ></el-input>\n</el-form-item>\n<el-form-item label=\"宿舍楼\" prop=\"dbid\">\n<el-select v-model=\"formData.dbid\" placeholder=\"请选择\"  size=\"small\" @change=\"onBuildingChange\">\n<el-option v-for=\"item in dormbuildingList\" :key=\"item.dbid\" :label=\"item.dbname\" :value=\"item.dbid\"></el-option>\n</el-select>\n</el-form-item>\n<el-form-item label=\"宿舍\" prop=\"doro\">\n<el-select v-model=\"formData.doro\" placeholder=\"请先选择宿舍楼和性别\"  size=\"small\" :disabled=\"!formData.dbid || !formData.sex\">\n<el-option v-for=\"item in filteredDormitoryList\" :key=\"item.doro\" :label=\"item.doro\" :value=\"item.doro\"></el-option>\n</el-select>\n</el-form-item>\n<el-form-item label=\"专业\" prop=\"specialty\">\n<el-input v-model=\"formData.specialty\" placeholder=\"专业\"  style=\"width:50%;\" ></el-input>\n</el-form-item>\n<el-form-item label=\"班级\" prop=\"clsname\">\n<el-input v-model=\"formData.clsname\" placeholder=\"班级\"  style=\"width:50%;\" ></el-input>\n</el-form-item>\n<el-form-item>\n<el-button type=\"primary\" @click=\"reg\" :loading=\"btnLoading\" >注 册</el-button>\n</el-form-item>\n</el-form>\n</el-dialog>\n\n\n                </div>\n            </div>\n            </form>\n\n        </div>\n    </div>\n\n\n\n</template>\n<script>\nimport request, { base } from \"../../utils/http\";\nexport default {\n  name: \"Login\",\n  data() {\n    return {\n      year: new Date().getFullYear(),\n      loginModel: {\n        username: \"\",\n        password: \"\",\n        radio: \"管理员\",\n      },\n      loginModel2: {},\n     add: true, //是否是添加\n      formVisible: false,\n      formData:{},\n      dormbuildingList: [], //宿舍楼列表\n      dormitoryList: [], //所有宿舍列表\n      filteredDormitoryList: [], //过滤后的宿舍列表\n\n      addrules: {\n          sno: [{ required: true, message: '请输入学号', trigger: 'blur' },],\n          password: [{ required: true, message: '请输入登录密码', trigger: 'blur' }],\n          password2: [{ required: true, message: '请输入登录密码', trigger: 'blur' },{ validator: (rule, value, callback) => { if (value !== this.formData.password) { callback(new Error('两次输入密码不一致!')); } else { callback(); } }, trigger: 'blur' },],\n          stname: [{ required: true, message: '请输入姓名', trigger: 'blur' },],\n          sex: [{ required: true, message: '请输入性别', trigger: 'blur' },],\n          phone: [{ required: true, message: '请输入手机号码', trigger: 'blur' },        { pattern: /^1[3456789]\\d{9}$/, message: '手机号码格式不正确', trigger: 'blur' },],\n          doro: [{ required: true, message: '请选择宿舍', trigger: 'onchange' }],\n          specialty: [{ required: true, message: '请输入专业', trigger: 'blur' },],\n          clsname: [{ required: true, message: '请输入班级', trigger: 'blur' },],\n      },\n\n\n      btnLoading: false, //按钮是否在加载中\n\n\n    };\n  },\n  mounted() {},\n  created() {\n\n  },\n  methods: {\n    login() {\n      let that = this;\n\n      if (that.loginModel.username == \"\") {\n        that.$message({\n          message: \"请输入账号\",\n          type: \"warning\",\n        });\n        return;\n      }\n      if (that.loginModel.password == \"\") {\n        that.$message({\n          message: \"请输入密码\",\n          type: \"warning\",\n        });\n        return;\n      }\n\n      this.loading = true;\n     var role = that.loginModel.radio; //获取身份\nif (role == '管理员') {\n      let url = base + \"/admin/login\";\n      this.loginModel2.aname = this.loginModel.username;\n      this.loginModel2.loginpassword = this.loginModel.password;\n      request.post(url, this.loginModel2).then((res) => {\n        this.loading = false;\n        if (res.code == 200) {\n          console.log(JSON.stringify(res.resdata));\n          sessionStorage.setItem(\"user\", JSON.stringify(res.resdata));\n          sessionStorage.setItem(\"userLname\", res.resdata.aname);\n          sessionStorage.setItem(\"role\", \"管理员\");\n          this.$router.push(\"/main\");\n        } else {\n          this.$message({\n            message: res.msg,\n            type: \"error\",\n          });\n        }\n      });\n          }\nelse if (role == '宿管阿姨') {\n      let url = base + \"/hostess/login\";\n      this.loginModel2.hno = this.loginModel.username;\n      this.loginModel2.password = this.loginModel.password;\n      request.post(url, this.loginModel2).then((res) => {\n        this.loading = false;\n        if (res.code == 200) {\n          console.log(JSON.stringify(res.resdata));\n          sessionStorage.setItem(\"user\", JSON.stringify(res.resdata));\n          sessionStorage.setItem(\"userLname\", res.resdata.hno);\n          sessionStorage.setItem(\"role\", \"宿管阿姨\");\n          this.$router.push(\"/main\");\n        } else {\n          this.$message({\n            message: res.msg,\n            type: \"error\",\n          });\n        }\n      });\n          }\nelse if (role == '维修员') {\n      let url = base + \"/repairmen/login\";\n      this.loginModel2.rno = this.loginModel.username;\n      this.loginModel2.password = this.loginModel.password;\n      request.post(url, this.loginModel2).then((res) => {\n        this.loading = false;\n        if (res.code == 200) {\n          console.log(JSON.stringify(res.resdata));\n          sessionStorage.setItem(\"user\", JSON.stringify(res.resdata));\n          sessionStorage.setItem(\"userLname\", res.resdata.rno);\n          sessionStorage.setItem(\"role\", \"维修员\");\n          this.$router.push(\"/main\");\n        } else {\n          this.$message({\n            message: res.msg,\n            type: \"error\",\n          });\n        }\n      });\n          }\nelse if (role == '学生') {\n      let url = base + \"/student/login\";\n      this.loginModel2.sno = this.loginModel.username;\n      this.loginModel2.password = this.loginModel.password;\n      request.post(url, this.loginModel2).then((res) => {\n        this.loading = false;\n        if (res.code == 200) {\n          console.log(JSON.stringify(res.resdata));\n          sessionStorage.setItem(\"user\", JSON.stringify(res.resdata));\n          sessionStorage.setItem(\"userLname\", res.resdata.sno);\n          sessionStorage.setItem(\"role\", \"学生\");\n          this.$router.push(\"/main\");\n        } else {\n          this.$message({\n            message: res.msg,\n            type: \"error\",\n          });\n        }\n      });\n          }\n\n\n    },\n\n    toreg() {\n    this.formVisible = true;\n    this.add = true;\n    this.isClear = true;\n    this.rules = this.addrules;\n    this.getdormbuildingList(); // 加载宿舍楼列表\n    this.$nextTick(() => {\n        this.$refs[\"formDataRef\"].resetFields();\n    });\n},\n\n//注册\nreg() {\n    //表单验证\n    this.$refs[\"formDataRef\"].validate((valid) => {\n\n        if (valid) {\n            let url = base + \"/student/add\"; //请求地址\n            this.btnLoading = true; //按钮加载状态\n            request.post(url, this.formData).then((res) => { //请求接口\n                if (res.code == 200) {\n                    this.$message({\n                        message: \"恭喜您，注册成功，请登录！\",\n                        type: \"success\",\n                        offset: 320,\n                    });\n                    this.formVisible = false; //关闭表单\n                    this.btnLoading = false; //按钮加载状态\n                    this.$refs[\"formDataRef\"].resetFields(); //重置表单\n                    this.$refs[\"formDataRef\"].clearValidate();\n                }\n                else if (res.code == 201) {\n                    this.$message({\n                        message: res.msg,\n                        type: \"error\",\n                        offset: 320,\n                    });\n                }\n                else {\n                    this.$message({\n                        message: \"服务器错误\",\n                        type: \"error\",\n                        offset: 320,\n                    });\n                }\n            });\n        }\n    });\n},\n// 获取宿舍楼列表\ngetdormbuildingList() {\n  let para = {};\n  let url = base + \"/dormbuilding/list?currentPage=1&pageSize=1000\";\n  request.post(url, para).then((res) => {\n    this.dormbuildingList = res.resdata;\n  });\n},\n\n// 宿舍楼变化时的处理\nonBuildingChange() {\n  this.formData.doro = ''; // 清空宿舍选择\n  this.updateDormitoryList();\n},\n\n// 性别变化时的处理\nonGenderChange() {\n  this.formData.doro = ''; // 清空宿舍选择\n  this.updateDormitoryList();\n},\n\n// 更新宿舍列表\nupdateDormitoryList() {\n  if (!this.formData.dbid || !this.formData.sex) {\n    this.filteredDormitoryList = [];\n    return;\n  }\n\n  let para = {\n    dbid: this.formData.dbid,\n    dorgender: this.formData.sex\n  };\n  let url = base + \"/dormitory/listByBuildingAndGender\";\n  request.post(url, para).then((res) => {\n    if (res.code == 200) {\n      this.filteredDormitoryList = res.resdata;\n    } else {\n      this.filteredDormitoryList = [];\n      this.$message({\n        message: res.msg || \"获取宿舍列表失败\",\n        type: \"error\",\n        offset: 320,\n      });\n    }\n  }).catch(() => {\n    this.filteredDormitoryList = [];\n    this.$message({\n      message: \"获取宿舍列表失败\",\n      type: \"error\",\n      offset: 320,\n    });\n  });\n},\n\n\n\n\n\n  },\n};\n</script>\n\n<style scoped>\n@import url(../assets/css/htstyle.css);\n\n</style>\n\n\n"]}]}