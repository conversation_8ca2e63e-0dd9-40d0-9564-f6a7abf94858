package com.controller;

import com.model.ReportData;
import com.response.Response;
import com.service.ReportDataService;
import com.util.PageBean;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

@RestController
@RequestMapping("/api/ReportData")
public class ReportDataController {

	@Resource
	private ReportDataService reportDataService;

	//查询报表
	@RequestMapping(value="/queryReport")
	@CrossOrigin
	public Response<List<ReportData>> queryReport(@RequestBody ReportData reportdata,HttpServletRequest req) throws Exception {

		reportdata.setSql("select date_format(ltime, '%Y-%m-%d') as name, count(*) as num \n" +
		"from leaves \n" +
		"where flag = '审核通过' \n" +
		"group by date_format(ltime, '%Y-%m-%d') order by name desc");
		List<ReportData> getReportData = reportDataService.report(reportdata);

		req.setAttribute("ReportDataList", getReportData);

		return Response.success(getReportData);
	}

	//报修类型统计饼图
	@RequestMapping(value="/repairTypeStats")
	@CrossOrigin
	public Response<List<ReportData>> repairTypeStats(@RequestBody ReportData reportdata,HttpServletRequest req) throws Exception {

		reportdata.setSql("select rt.typename as name, count(ro.no) as num \n" +
		"from repairtype rt \n" +
		"left join repairorders ro on rt.typeid = ro.typeid \n" +
		"group by rt.typeid, rt.typename \n" +
		"order by num desc");
		List<ReportData> getReportData = reportDataService.report(reportdata);

		req.setAttribute("RepairTypeStatsList", getReportData);

		return Response.success(getReportData);
	}

	//维修员维修统计柱状图
	@RequestMapping(value="/repairmenStats")
	@CrossOrigin
	public Response<List<ReportData>> repairmenStats(@RequestBody ReportData reportdata,HttpServletRequest req) throws Exception {

		reportdata.setSql("select rm.rnname as name, count(ro.no) as num \n" +
		"from repairmen rm \n" +
		"left join repairorders ro on rm.rno = ro.rno \n" +
		"group by rm.rno, rm.rnname \n" +
		"order by num desc");
		List<ReportData> getReportData = reportDataService.report(reportdata);

		req.setAttribute("RepairmenStatsList", getReportData);

		return Response.success(getReportData);
	}

	//宿舍评分统计柱状图
	@RequestMapping(value="/dormScoreStats")
	@CrossOrigin
	public Response<List<ReportData>> dormScoreStats(@RequestBody ReportData reportdata,HttpServletRequest req) throws Exception {

		reportdata.setSql("select \n" +
		"  case \n" +
		"    when score >= 90 then '优秀(90-100分)' \n" +
		"    when score >= 80 then '良好(80-89分)' \n" +
		"    when score >= 70 then '中等(70-79分)' \n" +
		"    when score >= 60 then '及格(60-69分)' \n" +
		"    else '不及格(60分以下)' \n" +
		"  end as name, \n" +
		"  count(*) as num \n" +
		"from dormitoryscore \n" +
		"where score is not null \n" +
		"group by \n" +
		"  case \n" +
		"    when score >= 90 then '优秀(90-100分)' \n" +
		"    when score >= 80 then '良好(80-89分)' \n" +
		"    when score >= 70 then '中等(70-79分)' \n" +
		"    when score >= 60 then '及格(60-69分)' \n" +
		"    else '不及格(60分以下)' \n" +
		"  end \n" +
		"order by \n" +
		"  case \n" +
		"    when name = '优秀(90-100分)' then 1 \n" +
		"    when name = '良好(80-89分)' then 2 \n" +
		"    when name = '中等(70-79分)' then 3 \n" +
		"    when name = '及格(60-69分)' then 4 \n" +
		"    else 5 \n" +
		"  end");
		List<ReportData> getReportData = reportDataService.report(reportdata);

		req.setAttribute("DormScoreStatsList", getReportData);

		return Response.success(getReportData);
	}

}

