{"version": 3, "names": ["_helper<PERSON>lugin<PERSON><PERSON>s", "require", "_helperModuleImports", "_core", "_helpers", "_index", "_polyfills", "supportsStaticESM", "caller", "_default", "declare", "api", "options", "dirname", "assertVersion", "helpers", "useRuntimeHelpers", "useESModules", "version", "runtimeVersion", "absoluteRuntime", "Error", "DUAL_MODE_RUNTIME", "supportsCJSDefault", "hasMinVersion", "has", "obj", "key", "Object", "prototype", "hasOwnProperty", "call", "esModules", "HEADER_HELPERS", "name", "inherits", "createBasePolyfillsPlugin", "pre", "file", "modulePath", "set", "_modulePath", "_file$get", "getRuntimePath", "get", "availableHelper", "t", "arrowFunctionExpression", "identifier", "isInteropHelper", "indexOf", "blockHoist", "isModule", "path", "undefined", "helpersDir", "node", "sourceType", "help<PERSON><PERSON><PERSON>", "resolveFSPath", "addDefaultImport", "cache", "Map", "source", "nameHint", "is<PERSON><PERSON>per", "cache<PERSON>ey", "cached", "cloneNode", "addDefault", "importedInterop", "exports", "default"], "sources": ["../src/index.ts"], "sourcesContent": ["import { declare } from \"@babel/helper-plugin-utils\";\nimport { addDefault, isModule } from \"@babel/helper-module-imports\";\nimport { types as t, type CallerMetadata } from \"@babel/core\";\n\nimport { hasMinVersion } from \"./helpers.ts\";\nimport getRuntimePath, { resolveFSPath } from \"./get-runtime-path/index.ts\";\nimport { createBasePolyfillsPlugin } from \"./polyfills.ts\";\n\nfunction supportsStaticESM(caller: CallerMetadata | undefined) {\n  // @ts-expect-error TS does not narrow down optional chaining\n  return !!caller?.supportsStaticESM;\n}\n\nexport interface Options {\n  absoluteRuntime?: boolean;\n  corejs?: string | number | { version: string | number; proposals?: boolean };\n  helpers?: boolean;\n  regenerator?: boolean;\n  useESModules?: boolean | \"auto\";\n  version?: string;\n}\n\nexport default declare((api, options: Options, dirname) => {\n  api.assertVersion(7);\n\n  const {\n    helpers: useRuntimeHelpers = true,\n    useESModules = false,\n    version: runtimeVersion = \"7.0.0-beta.0\",\n    absoluteRuntime = false,\n  } = options;\n\n  if (typeof useRuntimeHelpers !== \"boolean\") {\n    throw new Error(\"The 'helpers' option must be undefined, or a boolean.\");\n  }\n\n  if (typeof useESModules !== \"boolean\" && useESModules !== \"auto\") {\n    throw new Error(\n      \"The 'useESModules' option must be undefined, or a boolean, or 'auto'.\",\n    );\n  }\n\n  if (\n    typeof absoluteRuntime !== \"boolean\" &&\n    typeof absoluteRuntime !== \"string\"\n  ) {\n    throw new Error(\n      \"The 'absoluteRuntime' option must be undefined, a boolean, or a string.\",\n    );\n  }\n\n  if (typeof runtimeVersion !== \"string\") {\n    throw new Error(`The 'version' option must be a version string.`);\n  }\n\n  if (!process.env.BABEL_8_BREAKING) {\n    // In recent @babel/runtime versions, we can use require(\"helper\").default\n    // instead of require(\"helper\") so that it has the same interface as the\n    // ESM helper, and bundlers can better exchange one format for the other.\n    const DUAL_MODE_RUNTIME = \"7.13.0\";\n    // eslint-disable-next-line no-var\n    var supportsCJSDefault = hasMinVersion(DUAL_MODE_RUNTIME, runtimeVersion);\n  }\n\n  function has(obj: {}, key: string) {\n    return Object.prototype.hasOwnProperty.call(obj, key);\n  }\n\n  if (has(options, \"useBuiltIns\")) {\n    // @ts-expect-error deprecated options\n    if (options[\"useBuiltIns\"]) {\n      throw new Error(\n        \"The 'useBuiltIns' option has been removed. The @babel/runtime \" +\n          \"module now uses builtins by default.\",\n      );\n    } else {\n      throw new Error(\n        \"The 'useBuiltIns' option has been removed. Use the 'corejs'\" +\n          \"option to polyfill with `core-js` via @babel/runtime.\",\n      );\n    }\n  }\n\n  if (has(options, \"polyfill\")) {\n    // @ts-expect-error deprecated options\n    if (options[\"polyfill\"] === false) {\n      throw new Error(\n        \"The 'polyfill' option has been removed. The @babel/runtime \" +\n          \"module now skips polyfilling by default.\",\n      );\n    } else {\n      throw new Error(\n        \"The 'polyfill' option has been removed. Use the 'corejs'\" +\n          \"option to polyfill with `core-js` via @babel/runtime.\",\n      );\n    }\n  }\n\n  if (has(options, \"moduleName\")) {\n    throw new Error(\n      \"The 'moduleName' option has been removed. @babel/transform-runtime \" +\n        \"no longer supports arbitrary runtimes. If you were using this to \" +\n        \"set an absolute path for Babel's standard runtimes, please use the \" +\n        \"'absoluteRuntime' option.\",\n    );\n  }\n\n  const esModules =\n    useESModules === \"auto\" ? api.caller(supportsStaticESM) : useESModules;\n\n  const HEADER_HELPERS = [\"interopRequireWildcard\", \"interopRequireDefault\"];\n\n  return {\n    name: \"transform-runtime\",\n\n    inherits: createBasePolyfillsPlugin(\n      options,\n      runtimeVersion,\n      absoluteRuntime,\n    ),\n\n    pre(file) {\n      if (!useRuntimeHelpers) return;\n\n      let modulePath: string;\n\n      file.set(\"helperGenerator\", (name: string) => {\n        modulePath ??= getRuntimePath(\n          file.get(\"runtimeHelpersModuleName\") ?? \"@babel/runtime\",\n          dirname,\n          absoluteRuntime,\n        );\n\n        // If the helper didn't exist yet at the version given, we bail\n        // out and let Babel either insert it directly, or throw an error\n        // so that plugins can handle that case properly.\n        if (!process.env.BABEL_8_BREAKING) {\n          if (!file.availableHelper?.(name, runtimeVersion)) {\n            if (name === \"regeneratorRuntime\") {\n              // For regeneratorRuntime, we can fallback to the old behavior of\n              // relying on the regeneratorRuntime global. If the 'regenerator'\n              // option is not disabled, babel-plugin-polyfill-regenerator will\n              // then replace it with a @babel/helpers/regenerator import.\n              //\n              // We must wrap it in a function, because built-in Babel helpers\n              // are functions.\n              return t.arrowFunctionExpression(\n                [],\n                t.identifier(\"regeneratorRuntime\"),\n              );\n            }\n            return;\n          }\n        } else {\n          if (!file.availableHelper(name, runtimeVersion)) return;\n        }\n\n        const isInteropHelper = HEADER_HELPERS.indexOf(name) !== -1;\n\n        // Explicitly set the CommonJS interop helpers to their reserve\n        // blockHoist of 4 so they are guaranteed to exist\n        // when other things used them to import.\n        const blockHoist =\n          isInteropHelper && !isModule(file.path) ? 4 : undefined;\n\n        const helpersDir =\n          esModules && file.path.node.sourceType === \"module\"\n            ? \"helpers/esm\"\n            : \"helpers\";\n\n        let helperPath = `${modulePath}/${helpersDir}/${name}`;\n        if (absoluteRuntime) helperPath = resolveFSPath(helperPath);\n\n        return addDefaultImport(helperPath, name, blockHoist, true);\n      });\n\n      const cache = new Map();\n\n      function addDefaultImport(\n        source: string,\n        nameHint: string,\n        blockHoist: number,\n        isHelper = false,\n      ) {\n        // If something on the page adds a helper when the file is an ES6\n        // file, we can't reused the cached helper name after things have been\n        // transformed because it has almost certainly been renamed.\n        const cacheKey = isModule(file.path);\n        const key = `${source}:${nameHint}:${cacheKey || \"\"}`;\n\n        let cached = cache.get(key);\n        if (cached) {\n          cached = t.cloneNode(cached);\n        } else {\n          cached = addDefault(file.path, source, {\n            importedInterop: (\n              process.env.BABEL_8_BREAKING\n                ? isHelper\n                : isHelper && supportsCJSDefault\n            )\n              ? \"compiled\"\n              : \"uncompiled\",\n            nameHint,\n            blockHoist,\n          });\n\n          cache.set(key, cached);\n        }\n        return cached;\n      }\n    },\n  };\n});\n"], "mappings": ";;;;;;AAAA,IAAAA,kBAAA,GAAAC,OAAA;AACA,IAAAC,oBAAA,GAAAD,OAAA;AACA,IAAAE,KAAA,GAAAF,OAAA;AAEA,IAAAG,QAAA,GAAAH,OAAA;AACA,IAAAI,MAAA,GAAAJ,OAAA;AACA,IAAAK,UAAA,GAAAL,OAAA;AAEA,SAASM,iBAAiBA,CAACC,MAAkC,EAAE;EAE7D,OAAO,CAAC,EAACA,MAAM,YAANA,MAAM,CAAED,iBAAiB;AACpC;AAAC,IAAAE,QAAA,GAWc,IAAAC,0BAAO,EAAC,CAACC,GAAG,EAAEC,OAAgB,EAAEC,OAAO,KAAK;EACzDF,GAAG,CAACG,aAAa,CAAC,CAAC,CAAC;EAEpB,MAAM;IACJC,OAAO,EAAEC,iBAAiB,GAAG,IAAI;IACjCC,YAAY,GAAG,KAAK;IACpBC,OAAO,EAAEC,cAAc,GAAG,cAAc;IACxCC,eAAe,GAAG;EACpB,CAAC,GAAGR,OAAO;EAEX,IAAI,OAAOI,iBAAiB,KAAK,SAAS,EAAE;IAC1C,MAAM,IAAIK,KAAK,CAAC,uDAAuD,CAAC;EAC1E;EAEA,IAAI,OAAOJ,YAAY,KAAK,SAAS,IAAIA,YAAY,KAAK,MAAM,EAAE;IAChE,MAAM,IAAII,KAAK,CACb,uEACF,CAAC;EACH;EAEA,IACE,OAAOD,eAAe,KAAK,SAAS,IACpC,OAAOA,eAAe,KAAK,QAAQ,EACnC;IACA,MAAM,IAAIC,KAAK,CACb,yEACF,CAAC;EACH;EAEA,IAAI,OAAOF,cAAc,KAAK,QAAQ,EAAE;IACtC,MAAM,IAAIE,KAAK,CAAE,gDAA+C,CAAC;EACnE;EAEmC;IAIjC,MAAMC,iBAAiB,GAAG,QAAQ;IAElC,IAAIC,kBAAkB,GAAG,IAAAC,sBAAa,EAACF,iBAAiB,EAAEH,cAAc,CAAC;EAC3E;EAEA,SAASM,GAAGA,CAACC,GAAO,EAAEC,GAAW,EAAE;IACjC,OAAOC,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACL,GAAG,EAAEC,GAAG,CAAC;EACvD;EAEA,IAAIF,GAAG,CAACb,OAAO,EAAE,aAAa,CAAC,EAAE;IAE/B,IAAIA,OAAO,CAAC,aAAa,CAAC,EAAE;MAC1B,MAAM,IAAIS,KAAK,CACb,gEAAgE,GAC9D,sCACJ,CAAC;IACH,CAAC,MAAM;MACL,MAAM,IAAIA,KAAK,CACb,6DAA6D,GAC3D,uDACJ,CAAC;IACH;EACF;EAEA,IAAII,GAAG,CAACb,OAAO,EAAE,UAAU,CAAC,EAAE;IAE5B,IAAIA,OAAO,CAAC,UAAU,CAAC,KAAK,KAAK,EAAE;MACjC,MAAM,IAAIS,KAAK,CACb,6DAA6D,GAC3D,0CACJ,CAAC;IACH,CAAC,MAAM;MACL,MAAM,IAAIA,KAAK,CACb,0DAA0D,GACxD,uDACJ,CAAC;IACH;EACF;EAEA,IAAII,GAAG,CAACb,OAAO,EAAE,YAAY,CAAC,EAAE;IAC9B,MAAM,IAAIS,KAAK,CACb,qEAAqE,GACnE,mEAAmE,GACnE,qEAAqE,GACrE,2BACJ,CAAC;EACH;EAEA,MAAMW,SAAS,GACbf,YAAY,KAAK,MAAM,GAAGN,GAAG,CAACH,MAAM,CAACD,iBAAiB,CAAC,GAAGU,YAAY;EAExE,MAAMgB,cAAc,GAAG,CAAC,wBAAwB,EAAE,uBAAuB,CAAC;EAE1E,OAAO;IACLC,IAAI,EAAE,mBAAmB;IAEzBC,QAAQ,EAAE,IAAAC,oCAAyB,EACjCxB,OAAO,EACPO,cAAc,EACdC,eACF,CAAC;IAEDiB,GAAGA,CAACC,IAAI,EAAE;MACR,IAAI,CAACtB,iBAAiB,EAAE;MAExB,IAAIuB,UAAkB;MAEtBD,IAAI,CAACE,GAAG,CAAC,iBAAiB,EAAGN,IAAY,IAAK;QAAA,IAAAO,WAAA,EAAAC,SAAA;QAC5C,CAAAD,WAAA,GAAAF,UAAU,YAAAE,WAAA,GAAVF,UAAU,GAAK,IAAAI,cAAc,GAAAD,SAAA,GAC3BJ,IAAI,CAACM,GAAG,CAAC,0BAA0B,CAAC,YAAAF,SAAA,GAAI,gBAAgB,EACxD7B,OAAO,EACPO,eACF,CAAC;QAKkC;UACjC,IAAI,EAACkB,IAAI,CAACO,eAAe,YAApBP,IAAI,CAACO,eAAe,CAAGX,IAAI,EAAEf,cAAc,CAAC,GAAE;YACjD,IAAIe,IAAI,KAAK,oBAAoB,EAAE;cAQjC,OAAOY,WAAC,CAACC,uBAAuB,CAC9B,EAAE,EACFD,WAAC,CAACE,UAAU,CAAC,oBAAoB,CACnC,CAAC;YACH;YACA;UACF;QACF;QAIA,MAAMC,eAAe,GAAGhB,cAAc,CAACiB,OAAO,CAAChB,IAAI,CAAC,KAAK,CAAC,CAAC;QAK3D,MAAMiB,UAAU,GACdF,eAAe,IAAI,CAAC,IAAAG,6BAAQ,EAACd,IAAI,CAACe,IAAI,CAAC,GAAG,CAAC,GAAGC,SAAS;QAEzD,MAAMC,UAAU,GACdvB,SAAS,IAAIM,IAAI,CAACe,IAAI,CAACG,IAAI,CAACC,UAAU,KAAK,QAAQ,GAC/C,aAAa,GACb,SAAS;QAEf,IAAIC,UAAU,GAAI,GAAEnB,UAAW,IAAGgB,UAAW,IAAGrB,IAAK,EAAC;QACtD,IAAId,eAAe,EAAEsC,UAAU,GAAG,IAAAC,oBAAa,EAACD,UAAU,CAAC;QAE3D,OAAOE,gBAAgB,CAACF,UAAU,EAAExB,IAAI,EAAEiB,UAAU,EAAE,IAAI,CAAC;MAC7D,CAAC,CAAC;MAEF,MAAMU,KAAK,GAAG,IAAIC,GAAG,CAAC,CAAC;MAEvB,SAASF,gBAAgBA,CACvBG,MAAc,EACdC,QAAgB,EAChBb,UAAkB,EAClBc,QAAQ,GAAG,KAAK,EAChB;QAIA,MAAMC,QAAQ,GAAG,IAAAd,6BAAQ,EAACd,IAAI,CAACe,IAAI,CAAC;QACpC,MAAM1B,GAAG,GAAI,GAAEoC,MAAO,IAAGC,QAAS,IAAGE,QAAQ,IAAI,EAAG,EAAC;QAErD,IAAIC,MAAM,GAAGN,KAAK,CAACjB,GAAG,CAACjB,GAAG,CAAC;QAC3B,IAAIwC,MAAM,EAAE;UACVA,MAAM,GAAGrB,WAAC,CAACsB,SAAS,CAACD,MAAM,CAAC;QAC9B,CAAC,MAAM;UACLA,MAAM,GAAG,IAAAE,+BAAU,EAAC/B,IAAI,CAACe,IAAI,EAAEU,MAAM,EAAE;YACrCO,eAAe,EAGTL,QAAQ,IAAI1C,kBAAkB,GAEhC,UAAU,GACV,YAAY;YAChByC,QAAQ;YACRb;UACF,CAAC,CAAC;UAEFU,KAAK,CAACrB,GAAG,CAACb,GAAG,EAAEwC,MAAM,CAAC;QACxB;QACA,OAAOA,MAAM;MACf;IACF;EACF,CAAC;AACH,CAAC,CAAC;AAAAI,OAAA,CAAAC,OAAA,GAAA/D,QAAA"}