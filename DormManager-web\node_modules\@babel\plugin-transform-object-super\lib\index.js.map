{"version": 3, "names": ["_helper<PERSON>lugin<PERSON><PERSON>s", "require", "_helperReplaceSupers", "_core", "replacePropertySuper", "path", "getObjectRef", "file", "replaceSupers", "ReplaceSupers", "methodPath", "replace", "_default", "declare", "api", "assertVersion", "name", "visitor", "ObjectExpression", "state", "objectRef", "scope", "generateUidIdentifier", "get", "for<PERSON>ach", "prop<PERSON>ath", "isMethod", "push", "id", "t", "cloneNode", "replaceWith", "assignmentExpression", "node", "exports", "default"], "sources": ["../src/index.ts"], "sourcesContent": ["import { declare } from \"@babel/helper-plugin-utils\";\nimport ReplaceSupers from \"@babel/helper-replace-supers\";\nimport { types as t, type File } from \"@babel/core\";\nimport type { NodePath } from \"@babel/traverse\";\n\nfunction replacePropertySuper(\n  path: NodePath<t.ObjectMethod>,\n  getObjectRef: () => t.Identifier,\n  file: File,\n) {\n  // @ts-expect-error todo(flow->ts):\n  const replaceSupers = new ReplaceSupers({\n    getObjectRef: getObjectRef,\n    methodPath: path,\n    file: file,\n  });\n\n  replaceSupers.replace();\n}\n\nexport default declare(api => {\n  api.assertVersion(7);\n\n  return {\n    name: \"transform-object-super\",\n\n    visitor: {\n      ObjectExpression(path, state) {\n        let objectRef: t.Identifier;\n        const getObjectRef = () =>\n          (objectRef = objectRef || path.scope.generateUidIdentifier(\"obj\"));\n\n        path.get(\"properties\").forEach(propPath => {\n          if (!propPath.isMethod()) return;\n\n          replacePropertySuper(propPath, getObjectRef, state.file);\n        });\n\n        if (objectRef) {\n          path.scope.push({ id: t.cloneNode(objectRef) });\n          path.replaceWith(\n            t.assignmentExpression(\"=\", t.cloneNode(objectRef), path.node),\n          );\n        }\n      },\n    },\n  };\n});\n"], "mappings": ";;;;;;AAAA,IAAAA,kBAAA,GAAAC,OAAA;AACA,IAAAC,oBAAA,GAAAD,OAAA;AACA,IAAAE,KAAA,GAAAF,OAAA;AAGA,SAASG,oBAAoBA,CAC3BC,IAA8B,EAC9BC,YAAgC,EAChCC,IAAU,EACV;EAEA,MAAMC,aAAa,GAAG,IAAIC,4BAAa,CAAC;IACtCH,YAAY,EAAEA,YAAY;IAC1BI,UAAU,EAAEL,IAAI;IAChBE,IAAI,EAAEA;EACR,CAAC,CAAC;EAEFC,aAAa,CAACG,OAAO,CAAC,CAAC;AACzB;AAAC,IAAAC,QAAA,GAEc,IAAAC,0BAAO,EAACC,GAAG,IAAI;EAC5BA,GAAG,CAACC,aAAa,CAAC,CAAC,CAAC;EAEpB,OAAO;IACLC,IAAI,EAAE,wBAAwB;IAE9BC,OAAO,EAAE;MACPC,gBAAgBA,CAACb,IAAI,EAAEc,KAAK,EAAE;QAC5B,IAAIC,SAAuB;QAC3B,MAAMd,YAAY,GAAGA,CAAA,KAClBc,SAAS,GAAGA,SAAS,IAAIf,IAAI,CAACgB,KAAK,CAACC,qBAAqB,CAAC,KAAK,CAAE;QAEpEjB,IAAI,CAACkB,GAAG,CAAC,YAAY,CAAC,CAACC,OAAO,CAACC,QAAQ,IAAI;UACzC,IAAI,CAACA,QAAQ,CAACC,QAAQ,CAAC,CAAC,EAAE;UAE1BtB,oBAAoB,CAACqB,QAAQ,EAAEnB,YAAY,EAAEa,KAAK,CAACZ,IAAI,CAAC;QAC1D,CAAC,CAAC;QAEF,IAAIa,SAAS,EAAE;UACbf,IAAI,CAACgB,KAAK,CAACM,IAAI,CAAC;YAAEC,EAAE,EAAEC,WAAC,CAACC,SAAS,CAACV,SAAS;UAAE,CAAC,CAAC;UAC/Cf,IAAI,CAAC0B,WAAW,CACdF,WAAC,CAACG,oBAAoB,CAAC,GAAG,EAAEH,WAAC,CAACC,SAAS,CAACV,SAAS,CAAC,EAAEf,IAAI,CAAC4B,IAAI,CAC/D,CAAC;QACH;MACF;IACF;EACF,CAAC;AACH,CAAC,CAAC;AAAAC,OAAA,CAAAC,OAAA,GAAAvB,QAAA"}