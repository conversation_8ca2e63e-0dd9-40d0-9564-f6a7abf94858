{"version": 3, "names": ["_semver", "require", "_corejs2BuiltIns", "_coreJsCompat", "_pluginsCompatData", "_moduleTransformations", "_options", "_helperValidatorOption", "corejs2DefaultWebIncludes", "v", "OptionValidator", "allPluginsList", "Object", "keys", "pluginsList", "modulePlugins", "moduleTransformations", "map", "m", "getValidIncludesAndExcludes", "type", "corejs", "Array", "from", "Set", "corejs2Polyfills", "corejs3Polyfills", "flatMap", "array", "fn", "prototype", "concat", "apply", "normalizePluginName", "plugin", "replace", "exports", "expandIncludesAndExcludes", "filterList", "length", "filterableItems", "invalidFilters", "selected<PERSON><PERSON><PERSON>", "filter", "re", "RegExp", "e", "push", "items", "item", "test", "invariant", "join", "checkDuplicateIncludeExcludes", "include", "exclude", "duplicates", "opt", "indexOf", "normalizeTargets", "targets", "isArray", "browsers", "assign", "validateModulesOption", "modulesOpt", "ModulesOption", "auto", "toString", "false", "validateUseBuiltInsOption", "builtInsOpt", "UseBuiltInsOption", "normalizeCoreJSOption", "useBuiltIns", "proposals", "rawVersion", "undefined", "console", "warn", "version", "Boolean", "semver", "coerce", "String", "major", "RangeError", "normalizeOptions", "opts", "validateTopLevelOptions", "TopLevelOptions", "bugfixes", "validateBooleanOption", "config<PERSON><PERSON>", "validateStringOption", "process", "cwd", "debug", "forceAllTransforms", "ignoreBrowserslistConfig", "loose", "modules", "shippedProposals", "spec", "browserslistEnv"], "sources": ["../src/normalize-options.ts"], "sourcesContent": ["import semver, { type SemVer } from \"semver\";\nimport corejs2Polyfills from \"@babel/compat-data/corejs2-built-ins\";\n// @ts-expect-error Fixme: TS can not infer types from ../data/core-js-compat.js\n// but we can't import core-js-compat/data.json because JSON imports do\n// not work on Node 14\nimport corejs3Polyfills from \"../data/core-js-compat.js\";\nimport { plugins as pluginsList } from \"./plugins-compat-data.ts\";\nimport moduleTransformations from \"./module-transformations.ts\";\nimport {\n  TopLevelOptions,\n  ModulesOption,\n  UseBuiltInsOption,\n} from \"./options.ts\";\nimport { OptionValidator } from \"@babel/helper-validator-option\";\n\nconst corejs2DefaultWebIncludes = [\n  \"web.timers\",\n  \"web.immediate\",\n  \"web.dom.iterable\",\n];\n\nimport type {\n  BuiltInsOption,\n  CorejsOption,\n  ModuleOption,\n  Options,\n  PluginListOption,\n} from \"./types.ts\";\n\nconst v = new OptionValidator(PACKAGE_JSON.name);\n\nconst allPluginsList = Object.keys(pluginsList);\n\n// NOTE: Since module plugins are handled separately compared to other plugins (via the \"modules\" option) it\n// should only be possible to exclude and not include module plugins, otherwise it's possible that preset-env\n// will add a module plugin twice.\nconst modulePlugins = [\n  \"transform-dynamic-import\",\n  ...Object.keys(moduleTransformations).map(m => moduleTransformations[m]),\n];\n\nconst getValidIncludesAndExcludes = (\n  type: \"include\" | \"exclude\",\n  corejs: number | false,\n) =>\n  Array.from(\n    new Set([\n      ...allPluginsList,\n      ...(type === \"exclude\" ? modulePlugins : []),\n      ...(corejs\n        ? corejs == 2\n          ? [...Object.keys(corejs2Polyfills), ...corejs2DefaultWebIncludes]\n          : Object.keys(corejs3Polyfills)\n        : []),\n    ]),\n  );\n\nfunction flatMap<T, U>(array: Array<T>, fn: (item: T) => Array<U>): Array<U> {\n  return Array.prototype.concat.apply([], array.map(fn));\n}\n\nexport const normalizePluginName = (plugin: string) =>\n  plugin.replace(/^(@babel\\/|babel-)(plugin-)?/, \"\");\n\nconst expandIncludesAndExcludes = (\n  filterList: PluginListOption = [],\n  type: \"include\" | \"exclude\",\n  corejs: number | false,\n) => {\n  if (filterList.length === 0) return [];\n\n  const filterableItems = getValidIncludesAndExcludes(type, corejs);\n\n  const invalidFilters: PluginListOption = [];\n  const selectedPlugins = flatMap(filterList, filter => {\n    let re: RegExp;\n    if (typeof filter === \"string\") {\n      try {\n        re = new RegExp(`^${normalizePluginName(filter)}$`);\n      } catch (e) {\n        invalidFilters.push(filter);\n        return [];\n      }\n    } else {\n      re = filter;\n    }\n    const items = filterableItems.filter(item => {\n      return process.env.BABEL_8_BREAKING\n        ? re.test(item)\n        : re.test(item) ||\n            // For backwards compatibility, we also support matching against the\n            // proposal- name.\n            re.test(item.replace(/^transform-/, \"proposal-\"));\n    });\n    if (items.length === 0) invalidFilters.push(filter);\n    return items;\n  });\n\n  v.invariant(\n    invalidFilters.length === 0,\n    `The plugins/built-ins '${invalidFilters.join(\n      \", \",\n    )}' passed to the '${type}' option are not\n    valid. Please check data/[plugin-features|built-in-features].js in babel-preset-env`,\n  );\n\n  return selectedPlugins;\n};\n\nexport const checkDuplicateIncludeExcludes = (\n  include: Array<string> = [],\n  exclude: Array<string> = [],\n) => {\n  const duplicates = include.filter(opt => exclude.indexOf(opt) >= 0);\n\n  v.invariant(\n    duplicates.length === 0,\n    `The plugins/built-ins '${duplicates.join(\n      \", \",\n    )}' were found in both the \"include\" and\n    \"exclude\" options.`,\n  );\n};\n\nconst normalizeTargets = (\n  targets: string | string[] | Options[\"targets\"],\n): Options[\"targets\"] => {\n  // TODO: Allow to use only query or strings as a targets from next breaking change.\n  if (typeof targets === \"string\" || Array.isArray(targets)) {\n    return { browsers: targets };\n  }\n  return { ...targets };\n};\n\nexport const validateModulesOption = (\n  modulesOpt: ModuleOption = ModulesOption.auto,\n) => {\n  v.invariant(\n    // @ts-expect-error we have provided fallback for undefined keys\n    ModulesOption[modulesOpt.toString()] || modulesOpt === ModulesOption.false,\n    `The 'modules' option must be one of \\n` +\n      ` - 'false' to indicate no module processing\\n` +\n      ` - a specific module type: 'commonjs', 'amd', 'umd', 'systemjs'` +\n      ` - 'auto' (default) which will automatically select 'false' if the current\\n` +\n      `   process is known to support ES module syntax, or \"commonjs\" otherwise\\n`,\n  );\n\n  return modulesOpt;\n};\n\nexport const validateUseBuiltInsOption = (\n  builtInsOpt: BuiltInsOption = false,\n) => {\n  v.invariant(\n    // @ts-expect-error we have provided fallback for undefined keys\n    UseBuiltInsOption[builtInsOpt.toString()] ||\n      builtInsOpt === UseBuiltInsOption.false,\n    `The 'useBuiltIns' option must be either\n    'false' (default) to indicate no polyfill,\n    '\"entry\"' to indicate replacing the entry polyfill, or\n    '\"usage\"' to import only used polyfills per file`,\n  );\n\n  return builtInsOpt;\n};\n\nexport type NormalizedCorejsOption = {\n  proposals: boolean;\n  version: SemVer | null | false;\n};\n\nexport function normalizeCoreJSOption(\n  corejs: CorejsOption | undefined | null,\n  useBuiltIns: BuiltInsOption,\n): NormalizedCorejsOption {\n  let proposals = false;\n  let rawVersion;\n\n  if (useBuiltIns && corejs === undefined) {\n    rawVersion = 2;\n    console.warn(\n      \"\\nWARNING (@babel/preset-env): We noticed you're using the `useBuiltIns` option without declaring a \" +\n        \"core-js version. Currently, we assume version 2.x when no version \" +\n        \"is passed. Since this default version will likely change in future \" +\n        \"versions of Babel, we recommend explicitly setting the core-js version \" +\n        \"you are using via the `corejs` option.\\n\" +\n        \"\\nYou should also be sure that the version you pass to the `corejs` \" +\n        \"option matches the version specified in your `package.json`'s \" +\n        \"`dependencies` section. If it doesn't, you need to run one of the \" +\n        \"following commands:\\n\\n\" +\n        \"  npm install --save core-js@2    npm install --save core-js@3\\n\" +\n        \"  yarn add core-js@2              yarn add core-js@3\\n\\n\" +\n        \"More info about useBuiltIns: https://babeljs.io/docs/en/babel-preset-env#usebuiltins\\n\" +\n        \"More info about core-js: https://babeljs.io/docs/en/babel-preset-env#corejs\",\n    );\n  } else if (typeof corejs === \"object\" && corejs !== null) {\n    rawVersion = corejs.version;\n    proposals = Boolean(corejs.proposals);\n  } else {\n    rawVersion = corejs;\n  }\n\n  const version = rawVersion ? semver.coerce(String(rawVersion)) : false;\n\n  if (!useBuiltIns && version) {\n    console.warn(\n      \"\\nWARNING (@babel/preset-env): The `corejs` option only has an effect when the `useBuiltIns` option is not `false`\\n\",\n    );\n  }\n\n  if (useBuiltIns && (!version || version.major < 2 || version.major > 3)) {\n    throw new RangeError(\n      \"Invalid Option: The version passed to `corejs` is invalid. Currently, \" +\n        \"only core-js@2 and core-js@3 are supported.\",\n    );\n  }\n\n  return { version, proposals };\n}\n\nexport default function normalizeOptions(opts: Options) {\n  v.validateTopLevelOptions(opts, TopLevelOptions);\n\n  const useBuiltIns = validateUseBuiltInsOption(opts.useBuiltIns);\n\n  const corejs = normalizeCoreJSOption(opts.corejs, useBuiltIns);\n\n  const include = expandIncludesAndExcludes(\n    opts.include,\n    TopLevelOptions.include,\n    !!corejs.version && corejs.version.major,\n  );\n\n  const exclude = expandIncludesAndExcludes(\n    opts.exclude,\n    TopLevelOptions.exclude,\n    !!corejs.version && corejs.version.major,\n  );\n\n  checkDuplicateIncludeExcludes(include, exclude);\n\n  return {\n    bugfixes: v.validateBooleanOption(\n      TopLevelOptions.bugfixes,\n      opts.bugfixes,\n      process.env.BABEL_8_BREAKING ? true : false,\n    ),\n    configPath: v.validateStringOption(\n      TopLevelOptions.configPath,\n      opts.configPath,\n      process.cwd(),\n    ),\n    corejs,\n    debug: v.validateBooleanOption(TopLevelOptions.debug, opts.debug, false),\n    include,\n    exclude,\n    forceAllTransforms: v.validateBooleanOption(\n      TopLevelOptions.forceAllTransforms,\n      opts.forceAllTransforms,\n      false,\n    ),\n    ignoreBrowserslistConfig: v.validateBooleanOption(\n      TopLevelOptions.ignoreBrowserslistConfig,\n      opts.ignoreBrowserslistConfig,\n      false,\n    ),\n    loose: v.validateBooleanOption<boolean>(TopLevelOptions.loose, opts.loose),\n    modules: validateModulesOption(opts.modules),\n    shippedProposals: v.validateBooleanOption(\n      TopLevelOptions.shippedProposals,\n      opts.shippedProposals,\n      false,\n    ),\n    spec: v.validateBooleanOption(TopLevelOptions.spec, opts.spec, false),\n    targets: normalizeTargets(opts.targets),\n    useBuiltIns: useBuiltIns,\n    browserslistEnv: v.validateStringOption<string>(\n      TopLevelOptions.browserslistEnv,\n      opts.browserslistEnv,\n    ),\n  };\n}\n"], "mappings": ";;;;;;;;;AAAA,IAAAA,OAAA,GAAAC,OAAA;AACA,IAAAC,gBAAA,GAAAD,OAAA;AAIA,IAAAE,aAAA,GAAAF,OAAA;AACA,IAAAG,kBAAA,GAAAH,OAAA;AACA,IAAAI,sBAAA,GAAAJ,OAAA;AACA,IAAAK,QAAA,GAAAL,OAAA;AAKA,IAAAM,sBAAA,GAAAN,OAAA;AAEA,MAAMO,yBAAyB,GAAG,CAChC,YAAY,EACZ,eAAe,EACf,kBAAkB,CACnB;AAUD,MAAMC,CAAC,GAAG,IAAIC,sCAAe,oBAAkB,CAAC;AAEhD,MAAMC,cAAc,GAAGC,MAAM,CAACC,IAAI,CAACC,0BAAW,CAAC;AAK/C,MAAMC,aAAa,GAAG,CACpB,0BAA0B,EAC1B,GAAGH,MAAM,CAACC,IAAI,CAACG,8BAAqB,CAAC,CAACC,GAAG,CAACC,CAAC,IAAIF,8BAAqB,CAACE,CAAC,CAAC,CAAC,CACzE;AAED,MAAMC,2BAA2B,GAAGA,CAClCC,IAA2B,EAC3BC,MAAsB,KAEtBC,KAAK,CAACC,IAAI,CACR,IAAIC,GAAG,CAAC,CACN,GAAGb,cAAc,EACjB,IAAIS,IAAI,KAAK,SAAS,GAAGL,aAAa,GAAG,EAAE,CAAC,EAC5C,IAAIM,MAAM,GACNA,MAAM,IAAI,CAAC,GACT,CAAC,GAAGT,MAAM,CAACC,IAAI,CAACY,gBAAgB,CAAC,EAAE,GAAGjB,yBAAyB,CAAC,GAChEI,MAAM,CAACC,IAAI,CAACa,aAAgB,CAAC,GAC/B,EAAE,CAAC,CACR,CACH,CAAC;AAEH,SAASC,OAAOA,CAAOC,KAAe,EAAEC,EAAyB,EAAY;EAC3E,OAAOP,KAAK,CAACQ,SAAS,CAACC,MAAM,CAACC,KAAK,CAAC,EAAE,EAAEJ,KAAK,CAACX,GAAG,CAACY,EAAE,CAAC,CAAC;AACxD;AAEO,MAAMI,mBAAmB,GAAIC,MAAc,IAChDA,MAAM,CAACC,OAAO,CAAC,8BAA8B,EAAE,EAAE,CAAC;AAACC,OAAA,CAAAH,mBAAA,GAAAA,mBAAA;AAErD,MAAMI,yBAAyB,GAAGA,CAChCC,UAA4B,GAAG,EAAE,EACjClB,IAA2B,EAC3BC,MAAsB,KACnB;EACH,IAAIiB,UAAU,CAACC,MAAM,KAAK,CAAC,EAAE,OAAO,EAAE;EAEtC,MAAMC,eAAe,GAAGrB,2BAA2B,CAACC,IAAI,EAAEC,MAAM,CAAC;EAEjE,MAAMoB,cAAgC,GAAG,EAAE;EAC3C,MAAMC,eAAe,GAAGf,OAAO,CAACW,UAAU,EAAEK,MAAM,IAAI;IACpD,IAAIC,EAAU;IACd,IAAI,OAAOD,MAAM,KAAK,QAAQ,EAAE;MAC9B,IAAI;QACFC,EAAE,GAAG,IAAIC,MAAM,CAAE,IAAGZ,mBAAmB,CAACU,MAAM,CAAE,GAAE,CAAC;MACrD,CAAC,CAAC,OAAOG,CAAC,EAAE;QACVL,cAAc,CAACM,IAAI,CAACJ,MAAM,CAAC;QAC3B,OAAO,EAAE;MACX;IACF,CAAC,MAAM;MACLC,EAAE,GAAGD,MAAM;IACb;IACA,MAAMK,KAAK,GAAGR,eAAe,CAACG,MAAM,CAACM,IAAI,IAAI;MAC3C,OAEIL,EAAE,CAACM,IAAI,CAACD,IAAI,CAAC,IAGXL,EAAE,CAACM,IAAI,CAACD,IAAI,CAACd,OAAO,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;IACzD,CAAC,CAAC;IACF,IAAIa,KAAK,CAACT,MAAM,KAAK,CAAC,EAAEE,cAAc,CAACM,IAAI,CAACJ,MAAM,CAAC;IACnD,OAAOK,KAAK;EACd,CAAC,CAAC;EAEFvC,CAAC,CAAC0C,SAAS,CACTV,cAAc,CAACF,MAAM,KAAK,CAAC,EAC1B,0BAAyBE,cAAc,CAACW,IAAI,CAC3C,IACF,CAAE,oBAAmBhC,IAAK;AAC9B,wFACE,CAAC;EAED,OAAOsB,eAAe;AACxB,CAAC;AAEM,MAAMW,6BAA6B,GAAGA,CAC3CC,OAAsB,GAAG,EAAE,EAC3BC,OAAsB,GAAG,EAAE,KACxB;EACH,MAAMC,UAAU,GAAGF,OAAO,CAACX,MAAM,CAACc,GAAG,IAAIF,OAAO,CAACG,OAAO,CAACD,GAAG,CAAC,IAAI,CAAC,CAAC;EAEnEhD,CAAC,CAAC0C,SAAS,CACTK,UAAU,CAACjB,MAAM,KAAK,CAAC,EACtB,0BAAyBiB,UAAU,CAACJ,IAAI,CACvC,IACF,CAAE;AACN,uBACE,CAAC;AACH,CAAC;AAAChB,OAAA,CAAAiB,6BAAA,GAAAA,6BAAA;AAEF,MAAMM,gBAAgB,GACpBC,OAA+C,IACxB;EAEvB,IAAI,OAAOA,OAAO,KAAK,QAAQ,IAAItC,KAAK,CAACuC,OAAO,CAACD,OAAO,CAAC,EAAE;IACzD,OAAO;MAAEE,QAAQ,EAAEF;IAAQ,CAAC;EAC9B;EACA,OAAAhD,MAAA,CAAAmD,MAAA,KAAYH,OAAO;AACrB,CAAC;AAEM,MAAMI,qBAAqB,GAAGA,CACnCC,UAAwB,GAAGC,sBAAa,CAACC,IAAI,KAC1C;EACH1D,CAAC,CAAC0C,SAAS,CAETe,sBAAa,CAACD,UAAU,CAACG,QAAQ,CAAC,CAAC,CAAC,IAAIH,UAAU,KAAKC,sBAAa,CAACG,KAAK,EACzE,wCAAuC,GACrC,+CAA8C,GAC9C,iEAAgE,GAChE,8EAA6E,GAC7E,4EACL,CAAC;EAED,OAAOJ,UAAU;AACnB,CAAC;AAAC7B,OAAA,CAAA4B,qBAAA,GAAAA,qBAAA;AAEK,MAAMM,yBAAyB,GAAGA,CACvCC,WAA2B,GAAG,KAAK,KAChC;EACH9D,CAAC,CAAC0C,SAAS,CAETqB,0BAAiB,CAACD,WAAW,CAACH,QAAQ,CAAC,CAAC,CAAC,IACvCG,WAAW,KAAKC,0BAAiB,CAACH,KAAK,EACxC;AACL;AACA;AACA,qDACE,CAAC;EAED,OAAOE,WAAW;AACpB,CAAC;AAACnC,OAAA,CAAAkC,yBAAA,GAAAA,yBAAA;AAOK,SAASG,qBAAqBA,CACnCpD,MAAuC,EACvCqD,WAA2B,EACH;EACxB,IAAIC,SAAS,GAAG,KAAK;EACrB,IAAIC,UAAU;EAEd,IAAIF,WAAW,IAAIrD,MAAM,KAAKwD,SAAS,EAAE;IACvCD,UAAU,GAAG,CAAC;IACdE,OAAO,CAACC,IAAI,CACV,sGAAsG,GACpG,oEAAoE,GACpE,qEAAqE,GACrE,yEAAyE,GACzE,0CAA0C,GAC1C,sEAAsE,GACtE,gEAAgE,GAChE,oEAAoE,GACpE,yBAAyB,GACzB,kEAAkE,GAClE,0DAA0D,GAC1D,wFAAwF,GACxF,6EACJ,CAAC;EACH,CAAC,MAAM,IAAI,OAAO1D,MAAM,KAAK,QAAQ,IAAIA,MAAM,KAAK,IAAI,EAAE;IACxDuD,UAAU,GAAGvD,MAAM,CAAC2D,OAAO;IAC3BL,SAAS,GAAGM,OAAO,CAAC5D,MAAM,CAACsD,SAAS,CAAC;EACvC,CAAC,MAAM;IACLC,UAAU,GAAGvD,MAAM;EACrB;EAEA,MAAM2D,OAAO,GAAGJ,UAAU,GAAGM,OAAM,CAACC,MAAM,CAACC,MAAM,CAACR,UAAU,CAAC,CAAC,GAAG,KAAK;EAEtE,IAAI,CAACF,WAAW,IAAIM,OAAO,EAAE;IAC3BF,OAAO,CAACC,IAAI,CACV,sHACF,CAAC;EACH;EAEA,IAAIL,WAAW,KAAK,CAACM,OAAO,IAAIA,OAAO,CAACK,KAAK,GAAG,CAAC,IAAIL,OAAO,CAACK,KAAK,GAAG,CAAC,CAAC,EAAE;IACvE,MAAM,IAAIC,UAAU,CAClB,wEAAwE,GACtE,6CACJ,CAAC;EACH;EAEA,OAAO;IAAEN,OAAO;IAAEL;EAAU,CAAC;AAC/B;AAEe,SAASY,gBAAgBA,CAACC,IAAa,EAAE;EACtD/E,CAAC,CAACgF,uBAAuB,CAACD,IAAI,EAAEE,wBAAe,CAAC;EAEhD,MAAMhB,WAAW,GAAGJ,yBAAyB,CAACkB,IAAI,CAACd,WAAW,CAAC;EAE/D,MAAMrD,MAAM,GAAGoD,qBAAqB,CAACe,IAAI,CAACnE,MAAM,EAAEqD,WAAW,CAAC;EAE9D,MAAMpB,OAAO,GAAGjB,yBAAyB,CACvCmD,IAAI,CAAClC,OAAO,EACZoC,wBAAe,CAACpC,OAAO,EACvB,CAAC,CAACjC,MAAM,CAAC2D,OAAO,IAAI3D,MAAM,CAAC2D,OAAO,CAACK,KACrC,CAAC;EAED,MAAM9B,OAAO,GAAGlB,yBAAyB,CACvCmD,IAAI,CAACjC,OAAO,EACZmC,wBAAe,CAACnC,OAAO,EACvB,CAAC,CAAClC,MAAM,CAAC2D,OAAO,IAAI3D,MAAM,CAAC2D,OAAO,CAACK,KACrC,CAAC;EAEDhC,6BAA6B,CAACC,OAAO,EAAEC,OAAO,CAAC;EAE/C,OAAO;IACLoC,QAAQ,EAAElF,CAAC,CAACmF,qBAAqB,CAC/BF,wBAAe,CAACC,QAAQ,EACxBH,IAAI,CAACG,QAAQ,EACyB,KACxC,CAAC;IACDE,UAAU,EAAEpF,CAAC,CAACqF,oBAAoB,CAChCJ,wBAAe,CAACG,UAAU,EAC1BL,IAAI,CAACK,UAAU,EACfE,OAAO,CAACC,GAAG,CAAC,CACd,CAAC;IACD3E,MAAM;IACN4E,KAAK,EAAExF,CAAC,CAACmF,qBAAqB,CAACF,wBAAe,CAACO,KAAK,EAAET,IAAI,CAACS,KAAK,EAAE,KAAK,CAAC;IACxE3C,OAAO;IACPC,OAAO;IACP2C,kBAAkB,EAAEzF,CAAC,CAACmF,qBAAqB,CACzCF,wBAAe,CAACQ,kBAAkB,EAClCV,IAAI,CAACU,kBAAkB,EACvB,KACF,CAAC;IACDC,wBAAwB,EAAE1F,CAAC,CAACmF,qBAAqB,CAC/CF,wBAAe,CAACS,wBAAwB,EACxCX,IAAI,CAACW,wBAAwB,EAC7B,KACF,CAAC;IACDC,KAAK,EAAE3F,CAAC,CAACmF,qBAAqB,CAAUF,wBAAe,CAACU,KAAK,EAAEZ,IAAI,CAACY,KAAK,CAAC;IAC1EC,OAAO,EAAErC,qBAAqB,CAACwB,IAAI,CAACa,OAAO,CAAC;IAC5CC,gBAAgB,EAAE7F,CAAC,CAACmF,qBAAqB,CACvCF,wBAAe,CAACY,gBAAgB,EAChCd,IAAI,CAACc,gBAAgB,EACrB,KACF,CAAC;IACDC,IAAI,EAAE9F,CAAC,CAACmF,qBAAqB,CAACF,wBAAe,CAACa,IAAI,EAAEf,IAAI,CAACe,IAAI,EAAE,KAAK,CAAC;IACrE3C,OAAO,EAAED,gBAAgB,CAAC6B,IAAI,CAAC5B,OAAO,CAAC;IACvCc,WAAW,EAAEA,WAAW;IACxB8B,eAAe,EAAE/F,CAAC,CAACqF,oBAAoB,CACrCJ,wBAAe,CAACc,eAAe,EAC/BhB,IAAI,CAACgB,eACP;EACF,CAAC;AACH"}