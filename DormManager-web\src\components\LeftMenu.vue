<template>
 <nav class="pcoded-navbar">
        <div class="navbar-wrapper">
            <div class="navbar-brand header-logo">
                <a href="/main" class="b-brand">
                    <div class="b-bg">
                        <i class="feather icon-trending-up"></i>
                    </div>
                    <span class="b-title">宿舍管理系统</span>
                </a>

            </div>
            <div class="navbar-content scroll-div">
                <ul class="nav pcoded-inner-navbar" v-show="role == '管理员'">
                    <li class="nav-item pcoded-menu-caption">
                        <label>功能菜单</label>
                    </li>

                       <li  class="nav-item pcoded-hasmenu">
                        <a href="#!" class="nav-link"><span class="pcoded-micon"><i class="feather icon-gitlab"></i></span><span class="pcoded-mtext">宿舍楼管理</span></a>
                        <ul class="pcoded-submenu">
                             <li> <router-link to="/dormbuildingAdd">添加宿舍楼</router-link></li>
  <li> <router-link to="/dormbuildingManage">管理宿舍楼</router-link></li>

                        </ul>
                    </li>

                      <li  class="nav-item pcoded-hasmenu">
                        <a href="#!" class="nav-link"><span class="pcoded-micon"><i class="feather icon-gitlab"></i></span><span class="pcoded-mtext">宿舍管理</span></a>
                        <ul class="pcoded-submenu">
                             <li> <router-link to="/dormitoryAdd">添加宿舍</router-link></li>
  <li> <router-link to="/dormitoryManage">管理宿舍</router-link></li>

                        </ul>
                    </li>

                         <li  class="nav-item pcoded-hasmenu">
                        <a href="#!" class="nav-link"><span class="pcoded-micon"><i class="feather icon-gitlab"></i></span><span class="pcoded-mtext">学生管理</span></a>
                        <ul class="pcoded-submenu">
                             <li> <router-link to="/studentAdd">添加学生</router-link></li>
  <li> <router-link to="/studentManage">管理学生</router-link></li>

                        </ul>
                    </li>

                           <li  class="nav-item pcoded-hasmenu">
                        <a href="#!" class="nav-link"><span class="pcoded-micon"><i class="feather icon-gitlab"></i></span><span class="pcoded-mtext">宿管阿姨管理</span></a>
                        <ul class="pcoded-submenu">
                             <li> <router-link to="/hostessAdd">添加宿管阿姨</router-link></li>
  <li> <router-link to="/hostessManage">管理宿管阿姨</router-link></li>

                        </ul>
                    </li>

                         <li  class="nav-item pcoded-hasmenu">
                        <a href="#!" class="nav-link"><span class="pcoded-micon"><i class="feather icon-gitlab"></i></span><span class="pcoded-mtext">维修员管理</span></a>
                        <ul class="pcoded-submenu">
                             <li> <router-link to="/repairmenAdd">添加维修员</router-link></li>
  <li> <router-link to="/repairmenManage">管理维修员</router-link></li>

                        </ul>
                    </li>


                         <li  class="nav-item pcoded-hasmenu">
                        <a href="#!" class="nav-link"><span class="pcoded-micon"><i class="feather icon-gitlab"></i></span><span class="pcoded-mtext">公告管理</span></a>
                        <ul class="pcoded-submenu">
                             <li> <router-link to="/systemnoticesAdd">添加公告</router-link></li>
  <li> <router-link to="/systemnoticesManage">管理公告</router-link></li>

                        </ul>
                    </li>

                          <li  class="nav-item pcoded-hasmenu">
                        <a href="#!" class="nav-link"><span class="pcoded-micon"><i class="feather icon-gitlab"></i></span><span class="pcoded-mtext">宿舍更换管理</span></a>
                        <ul class="pcoded-submenu">
                             <li> <router-link to="/dormitorychangeAdd">添加宿舍更换</router-link></li>
  <li> <router-link to="/dormitorychangeManage">管理宿舍更换</router-link></li>

                        </ul>
                    </li>

                     
       <li  class="nav-item pcoded-hasmenu">
                        <a href="#!" class="nav-link"><span class="pcoded-micon"><i class="feather icon-gitlab"></i></span><span class="pcoded-mtext">报修管理</span></a>
                        <ul class="pcoded-submenu">
                         <li> <router-link to="/repairtypeAdd">添加报修类型</router-link></li>
  <li> <router-link to="/repairtypeManage">管理报修类型</router-link></li>
  <li> <router-link to="/repairordersManage">管理报修</router-link></li>
  <li> <router-link to="/repairordersManage2">报修列表</router-link></li>

                        </ul>
                    </li>

                    
       <li  class="nav-item pcoded-hasmenu">
                        <a href="#!" class="nav-link"><span class="pcoded-micon"><i class="feather icon-gitlab"></i></span><span class="pcoded-mtext">水电费管理</span></a>
                        <ul class="pcoded-submenu">
                             <li> <router-link to="/waterelectricityfeeAdd">添加水电费</router-link></li>
  <li> <router-link to="/waterelectricityfeeManage">管理水电费</router-link></li>
  <li> <router-link to="/waterelectricityfeeManage2">水电费列表</router-link></li>

                        </ul>
                    </li>
    

  
       <li  class="nav-item pcoded-hasmenu">
                        <a href="#!" class="nav-link"><span class="pcoded-micon"><i class="feather icon-gitlab"></i></span><span class="pcoded-mtext">离校登记管理</span></a>
                        <ul class="pcoded-submenu">
                             <li> <router-link to="/leaveschoolAdd">添加离校登记</router-link></li>
  <li> <router-link to="/leaveschoolManage">管理离校登记</router-link></li>
  <li> <router-link to="/leaveschoolManage2">离校登记列表</router-link></li>

                        </ul>
                    </li>

                     
       <li  class="nav-item pcoded-hasmenu">
                        <a href="#!" class="nav-link"><span class="pcoded-micon"><i class="feather icon-gitlab"></i></span><span class="pcoded-mtext">返校登记管理</span></a>
                        <ul class="pcoded-submenu">
                             <li> <router-link to="/registerinfoAdd">添加返校登记</router-link></li>
  <li> <router-link to="/registerinfoManage">管理返校登记</router-link></li>
  <li> <router-link to="/registerinfoManage2">返校登记列表</router-link></li>

                        </ul>
                    </li>


       <li  class="nav-item pcoded-hasmenu">
                        <a href="#!" class="nav-link"><span class="pcoded-micon"><i class="feather icon-gitlab"></i></span><span class="pcoded-mtext">宿舍评分管理</span></a>
                        <ul class="pcoded-submenu">
                             <li> <router-link to="/dormitoryscoreAdd">添加宿舍评分</router-link></li>
  <li> <router-link to="/dormitoryscoreManage">管理宿舍评分</router-link></li>
  <li> <router-link to="/dormitoryscoreManage2">宿舍评分列表</router-link></li>

                        </ul>
                    </li>
 
    

     
       <li  class="nav-item pcoded-hasmenu">
                        <a href="#!" class="nav-link"><span class="pcoded-micon"><i class="feather icon-gitlab"></i></span><span class="pcoded-mtext">图表管理</span></a>
                        <ul class="pcoded-submenu">
                             <li> <router-link to="/total1">图表1</router-link></li>
  <li> <router-link to="/total2">图表2</router-link></li>
  <li> <router-link to="/total3">图表3</router-link></li>
  <li> <router-link to="/total4">图表4</router-link></li>

                        </ul>
                    </li>

                      <li  class="nav-item pcoded-hasmenu">
                        <a href="#!" class="nav-link"><span class="pcoded-micon"><i class="feather icon-gitlab"></i></span><span class="pcoded-mtext">图表管理</span></a>
                        <ul class="pcoded-submenu">
                             <li> <router-link to="/total1">图表1</router-link></li>
  <li> <router-link to="/total2">图表2</router-link></li>
  <li> <router-link to="/total3">图表3</router-link></li>
  <li> <router-link to="/total4">图表4</router-link></li>

                        </ul>
                    </li>

          
                </ul>

                      <ul class="nav pcoded-inner-navbar" v-show="role == '学生'">
                    <li class="nav-item pcoded-menu-caption">
                        <label>功能菜单</label>
                    </li>
                         <li  class="nav-item pcoded-hasmenu">
                        <a href="#!" class="nav-link"><span class="pcoded-micon"><i class="feather icon-gitlab"></i></span><span class="pcoded-mtext">公告管理</span></a>
                        <ul class="pcoded-submenu">
                             <li> <router-link to="/systemnoticesAdd">添加公告</router-link></li>
  <li> <router-link to="/systemnoticesManage">管理公告</router-link></li>
  <li> <router-link to="/systemnoticesManage2">公告列表</router-link></li>

                        </ul>
                    </li>
       <li  class="nav-item pcoded-hasmenu">
                        <a href="#!" class="nav-link"><span class="pcoded-micon"><i class="feather icon-gitlab"></i></span><span class="pcoded-mtext">维修员管理</span></a>
                        <ul class="pcoded-submenu">
                             <li> <router-link to="/repairmenAdd">添加维修员</router-link></li>
  <li> <router-link to="/repairmenManage">管理维修员</router-link></li>
  <li> <router-link to="/repairmenInfo">修改个人信息</router-link></li>

                        </ul>
                    </li>
       <li  class="nav-item pcoded-hasmenu">
                        <a href="#!" class="nav-link"><span class="pcoded-micon"><i class="feather icon-gitlab"></i></span><span class="pcoded-mtext">离校登记管理</span></a>
                        <ul class="pcoded-submenu">
                             <li> <router-link to="/leaveschoolAdd">添加离校登记</router-link></li>
  <li> <router-link to="/leaveschoolManage">管理离校登记</router-link></li>
  <li> <router-link to="/leaveschoolManage2">离校登记列表</router-link></li>

                        </ul>
                    </li>
       <li  class="nav-item pcoded-hasmenu">
                        <a href="#!" class="nav-link"><span class="pcoded-micon"><i class="feather icon-gitlab"></i></span><span class="pcoded-mtext">宿舍评分管理</span></a>
                        <ul class="pcoded-submenu">
                             <li> <router-link to="/dormitoryscoreAdd">添加宿舍评分</router-link></li>
  <li> <router-link to="/dormitoryscoreManage">管理宿舍评分</router-link></li>
  <li> <router-link to="/dormitoryscoreManage2">宿舍评分列表</router-link></li>

                        </ul>
                    </li>
       <li  class="nav-item pcoded-hasmenu">
                        <a href="#!" class="nav-link"><span class="pcoded-micon"><i class="feather icon-gitlab"></i></span><span class="pcoded-mtext">宿舍更换管理</span></a>
                        <ul class="pcoded-submenu">
                             <li> <router-link to="/dormitorychangeAdd">添加宿舍更换</router-link></li>
  <li> <router-link to="/dormitorychangeManage">管理宿舍更换</router-link></li>
  <li> <router-link to="/dormitorychangeManage2">宿舍更换列表</router-link></li>

                        </ul>
                    </li>
       <li  class="nav-item pcoded-hasmenu">
                        <a href="#!" class="nav-link"><span class="pcoded-micon"><i class="feather icon-gitlab"></i></span><span class="pcoded-mtext">水电费管理</span></a>
                        <ul class="pcoded-submenu">
                             <li> <router-link to="/waterelectricityfeeAdd">添加水电费</router-link></li>
  <li> <router-link to="/waterelectricityfeeManage">管理水电费</router-link></li>
  <li> <router-link to="/waterelectricityfeeManage2">水电费列表</router-link></li>

                        </ul>
                    </li>
       <li  class="nav-item pcoded-hasmenu">
                        <a href="#!" class="nav-link"><span class="pcoded-micon"><i class="feather icon-gitlab"></i></span><span class="pcoded-mtext">报修类型管理</span></a>
                        <ul class="pcoded-submenu">
                             <li> <router-link to="/repairtypeAdd">添加报修类型</router-link></li>
  <li> <router-link to="/repairtypeManage">管理报修类型</router-link></li>

                        </ul>
                    </li>
       <li  class="nav-item pcoded-hasmenu">
                        <a href="#!" class="nav-link"><span class="pcoded-micon"><i class="feather icon-gitlab"></i></span><span class="pcoded-mtext">宿舍楼管理</span></a>
                        <ul class="pcoded-submenu">
                             <li> <router-link to="/dormbuildingAdd">添加宿舍楼</router-link></li>
  <li> <router-link to="/dormbuildingManage">管理宿舍楼</router-link></li>

                        </ul>
                    </li>
       <li  class="nav-item pcoded-hasmenu">
                        <a href="#!" class="nav-link"><span class="pcoded-micon"><i class="feather icon-gitlab"></i></span><span class="pcoded-mtext">学生管理</span></a>
                        <ul class="pcoded-submenu">
                             <li> <router-link to="/studentAdd">添加学生</router-link></li>
  <li> <router-link to="/studentManage">管理学生</router-link></li>
  <li> <router-link to="/studentInfo">修改个人信息</router-link></li>

                        </ul>
                    </li>
       <li  class="nav-item pcoded-hasmenu">
                        <a href="#!" class="nav-link"><span class="pcoded-micon"><i class="feather icon-gitlab"></i></span><span class="pcoded-mtext">报修管理</span></a>
                        <ul class="pcoded-submenu">
                             <li> <router-link to="/repairordersAdd">添加报修</router-link></li>
  <li> <router-link to="/repairordersManage">管理报修</router-link></li>
  <li> <router-link to="/repairordersManage2">报修列表</router-link></li>

                        </ul>
                    </li>
       <li  class="nav-item pcoded-hasmenu">
                        <a href="#!" class="nav-link"><span class="pcoded-micon"><i class="feather icon-gitlab"></i></span><span class="pcoded-mtext">返校登记管理</span></a>
                        <ul class="pcoded-submenu">
                             <li> <router-link to="/registerinfoAdd">添加返校登记</router-link></li>
  <li> <router-link to="/registerinfoManage">管理返校登记</router-link></li>
  <li> <router-link to="/registerinfoManage2">返校登记列表</router-link></li>

                        </ul>
                    </li>
       <li  class="nav-item pcoded-hasmenu">
                        <a href="#!" class="nav-link"><span class="pcoded-micon"><i class="feather icon-gitlab"></i></span><span class="pcoded-mtext">宿管阿姨管理</span></a>
                        <ul class="pcoded-submenu">
                             <li> <router-link to="/hostessAdd">添加宿管阿姨</router-link></li>
  <li> <router-link to="/hostessManage">管理宿管阿姨</router-link></li>
  <li> <router-link to="/hostessInfo">修改个人信息</router-link></li>

                        </ul>
                    </li>
       <li  class="nav-item pcoded-hasmenu">
                        <a href="#!" class="nav-link"><span class="pcoded-micon"><i class="feather icon-gitlab"></i></span><span class="pcoded-mtext">宿舍管理</span></a>
                        <ul class="pcoded-submenu">
                             <li> <router-link to="/dormitoryAdd">添加宿舍</router-link></li>
  <li> <router-link to="/dormitoryManage">管理宿舍</router-link></li>

                        </ul>
                    </li>
       <li  class="nav-item pcoded-hasmenu">
                        <a href="#!" class="nav-link"><span class="pcoded-micon"><i class="feather icon-gitlab"></i></span><span class="pcoded-mtext">图表管理</span></a>
                        <ul class="pcoded-submenu">
                             <li> <router-link to="/total1">图表1</router-link></li>
  <li> <router-link to="/total2">图表2</router-link></li>
  <li> <router-link to="/total3">图表3</router-link></li>
  <li> <router-link to="/total4">图表4</router-link></li>

                        </ul>
                    </li>

          
                </ul>


                      <ul class="nav pcoded-inner-navbar" v-show="role == '维修员'">
                    <li class="nav-item pcoded-menu-caption">
                        <label>功能菜单</label>
                    </li>
                         <li  class="nav-item pcoded-hasmenu">
                        <a href="#!" class="nav-link"><span class="pcoded-micon"><i class="feather icon-gitlab"></i></span><span class="pcoded-mtext">公告管理</span></a>
                        <ul class="pcoded-submenu">
                             <li> <router-link to="/systemnoticesAdd">添加公告</router-link></li>
  <li> <router-link to="/systemnoticesManage">管理公告</router-link></li>
  <li> <router-link to="/systemnoticesManage2">公告列表</router-link></li>

                        </ul>
                    </li>
       <li  class="nav-item pcoded-hasmenu">
                        <a href="#!" class="nav-link"><span class="pcoded-micon"><i class="feather icon-gitlab"></i></span><span class="pcoded-mtext">维修员管理</span></a>
                        <ul class="pcoded-submenu">
                             <li> <router-link to="/repairmenAdd">添加维修员</router-link></li>
  <li> <router-link to="/repairmenManage">管理维修员</router-link></li>
  <li> <router-link to="/repairmenInfo">修改个人信息</router-link></li>

                        </ul>
                    </li>
       <li  class="nav-item pcoded-hasmenu">
                        <a href="#!" class="nav-link"><span class="pcoded-micon"><i class="feather icon-gitlab"></i></span><span class="pcoded-mtext">离校登记管理</span></a>
                        <ul class="pcoded-submenu">
                             <li> <router-link to="/leaveschoolAdd">添加离校登记</router-link></li>
  <li> <router-link to="/leaveschoolManage">管理离校登记</router-link></li>
  <li> <router-link to="/leaveschoolManage2">离校登记列表</router-link></li>

                        </ul>
                    </li>
       <li  class="nav-item pcoded-hasmenu">
                        <a href="#!" class="nav-link"><span class="pcoded-micon"><i class="feather icon-gitlab"></i></span><span class="pcoded-mtext">宿舍评分管理</span></a>
                        <ul class="pcoded-submenu">
                             <li> <router-link to="/dormitoryscoreAdd">添加宿舍评分</router-link></li>
  <li> <router-link to="/dormitoryscoreManage">管理宿舍评分</router-link></li>
  <li> <router-link to="/dormitoryscoreManage2">宿舍评分列表</router-link></li>

                        </ul>
                    </li>
       <li  class="nav-item pcoded-hasmenu">
                        <a href="#!" class="nav-link"><span class="pcoded-micon"><i class="feather icon-gitlab"></i></span><span class="pcoded-mtext">宿舍更换管理</span></a>
                        <ul class="pcoded-submenu">
                             <li> <router-link to="/dormitorychangeAdd">添加宿舍更换</router-link></li>
  <li> <router-link to="/dormitorychangeManage">管理宿舍更换</router-link></li>
  <li> <router-link to="/dormitorychangeManage2">宿舍更换列表</router-link></li>

                        </ul>
                    </li>
       <li  class="nav-item pcoded-hasmenu">
                        <a href="#!" class="nav-link"><span class="pcoded-micon"><i class="feather icon-gitlab"></i></span><span class="pcoded-mtext">水电费管理</span></a>
                        <ul class="pcoded-submenu">
                             <li> <router-link to="/waterelectricityfeeAdd">添加水电费</router-link></li>
  <li> <router-link to="/waterelectricityfeeManage">管理水电费</router-link></li>
  <li> <router-link to="/waterelectricityfeeManage2">水电费列表</router-link></li>

                        </ul>
                    </li>
       <li  class="nav-item pcoded-hasmenu">
                        <a href="#!" class="nav-link"><span class="pcoded-micon"><i class="feather icon-gitlab"></i></span><span class="pcoded-mtext">报修类型管理</span></a>
                        <ul class="pcoded-submenu">
                             <li> <router-link to="/repairtypeAdd">添加报修类型</router-link></li>
  <li> <router-link to="/repairtypeManage">管理报修类型</router-link></li>

                        </ul>
                    </li>
       <li  class="nav-item pcoded-hasmenu">
                        <a href="#!" class="nav-link"><span class="pcoded-micon"><i class="feather icon-gitlab"></i></span><span class="pcoded-mtext">宿舍楼管理</span></a>
                        <ul class="pcoded-submenu">
                             <li> <router-link to="/dormbuildingAdd">添加宿舍楼</router-link></li>
  <li> <router-link to="/dormbuildingManage">管理宿舍楼</router-link></li>

                        </ul>
                    </li>
       <li  class="nav-item pcoded-hasmenu">
                        <a href="#!" class="nav-link"><span class="pcoded-micon"><i class="feather icon-gitlab"></i></span><span class="pcoded-mtext">学生管理</span></a>
                        <ul class="pcoded-submenu">
                             <li> <router-link to="/studentAdd">添加学生</router-link></li>
  <li> <router-link to="/studentManage">管理学生</router-link></li>
  <li> <router-link to="/studentInfo">修改个人信息</router-link></li>

                        </ul>
                    </li>
       <li  class="nav-item pcoded-hasmenu">
                        <a href="#!" class="nav-link"><span class="pcoded-micon"><i class="feather icon-gitlab"></i></span><span class="pcoded-mtext">报修管理</span></a>
                        <ul class="pcoded-submenu">
                             <li> <router-link to="/repairordersAdd">添加报修</router-link></li>
  <li> <router-link to="/repairordersManage">管理报修</router-link></li>
  <li> <router-link to="/repairordersManage2">报修列表</router-link></li>

                        </ul>
                    </li>
       <li  class="nav-item pcoded-hasmenu">
                        <a href="#!" class="nav-link"><span class="pcoded-micon"><i class="feather icon-gitlab"></i></span><span class="pcoded-mtext">返校登记管理</span></a>
                        <ul class="pcoded-submenu">
                             <li> <router-link to="/registerinfoAdd">添加返校登记</router-link></li>
  <li> <router-link to="/registerinfoManage">管理返校登记</router-link></li>
  <li> <router-link to="/registerinfoManage2">返校登记列表</router-link></li>

                        </ul>
                    </li>
       <li  class="nav-item pcoded-hasmenu">
                        <a href="#!" class="nav-link"><span class="pcoded-micon"><i class="feather icon-gitlab"></i></span><span class="pcoded-mtext">宿管阿姨管理</span></a>
                        <ul class="pcoded-submenu">
                             <li> <router-link to="/hostessAdd">添加宿管阿姨</router-link></li>
  <li> <router-link to="/hostessManage">管理宿管阿姨</router-link></li>
  <li> <router-link to="/hostessInfo">修改个人信息</router-link></li>

                        </ul>
                    </li>
       <li  class="nav-item pcoded-hasmenu">
                        <a href="#!" class="nav-link"><span class="pcoded-micon"><i class="feather icon-gitlab"></i></span><span class="pcoded-mtext">宿舍管理</span></a>
                        <ul class="pcoded-submenu">
                             <li> <router-link to="/dormitoryAdd">添加宿舍</router-link></li>
  <li> <router-link to="/dormitoryManage">管理宿舍</router-link></li>

                        </ul>
                    </li>
       <li  class="nav-item pcoded-hasmenu">
                        <a href="#!" class="nav-link"><span class="pcoded-micon"><i class="feather icon-gitlab"></i></span><span class="pcoded-mtext">图表管理</span></a>
                        <ul class="pcoded-submenu">
                             <li> <router-link to="/total1">图表1</router-link></li>
  <li> <router-link to="/total2">图表2</router-link></li>
  <li> <router-link to="/total3">图表3</router-link></li>
  <li> <router-link to="/total4">图表4</router-link></li>

                        </ul>
                    </li>

          
                </ul>

                      <ul class="nav pcoded-inner-navbar" v-show="role == '宿管阿姨'">
                    <li class="nav-item pcoded-menu-caption">
                        <label>功能菜单</label>
                    </li>
                         <li  class="nav-item pcoded-hasmenu">
                        <a href="#!" class="nav-link"><span class="pcoded-micon"><i class="feather icon-gitlab"></i></span><span class="pcoded-mtext">公告管理</span></a>
                        <ul class="pcoded-submenu">
                             <li> <router-link to="/systemnoticesAdd">添加公告</router-link></li>
  <li> <router-link to="/systemnoticesManage">管理公告</router-link></li>
  <li> <router-link to="/systemnoticesManage2">公告列表</router-link></li>

                        </ul>
                    </li>
       <li  class="nav-item pcoded-hasmenu">
                        <a href="#!" class="nav-link"><span class="pcoded-micon"><i class="feather icon-gitlab"></i></span><span class="pcoded-mtext">维修员管理</span></a>
                        <ul class="pcoded-submenu">
                             <li> <router-link to="/repairmenAdd">添加维修员</router-link></li>
  <li> <router-link to="/repairmenManage">管理维修员</router-link></li>
  <li> <router-link to="/repairmenInfo">修改个人信息</router-link></li>

                        </ul>
                    </li>
       <li  class="nav-item pcoded-hasmenu">
                        <a href="#!" class="nav-link"><span class="pcoded-micon"><i class="feather icon-gitlab"></i></span><span class="pcoded-mtext">离校登记管理</span></a>
                        <ul class="pcoded-submenu">
                             <li> <router-link to="/leaveschoolAdd">添加离校登记</router-link></li>
  <li> <router-link to="/leaveschoolManage">管理离校登记</router-link></li>
  <li> <router-link to="/leaveschoolManage2">离校登记列表</router-link></li>

                        </ul>
                    </li>
       <li  class="nav-item pcoded-hasmenu">
                        <a href="#!" class="nav-link"><span class="pcoded-micon"><i class="feather icon-gitlab"></i></span><span class="pcoded-mtext">宿舍评分管理</span></a>
                        <ul class="pcoded-submenu">
                             <li> <router-link to="/dormitoryscoreAdd">添加宿舍评分</router-link></li>
  <li> <router-link to="/dormitoryscoreManage">管理宿舍评分</router-link></li>
  <li> <router-link to="/dormitoryscoreManage2">宿舍评分列表</router-link></li>

                        </ul>
                    </li>
       <li  class="nav-item pcoded-hasmenu">
                        <a href="#!" class="nav-link"><span class="pcoded-micon"><i class="feather icon-gitlab"></i></span><span class="pcoded-mtext">宿舍更换管理</span></a>
                        <ul class="pcoded-submenu">
                             <li> <router-link to="/dormitorychangeAdd">添加宿舍更换</router-link></li>
  <li> <router-link to="/dormitorychangeManage">管理宿舍更换</router-link></li>
  <li> <router-link to="/dormitorychangeManage2">宿舍更换列表</router-link></li>

                        </ul>
                    </li>
       <li  class="nav-item pcoded-hasmenu">
                        <a href="#!" class="nav-link"><span class="pcoded-micon"><i class="feather icon-gitlab"></i></span><span class="pcoded-mtext">水电费管理</span></a>
                        <ul class="pcoded-submenu">
                             <li> <router-link to="/waterelectricityfeeAdd">添加水电费</router-link></li>
  <li> <router-link to="/waterelectricityfeeManage">管理水电费</router-link></li>
  <li> <router-link to="/waterelectricityfeeManage2">水电费列表</router-link></li>

                        </ul>
                    </li>
       <li  class="nav-item pcoded-hasmenu">
                        <a href="#!" class="nav-link"><span class="pcoded-micon"><i class="feather icon-gitlab"></i></span><span class="pcoded-mtext">报修类型管理</span></a>
                        <ul class="pcoded-submenu">
                             <li> <router-link to="/repairtypeAdd">添加报修类型</router-link></li>
  <li> <router-link to="/repairtypeManage">管理报修类型</router-link></li>

                        </ul>
                    </li>
       <li  class="nav-item pcoded-hasmenu">
                        <a href="#!" class="nav-link"><span class="pcoded-micon"><i class="feather icon-gitlab"></i></span><span class="pcoded-mtext">宿舍楼管理</span></a>
                        <ul class="pcoded-submenu">
                             <li> <router-link to="/dormbuildingAdd">添加宿舍楼</router-link></li>
  <li> <router-link to="/dormbuildingManage">管理宿舍楼</router-link></li>

                        </ul>
                    </li>
       <li  class="nav-item pcoded-hasmenu">
                        <a href="#!" class="nav-link"><span class="pcoded-micon"><i class="feather icon-gitlab"></i></span><span class="pcoded-mtext">学生管理</span></a>
                        <ul class="pcoded-submenu">
                             <li> <router-link to="/studentAdd">添加学生</router-link></li>
  <li> <router-link to="/studentManage">管理学生</router-link></li>
  <li> <router-link to="/studentInfo">修改个人信息</router-link></li>

                        </ul>
                    </li>
       <li  class="nav-item pcoded-hasmenu">
                        <a href="#!" class="nav-link"><span class="pcoded-micon"><i class="feather icon-gitlab"></i></span><span class="pcoded-mtext">报修管理</span></a>
                        <ul class="pcoded-submenu">
                             <li> <router-link to="/repairordersAdd">添加报修</router-link></li>
  <li> <router-link to="/repairordersManage">管理报修</router-link></li>
  <li> <router-link to="/repairordersManage2">报修列表</router-link></li>

                        </ul>
                    </li>
       <li  class="nav-item pcoded-hasmenu">
                        <a href="#!" class="nav-link"><span class="pcoded-micon"><i class="feather icon-gitlab"></i></span><span class="pcoded-mtext">返校登记管理</span></a>
                        <ul class="pcoded-submenu">
                             <li> <router-link to="/registerinfoAdd">添加返校登记</router-link></li>
  <li> <router-link to="/registerinfoManage">管理返校登记</router-link></li>
  <li> <router-link to="/registerinfoManage2">返校登记列表</router-link></li>

                        </ul>
                    </li>
       <li  class="nav-item pcoded-hasmenu">
                        <a href="#!" class="nav-link"><span class="pcoded-micon"><i class="feather icon-gitlab"></i></span><span class="pcoded-mtext">宿管阿姨管理</span></a>
                        <ul class="pcoded-submenu">
                             <li> <router-link to="/hostessAdd">添加宿管阿姨</router-link></li>
  <li> <router-link to="/hostessManage">管理宿管阿姨</router-link></li>
  <li> <router-link to="/hostessInfo">修改个人信息</router-link></li>

                        </ul>
                    </li>
       <li  class="nav-item pcoded-hasmenu">
                        <a href="#!" class="nav-link"><span class="pcoded-micon"><i class="feather icon-gitlab"></i></span><span class="pcoded-mtext">宿舍管理</span></a>
                        <ul class="pcoded-submenu">
                             <li> <router-link to="/dormitoryAdd">添加宿舍</router-link></li>
  <li> <router-link to="/dormitoryManage">管理宿舍</router-link></li>

                        </ul>
                    </li>
       <li  class="nav-item pcoded-hasmenu">
                        <a href="#!" class="nav-link"><span class="pcoded-micon"><i class="feather icon-gitlab"></i></span><span class="pcoded-mtext">图表管理</span></a>
                        <ul class="pcoded-submenu">
                             <li> <router-link to="/total1">图表1</router-link></li>
  <li> <router-link to="/total2">图表2</router-link></li>
  <li> <router-link to="/total3">图表3</router-link></li>
  <li> <router-link to="/total4">图表4</router-link></li>

                        </ul>
                    </li>

          
                </ul>


            </div>
        </div>
    </nav>
    

</template>


<script>
import $ from 'jquery';

export default {
  name: "LeftMenu",
  data() {
    return {
      userLname: "",
      role: "",
      activeMenu: null, // 用于跟踪当前激活的菜单
    };
  },
  watch: {
    $route(to, from) {
      this.activeMenu = to.name;
      this.$nextTick(() => {
        this.initializeMenu();
      });
    }
  },
  mounted() {
    this.userLname = sessionStorage.getItem("userLname");
    this.role = sessionStorage.getItem("role");
    this.$nextTick(() => {
      this.initializeMenu();
    });
  },
  methods: {
    initializeMenu() {
      $('.nav-item.pcoded-hasmenu > a').off('click').on('click', function(e) {
        e.preventDefault();
        const $parent = $(this).parent();

        if ($parent.hasClass('pcoded-trigger')) {
          $parent.removeClass('pcoded-trigger');
          $parent.children('ul').slideUp();
        } else {
          $('.nav-item.pcoded-hasmenu').removeClass('pcoded-trigger');
          $('.nav-item.pcoded-hasmenu > ul').slideUp();
          $parent.addClass('pcoded-trigger');
          $parent.children('ul').slideDown();
        }
      });

      // 初始化：根据当前路由展开对应的菜单
      const currentPath = this.$route.path;
      $('.pcoded-submenu a').each(function() {
        if ($(this).attr('href') === currentPath) {
          $(this).parents('.nav-item.pcoded-hasmenu').addClass('pcoded-trigger');
          $(this).parents('ul.pcoded-submenu').show();
        }
      });
    },

    exit: function () {
      var _this = this;
      this.$confirm("确认退出吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
          .then(() => {
            sessionStorage.removeItem("userLname");
            sessionStorage.removeItem("role");
            _this.$router.push("/");
          })
          .catch(() => { });
    },
  }
};
</script>

<style scoped>
.example-showcase .el-dropdown-link {
  cursor: pointer;
  color:green;
  display: flex;
  align-items: center;
}

.pcoded-submenu a{
  text-decoration: none;
  font-size: 14px;
}

/*加点击效果*/
.pcoded-submenu a:hover{
  color: #fff;
  color: #ff6600;
}
</style>

