{"remainingRequest": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\B7839DormManager\\DormManager-web\\src\\components\\LeftMenu.vue?vue&type=template&id=edc10994&scoped=true", "dependencies": [{"path": "I:\\product4\\B7839DormManager\\DormManager-web\\src\\components\\LeftMenu.vue", "mtime": 1749046536848}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749043011899}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749043012919}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749043013784}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749043011899}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749043013130}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["I:\\product4\\B7839DormManager\\DormManager-web\\src\\components\\LeftMenu.vue"], "names": [], "mappings": ";CACC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACjC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC3B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACb,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC3C,CAAC,CAAC,CAAC,CAAC,CAAC;oBACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACtC,CAAC,CAAC,CAAC;;YAEP,CAAC,CAAC,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAClC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;;sBAGpD,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAClC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC5I,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;iCACjB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC9F,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;6BACvC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACtF,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;wBAExC,CAAC,CAAC,CAAC,CAAC;oBACR,CAAC,CAAC,CAAC,CAAC;;yBAEC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACrC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC5I,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;6BACrB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACpF,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;wBAEtC,CAAC,CAAC,CAAC,CAAC;oBACR,CAAC,CAAC,CAAC,CAAC;;2BAEG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACvC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC9I,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;6BACrB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACtF,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;wBAExC,CAAC,CAAC,CAAC,CAAC;oBACR,CAAC,CAAC,CAAC,CAAC;;yBAEC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACrC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC7I,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;6BACrB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACvF,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;wBAEzC,CAAC,CAAC,CAAC,CAAC;oBACR,CAAC,CAAC,CAAC,CAAC;;;yBAGC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACrC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC5I,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;6BACrB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC1F,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;wBAE5C,CAAC,CAAC,CAAC,CAAC;oBACR,CAAC,CAAC,CAAC,CAAC;;0BAEE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACtC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC9I,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;6BACrB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC9F,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;wBAEhD,CAAC,CAAC,CAAC,CAAC;oBACR,CAAC,CAAC,CAAC,CAAC;;;OAGjB,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACnB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC5I,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;yBACzB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACrF,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACjE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACjE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;wBAE5C,CAAC,CAAC,CAAC,CAAC;oBACR,CAAC,CAAC,CAAC,CAAC;;;OAGjB,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACnB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC7I,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;6BACrB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACjG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACzE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;wBAEpD,CAAC,CAAC,CAAC,CAAC;oBACR,CAAC,CAAC,CAAC,CAAC;;;;OAIjB,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACnB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC9I,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;6BACrB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC1F,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;wBAE7C,CAAC,CAAC,CAAC,CAAC;oBACR,CAAC,CAAC,CAAC,CAAC;;;OAGjB,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACnB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC9I,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;6BACrB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC3F,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;wBAE9C,CAAC,CAAC,CAAC,CAAC;oBACR,CAAC,CAAC,CAAC,CAAC;;;OAGjB,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACnB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC9I,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;6BACrB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7F,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACrE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;wBAEhD,CAAC,CAAC,CAAC,CAAC;oBACR,CAAC,CAAC,CAAC,CAAC;;;;;OAKjB,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACnB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC5I,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;6BACrB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClF,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACxD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;;wBAGjC,CAAC,CAAC,CAAC,CAAC;oBACR,CAAC,CAAC,CAAC,CAAC;;sBAEF,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAClC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC5I,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEhD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;wBAEjC,CAAC,CAAC,CAAC,CAAC;oBACR,CAAC,CAAC,CAAC,CAAC;;;gBAGR,CAAC,CAAC,CAAC,CAAC;;sBAEE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC3D,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACtB,CAAC,CAAC,CAAC,CAAC;yBACC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACrC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC5I,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEhD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;wBAE7C,CAAC,CAAC,CAAC,CAAC;oBACR,CAAC,CAAC,CAAC,CAAC;;2BAEG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACvC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC9I,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;uBAC3B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;wBAEzD,CAAC,CAAC,CAAC,CAAC;oBACR,CAAC,CAAC,CAAC,CAAC;;;OAGjB,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACnB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC9I,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;6BACrB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC1F,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;wBAE5C,CAAC,CAAC,CAAC,CAAC;oBACR,CAAC,CAAC,CAAC,CAAC;;wBAEA,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACpC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC9I,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;6BACrB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC3F,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;wBAE7C,CAAC,CAAC,CAAC,CAAC;oBACR,CAAC,CAAC,CAAC,CAAC;;uBAED,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACnC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC5I,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;6BACrB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACzF,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;wBAE3C,CAAC,CAAC,CAAC,CAAC;oBACR,CAAC,CAAC,CAAC,CAAC;;wBAEA,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACpC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC7I,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEhD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;wBAElD,CAAC,CAAC,CAAC,CAAC;oBACR,CAAC,CAAC,CAAC,CAAC;;;;OAIjB,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACnB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC9I,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEhD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;wBAE9C,CAAC,CAAC,CAAC,CAAC;oBACR,CAAC,CAAC,CAAC,CAAC;;;;;;;0BAOE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACtC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC5I,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;6BACrB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACvF,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;wBAEjC,CAAC,CAAC,CAAC,CAAC;oBACR,CAAC,CAAC,CAAC,CAAC;;;gBAGR,CAAC,CAAC,CAAC,CAAC;;;sBAGE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC5D,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACtB,CAAC,CAAC,CAAC,CAAC;yBACC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACrC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC5I,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;6BACrB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC1F,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;wBAE7C,CAAC,CAAC,CAAC,CAAC;oBACR,CAAC,CAAC,CAAC,CAAC;OACjB,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACnB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC7I,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;6BACrB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACvF,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC/D,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;wBAExC,CAAC,CAAC,CAAC,CAAC;oBACR,CAAC,CAAC,CAAC,CAAC;OACjB,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACnB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC9I,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;6BACrB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC1F,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;wBAE7C,CAAC,CAAC,CAAC,CAAC;oBACR,CAAC,CAAC,CAAC,CAAC;OACjB,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACnB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC9I,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;6BACrB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7F,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACrE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;wBAEhD,CAAC,CAAC,CAAC,CAAC;oBACR,CAAC,CAAC,CAAC,CAAC;OACjB,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACnB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC9I,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;6BACrB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC9F,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACtE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;wBAEjD,CAAC,CAAC,CAAC,CAAC;oBACR,CAAC,CAAC,CAAC,CAAC;OACjB,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACnB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC7I,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;6BACrB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACjG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACzE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;wBAEpD,CAAC,CAAC,CAAC,CAAC;oBACR,CAAC,CAAC,CAAC,CAAC;OACjB,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACnB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC9I,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;6BACrB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACzF,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;wBAE3C,CAAC,CAAC,CAAC,CAAC;oBACR,CAAC,CAAC,CAAC,CAAC;OACjB,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACnB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC7I,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;6BACrB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC1F,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;wBAE5C,CAAC,CAAC,CAAC,CAAC;oBACR,CAAC,CAAC,CAAC,CAAC;OACjB,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACnB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC5I,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;6BACrB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACpF,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5D,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;wBAEtC,CAAC,CAAC,CAAC,CAAC;oBACR,CAAC,CAAC,CAAC,CAAC;OACjB,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACnB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC5I,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;6BACrB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACzF,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACjE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;wBAE5C,CAAC,CAAC,CAAC,CAAC;oBACR,CAAC,CAAC,CAAC,CAAC;OACjB,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACnB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC9I,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;6BACrB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC3F,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;wBAE9C,CAAC,CAAC,CAAC,CAAC;oBACR,CAAC,CAAC,CAAC,CAAC;OACjB,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACnB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC9I,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;6BACrB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACtF,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC9D,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;wBAEtC,CAAC,CAAC,CAAC,CAAC;oBACR,CAAC,CAAC,CAAC,CAAC;OACjB,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACnB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC5I,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;6BACrB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACtF,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;wBAExC,CAAC,CAAC,CAAC,CAAC;oBACR,CAAC,CAAC,CAAC,CAAC;OACjB,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACnB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC5I,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;6BACrB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC/E,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACpD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACpD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;wBAE9B,CAAC,CAAC,CAAC,CAAC;oBACR,CAAC,CAAC,CAAC,CAAC;;;gBAGR,CAAC,CAAC,CAAC,CAAC;;sBAEE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC7D,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACtB,CAAC,CAAC,CAAC,CAAC;yBACC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACrC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC5I,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEhD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;wBAE7C,CAAC,CAAC,CAAC,CAAC;oBACR,CAAC,CAAC,CAAC,CAAC;;2BAEG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACvC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC9I,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEhD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;;wBAGhD,CAAC,CAAC,CAAC,CAAC;oBACR,CAAC,CAAC,CAAC,CAAC;;OAEjB,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACnB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC9I,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEhD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;wBAE7C,CAAC,CAAC,CAAC,CAAC;oBACR,CAAC,CAAC,CAAC,CAAC;;0BAEE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACtC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC9I,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;6BACrB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC3F,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;wBAE9C,CAAC,CAAC,CAAC,CAAC;oBACR,CAAC,CAAC,CAAC,CAAC;;0BAEE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACtC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC7I,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;6BACrB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACjG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;wBAEnD,CAAC,CAAC,CAAC,CAAC;oBACR,CAAC,CAAC,CAAC,CAAC;;OAEjB,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACnB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC9I,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;6BACrB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7F,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;wBAE/C,CAAC,CAAC,CAAC,CAAC;oBACR,CAAC,CAAC,CAAC,CAAC;;;;;8CAKsB,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC1D,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC5I,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;mBAC/B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7E,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;wBAEjC,CAAC,CAAC,CAAC,CAAC;oBACR,CAAC,CAAC,CAAC,CAAC;;;;gBAIR,CAAC,CAAC,CAAC,CAAC;;;YAGR,CAAC,CAAC,CAAC,CAAC,CAAC;QACT,CAAC,CAAC,CAAC,CAAC,CAAC;IACT,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "I:/product4/B7839DormManager/DormManager-web/src/components/LeftMenu.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n <nav class=\"pcoded-navbar\">\r\n        <div class=\"navbar-wrapper\">\r\n            <div class=\"navbar-brand header-logo\">\r\n                <a href=\"/main\" class=\"b-brand\">\r\n                    <div class=\"b-bg\">\r\n                        <i class=\"feather icon-trending-up\"></i>\r\n                    </div>\r\n                    <span class=\"b-title\">宿舍管理系统</span>\r\n                </a>\r\n\r\n            </div>\r\n            <div class=\"navbar-content scroll-div\">\r\n                <ul class=\"nav pcoded-inner-navbar\" v-show=\"role == '管理员'\">\r\n                 \r\n                   \r\n                      <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿舍管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                                 <li> <router-link to=\"/dormbuildingAdd\">添加宿舍楼</router-link></li>\r\n  <li> <router-link to=\"/dormbuildingManage\">管理宿舍楼</router-link></li>\r\n                             <li> <router-link to=\"/dormitoryAdd\">添加宿舍</router-link></li>\r\n  <li> <router-link to=\"/dormitoryManage\">管理宿舍</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                         <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">学生管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/studentAdd\">添加学生</router-link></li>\r\n  <li> <router-link to=\"/studentManage\">管理学生</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                           <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿管阿姨管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/hostessAdd\">添加宿管阿姨</router-link></li>\r\n  <li> <router-link to=\"/hostessManage\">管理宿管阿姨</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                         <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">维修员管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/repairmenAdd\">添加维修员</router-link></li>\r\n  <li> <router-link to=\"/repairmenManage\">管理维修员</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n\r\n                         <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">公告管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/systemnoticesAdd\">添加公告</router-link></li>\r\n  <li> <router-link to=\"/systemnoticesManage\">管理公告</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                          <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿舍更换管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/dormitorychangeAdd\">添加宿舍更换</router-link></li>\r\n  <li> <router-link to=\"/dormitorychangeManage\">管理宿舍更换</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                     \r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">报修管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                         <li> <router-link to=\"/repairtypeAdd\">添加报修类型</router-link></li>\r\n  <li> <router-link to=\"/repairtypeManage\">管理报修类型</router-link></li>\r\n  <li> <router-link to=\"/repairordersManage\">管理报修</router-link></li>\r\n  <li> <router-link to=\"/repairordersManage2\">报修列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                    \r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">水电费管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/waterelectricityfeeAdd\">添加水电费</router-link></li>\r\n  <li> <router-link to=\"/waterelectricityfeeManage\">管理水电费</router-link></li>\r\n  <li> <router-link to=\"/waterelectricityfeeManage2\">水电费列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n    \r\n\r\n  \r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">离校登记管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/leaveschoolAdd\">添加离校登记</router-link></li>\r\n  <li> <router-link to=\"/leaveschoolManage\">管理离校登记</router-link></li>\r\n  <li> <router-link to=\"/leaveschoolManage2\">离校登记列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                     \r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">返校登记管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/registerinfoAdd\">添加返校登记</router-link></li>\r\n  <li> <router-link to=\"/registerinfoManage\">管理返校登记</router-link></li>\r\n  <li> <router-link to=\"/registerinfoManage2\">返校登记列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿舍评分管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/dormitoryscoreAdd\">添加宿舍评分</router-link></li>\r\n  <li> <router-link to=\"/dormitoryscoreManage\">管理宿舍评分</router-link></li>\r\n  <li> <router-link to=\"/dormitoryscoreManage2\">宿舍评分列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n \r\n    \r\n\r\n     \r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">统计报表</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/total1\">报修类型统计</router-link></li>\r\n  <li> <router-link to=\"/total2\">维修员维修统计</router-link></li>\r\n  <li> <router-link to=\"/total3\">宿舍评分统计</router-link></li>\r\n\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                      <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">系统管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                           \r\n  <li> <router-link to=\"/password\">修改密码</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n          \r\n                </ul>\r\n\r\n                      <ul class=\"nav pcoded-inner-navbar\" v-show=\"role == '学生'\">\r\n                    <li class=\"nav-item pcoded-menu-caption\">\r\n                        <label>功能菜单</label>\r\n                    </li>\r\n                         <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">公告管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                         \r\n  <li> <router-link to=\"/systemnoticesManage2\">公告列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                           <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿舍更换管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                       <li> <router-link to=\"/mydormitory\">我的宿舍</router-link></li> \r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">离校登记管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/leaveschoolAdd\">添加离校登记</router-link></li>\r\n  <li> <router-link to=\"/leaveschoolManage\">我的离校登记</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                        <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">返校登记管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/registerinfoAdd\">添加返校登记</router-link></li>\r\n  <li> <router-link to=\"/registerinfoManage\">我的返校登记</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">报修管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/repairordersAdd\">在线报修</router-link></li>\r\n  <li> <router-link to=\"/repairordersManage\">我的报修</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                        <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">水电费管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n          \r\n  <li> <router-link to=\"/waterelectricityfeeManage2\">水电费</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n     \r\n     \r\n\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿舍评分管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                    \r\n  <li> <router-link to=\"/dormitoryscoreManage2\">宿舍评分</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n   \r\n \r\n    \r\n   \r\n\r\n                          <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">个人中心</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/studentInfo\">修改个人信息</router-link></li>\r\n  <li> <router-link to=\"/password\">修改密码</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n          \r\n                </ul>\r\n\r\n\r\n                      <ul class=\"nav pcoded-inner-navbar\" v-show=\"role == '维修员'\">\r\n                    <li class=\"nav-item pcoded-menu-caption\">\r\n                        <label>功能菜单</label>\r\n                    </li>\r\n                         <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">公告管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/systemnoticesAdd\">添加公告</router-link></li>\r\n  <li> <router-link to=\"/systemnoticesManage\">管理公告</router-link></li>\r\n  <li> <router-link to=\"/systemnoticesManage2\">公告列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">维修员管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/repairmenAdd\">添加维修员</router-link></li>\r\n  <li> <router-link to=\"/repairmenManage\">管理维修员</router-link></li>\r\n  <li> <router-link to=\"/repairmenInfo\">修改个人信息</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">离校登记管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/leaveschoolAdd\">添加离校登记</router-link></li>\r\n  <li> <router-link to=\"/leaveschoolManage\">管理离校登记</router-link></li>\r\n  <li> <router-link to=\"/leaveschoolManage2\">离校登记列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿舍评分管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/dormitoryscoreAdd\">添加宿舍评分</router-link></li>\r\n  <li> <router-link to=\"/dormitoryscoreManage\">管理宿舍评分</router-link></li>\r\n  <li> <router-link to=\"/dormitoryscoreManage2\">宿舍评分列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿舍更换管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/dormitorychangeAdd\">添加宿舍更换</router-link></li>\r\n  <li> <router-link to=\"/dormitorychangeManage\">管理宿舍更换</router-link></li>\r\n  <li> <router-link to=\"/dormitorychangeManage2\">宿舍更换列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">水电费管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/waterelectricityfeeAdd\">添加水电费</router-link></li>\r\n  <li> <router-link to=\"/waterelectricityfeeManage\">管理水电费</router-link></li>\r\n  <li> <router-link to=\"/waterelectricityfeeManage2\">水电费列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">报修类型管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/repairtypeAdd\">添加报修类型</router-link></li>\r\n  <li> <router-link to=\"/repairtypeManage\">管理报修类型</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿舍楼管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/dormbuildingAdd\">添加宿舍楼</router-link></li>\r\n  <li> <router-link to=\"/dormbuildingManage\">管理宿舍楼</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">学生管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/studentAdd\">添加学生</router-link></li>\r\n  <li> <router-link to=\"/studentManage\">管理学生</router-link></li>\r\n  <li> <router-link to=\"/studentInfo\">修改个人信息</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">报修管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/repairordersAdd\">添加报修</router-link></li>\r\n  <li> <router-link to=\"/repairordersManage\">管理报修</router-link></li>\r\n  <li> <router-link to=\"/repairordersManage2\">报修列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">返校登记管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/registerinfoAdd\">添加返校登记</router-link></li>\r\n  <li> <router-link to=\"/registerinfoManage\">管理返校登记</router-link></li>\r\n  <li> <router-link to=\"/registerinfoManage2\">返校登记列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿管阿姨管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/hostessAdd\">添加宿管阿姨</router-link></li>\r\n  <li> <router-link to=\"/hostessManage\">管理宿管阿姨</router-link></li>\r\n  <li> <router-link to=\"/hostessInfo\">修改个人信息</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿舍管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/dormitoryAdd\">添加宿舍</router-link></li>\r\n  <li> <router-link to=\"/dormitoryManage\">管理宿舍</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">图表管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/total1\">图表1</router-link></li>\r\n  <li> <router-link to=\"/total2\">图表2</router-link></li>\r\n  <li> <router-link to=\"/total3\">图表3</router-link></li>\r\n  <li> <router-link to=\"/total4\">图表4</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n          \r\n                </ul>\r\n\r\n                      <ul class=\"nav pcoded-inner-navbar\" v-show=\"role == '宿管阿姨'\">\r\n                    <li class=\"nav-item pcoded-menu-caption\">\r\n                        <label>功能菜单</label>\r\n                    </li>\r\n                         <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">公告管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                        \r\n  <li> <router-link to=\"/systemnoticesManage2\">公告列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                           <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿舍更换管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                 \r\n  <li> <router-link to=\"/dormitorychangeManage\">管理宿舍更换</router-link></li>\r\n\r\n\r\n                        </ul>\r\n                    </li>\r\n   \r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">离校登记管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                         \r\n  <li> <router-link to=\"/leaveschoolManage\">管理离校登记</router-link></li>\r\n  <li> <router-link to=\"/leaveschoolManage2\">离校登记列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                          <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">返校登记管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/registerinfoAdd\">添加返校登记</router-link></li>\r\n  <li> <router-link to=\"/registerinfoManage\">管理返校登记</router-link></li>\r\n  <li> <router-link to=\"/registerinfoManage2\">返校登记列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                          <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">水电费管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/waterelectricityfeeAdd\">添加水电费</router-link></li>\r\n  <li> <router-link to=\"/waterelectricityfeeManage\">管理水电费</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿舍评分管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/dormitoryscoreAdd\">添加宿舍评分</router-link></li>\r\n  <li> <router-link to=\"/dormitoryscoreManage\">管理宿舍评分</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n \r\n \r\n     \r\n                                              <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">个人中心</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                   <li> <router-link to=\"/hostessInfo\">修改个人信息</router-link></li>\r\n  <li> <router-link to=\"/password\">修改密码</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n\r\n          \r\n                </ul>\r\n\r\n\r\n            </div>\r\n        </div>\r\n    </nav>\r\n    \r\n\r\n</template>\r\n\r\n\r\n<script>\r\nimport $ from 'jquery';\r\n\r\nexport default {\r\n  name: \"LeftMenu\",\r\n  data() {\r\n    return {\r\n      userLname: \"\",\r\n      role: \"\",\r\n      activeMenu: null, // 用于跟踪当前激活的菜单\r\n    };\r\n  },\r\n  watch: {\r\n    $route(to, from) {\r\n      this.activeMenu = to.name;\r\n      this.$nextTick(() => {\r\n        this.initializeMenu();\r\n      });\r\n    }\r\n  },\r\n  mounted() {\r\n    this.userLname = sessionStorage.getItem(\"userLname\");\r\n    this.role = sessionStorage.getItem(\"role\");\r\n    this.$nextTick(() => {\r\n      this.initializeMenu();\r\n    });\r\n  },\r\n  methods: {\r\n    initializeMenu() {\r\n      $('.nav-item.pcoded-hasmenu > a').off('click').on('click', function(e) {\r\n        e.preventDefault();\r\n        const $parent = $(this).parent();\r\n\r\n        if ($parent.hasClass('pcoded-trigger')) {\r\n          $parent.removeClass('pcoded-trigger');\r\n          $parent.children('ul').slideUp();\r\n        } else {\r\n          $('.nav-item.pcoded-hasmenu').removeClass('pcoded-trigger');\r\n          $('.nav-item.pcoded-hasmenu > ul').slideUp();\r\n          $parent.addClass('pcoded-trigger');\r\n          $parent.children('ul').slideDown();\r\n        }\r\n      });\r\n\r\n      // 初始化：根据当前路由展开对应的菜单\r\n      const currentPath = this.$route.path;\r\n      $('.pcoded-submenu a').each(function() {\r\n        if ($(this).attr('href') === currentPath) {\r\n          $(this).parents('.nav-item.pcoded-hasmenu').addClass('pcoded-trigger');\r\n          $(this).parents('ul.pcoded-submenu').show();\r\n        }\r\n      });\r\n    },\r\n\r\n    exit: function () {\r\n      var _this = this;\r\n      this.$confirm(\"确认退出吗?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n          .then(() => {\r\n            sessionStorage.removeItem(\"userLname\");\r\n            sessionStorage.removeItem(\"role\");\r\n            _this.$router.push(\"/\");\r\n          })\r\n          .catch(() => { });\r\n    },\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.example-showcase .el-dropdown-link {\r\n  cursor: pointer;\r\n  color:green;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.pcoded-submenu a{\r\n  text-decoration: none;\r\n  font-size: 14px;\r\n}\r\n\r\n/*加点击效果*/\r\n.pcoded-submenu a:hover{\r\n  color: #fff;\r\n  color: #ff6600;\r\n}\r\n</style>\r\n\r\n"]}]}