{"version": 3, "names": ["TopLevelOptions", "bugfixes", "config<PERSON><PERSON>", "corejs", "debug", "exclude", "forceAllTransforms", "ignoreBrowserslistConfig", "include", "loose", "modules", "shippedProposals", "spec", "targets", "useBuiltIns", "browserslistEnv", "exports", "ModulesOption", "false", "auto", "amd", "commonjs", "cjs", "systemjs", "umd", "UseBuiltInsOption", "entry", "usage"], "sources": ["../src/options.ts"], "sourcesContent": ["export const TopLevelOptions = {\n  bugfixes: \"bugfixes\",\n  configPath: \"configPath\",\n  corejs: \"corejs\",\n  debug: \"debug\",\n  exclude: \"exclude\",\n  forceAllTransforms: \"forceAllTransforms\",\n  ignoreBrowserslistConfig: \"ignoreBrowserslistConfig\",\n  include: \"include\",\n  loose: \"loose\",\n  modules: \"modules\",\n  shippedProposals: \"shippedProposals\",\n  spec: \"spec\",\n  targets: \"targets\",\n  useBuiltIns: \"useBuiltIns\",\n  browserslistEnv: \"browserslistEnv\",\n} as const;\n\nexport const ModulesOption = {\n  false: false,\n  auto: \"auto\",\n  amd: \"amd\",\n  commonjs: \"commonjs\",\n  cjs: \"cjs\",\n  systemjs: \"systemjs\",\n  umd: \"umd\",\n} as const;\n\nexport const UseBuiltInsOption = {\n  false: false,\n  entry: \"entry\",\n  usage: \"usage\",\n} as const;\n"], "mappings": ";;;;;;AAAO,MAAMA,eAAe,GAAG;EAC7BC,QAAQ,EAAE,UAAU;EACpBC,UAAU,EAAE,YAAY;EACxBC,MAAM,EAAE,QAAQ;EAChBC,KAAK,EAAE,OAAO;EACdC,OAAO,EAAE,SAAS;EAClBC,kBAAkB,EAAE,oBAAoB;EACxCC,wBAAwB,EAAE,0BAA0B;EACpDC,OAAO,EAAE,SAAS;EAClBC,KAAK,EAAE,OAAO;EACdC,OAAO,EAAE,SAAS;EAClBC,gBAAgB,EAAE,kBAAkB;EACpCC,IAAI,EAAE,MAAM;EACZC,OAAO,EAAE,SAAS;EAClBC,WAAW,EAAE,aAAa;EAC1BC,eAAe,EAAE;AACnB,CAAU;AAACC,OAAA,CAAAhB,eAAA,GAAAA,eAAA;AAEJ,MAAMiB,aAAa,GAAG;EAC3BC,KAAK,EAAE,KAAK;EACZC,IAAI,EAAE,MAAM;EACZC,GAAG,EAAE,KAAK;EACVC,QAAQ,EAAE,UAAU;EACpBC,GAAG,EAAE,KAAK;EACVC,QAAQ,EAAE,UAAU;EACpBC,GAAG,EAAE;AACP,CAAU;AAACR,OAAA,CAAAC,aAAA,GAAAA,aAAA;AAEJ,MAAMQ,iBAAiB,GAAG;EAC/BP,KAAK,EAAE,KAAK;EACZQ,KAAK,EAAE,OAAO;EACdC,KAAK,EAAE;AACT,CAAU;AAACX,OAAA,CAAAS,iBAAA,GAAAA,iBAAA"}