{"remainingRequest": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\admin\\dormitorychange\\DormitorychangeManage.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\admin\\dormitorychange\\DormitorychangeManage.vue", "mtime": 1749046404836}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\babel.config.js", "mtime": 1749034452000}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749043011899}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749043012919}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749043011899}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749043013130}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkucHVzaC5qcyI7CmltcG9ydCByZXF1ZXN0LCB7IGJhc2UgfSBmcm9tICIuLi8uLi8uLi8uLi91dGlscy9odHRwIjsKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdkb3JtaXRvcnljaGFuZ2UnLAogIGNvbXBvbmVudHM6IHt9LAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBmaWx0ZXJzOiB7CiAgICAgICAgLy/liJfooajmn6Xor6Llj4LmlbAKICAgICAgICBzbm86ICcnLAogICAgICAgIHJldmlld3N0YXR1czogJycKICAgICAgfSwKICAgICAgcGFnZTogewogICAgICAgIGN1cnJlbnRQYWdlOiAxLAogICAgICAgIC8vIOW9k+W<PERSON><PERSON>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"}, {"version": 3, "names": ["request", "base", "name", "components", "data", "filters", "sno", "reviewstatus", "page", "currentPage", "pageSize", "totalCount", "isClear", "listLoading", "btnLoading", "datalist", "reviewVisible", "reviewForm", "id", "dbname", "doro", "dbname2", "doro2", "<PERSON><PERSON><PERSON>", "reviewresponse", "reviewRules", "required", "message", "trigger", "created", "getDatas", "methods", "handleDelete", "index", "row", "$confirm", "confirmButtonText", "cancelButtonText", "type", "then", "url", "post", "res", "$message", "offset", "catch", "handleCurrentChange", "val", "para", "resdata", "length", "isPage", "count", "query", "handleShow", "$router", "push", "path", "handleEdit", "handleReview", "code", "dbid", "dbid2", "submitReview", "$refs", "reviewFormRef", "validate", "valid", "msg", "resetReviewForm", "resetFields", "getStatusType", "status"], "sources": ["I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\admin\\dormitorychange\\DormitorychangeManage.vue"], "sourcesContent": ["<template>\n    <div style=\"width: 100%;line-height: 30px;text-align: left;\">\n      <el-col  :span=\"24\"  style=\"padding-bottom: 0px; margin-left: 10px\">\n<el-form :inline=\"true\" :model=\"filters\" >\n<el-form-item>\n<el-input v-model=\"filters.sno\" placeholder=\"学号\"  size=\"small\"></el-input>\n</el-form-item>\n<el-form-item>\n<el-select v-model=\"filters.reviewstatus\" placeholder=\"审核状态\" size=\"small\" clearable>\n<el-option label=\"待审核\" value=\"待审核\"></el-option>\n<el-option label=\"审核通过\" value=\"审核通过\"></el-option>\n<el-option label=\"审核不通过\" value=\"审核不通过\"></el-option>\n</el-select>\n</el-form-item>\n<el-form-item>\n<el-button type=\"primary\" size=\"small\" @click=\"query\" icon=\"el-icon-search\">搜索</el-button>\n</el-form-item>\n </el-form>\n</el-col>\n\n<el-table :data=\"datalist\" border stripe style=\"width: 100%\"  v-loading=\"listLoading\"   highlight-current-row   max-height=\"600\"     size=\"small\">\n<el-table-column prop=\"sno\" label=\"学号\"  align=\"center\"></el-table-column>\n<el-table-column prop=\"dbid\" label=\"原宿舍楼id\"  align=\"center\"></el-table-column>\n<el-table-column prop=\"doro\" label=\"原宿舍编号\"  align=\"center\"></el-table-column>\n<el-table-column prop=\"dbid2\" label=\"更换宿舍楼id\"  align=\"center\"></el-table-column>\n<el-table-column prop=\"doro2\" label=\"更换宿舍编号\"  align=\"center\"></el-table-column>\n<el-table-column prop=\"applicationreason\" label=\"申请原因\"  align=\"center\">\n<template #default=\"scope\">\n<span v-if=\"scope.row.applicationreason != null\">{{scope.row.applicationreason.substring(0,20)}}</span>\n</template>\n</el-table-column>\n<el-table-column prop=\"submissiontime\" label=\"提交时间\"  align=\"center\"></el-table-column>\n<el-table-column prop=\"reviewstatus\" label=\"审核状态\"  align=\"center\">\n<template #default=\"scope\">\n<el-tag :type=\"getStatusType(scope.row.reviewstatus)\">{{ scope.row.reviewstatus }}</el-tag>\n</template>\n</el-table-column>\n<el-table-column prop=\"reviewresponse\" label=\"审核回复\"  align=\"center\">\n<template #default=\"scope\">\n<span v-if=\"scope.row.reviewresponse != null\">{{scope.row.reviewresponse.substring(0,20)}}</span>\n</template>\n</el-table-column>\n<el-table-column label=\"操作\" min-width=\"250\" align=\"center\">\n<template #default=\"scope\">\n<el-button v-if=\"scope.row.reviewstatus === '待审核'\" type=\"success\" size=\"mini\" @click=\"handleReview(scope.$index, scope.row)\" icon=\"el-icon-check\" style=\" padding: 3px 6px 3px 6px;\">审核</el-button>\n<el-button type=\"primary\" size=\"mini\" @click=\"handleShow(scope.$index, scope.row)\" icon=\"el-icon-zoom-in\" style=\" padding: 3px 6px 3px 6px;\">详情</el-button>\n<el-button type=\"danger\" size=\"mini\" @click=\"handleDelete(scope.$index, scope.row)\" icon=\"el-icon-delete\" style=\" padding: 3px 6px 3px 6px;\">删除</el-button>\n</template>\n</el-table-column>\n</el-table>\n<el-pagination  @current-change=\"handleCurrentChange\" :current-page=\"page.currentPage\" :page-size=\"page.pageSize\"\n background layout=\"total, prev, pager, next, jumper\" :total=\"page.totalCount\"\n style=\"float: right; margin: 10px 20px 0 0\"></el-pagination>\n\n<!-- 审核对话框 -->\n<el-dialog title=\"审核宿舍更换申请\" :visible.sync=\"reviewVisible\" width=\"600px\" @close=\"resetReviewForm\">\n  <el-form :model=\"reviewForm\" :rules=\"reviewRules\" ref=\"reviewFormRef\" label-width=\"120px\">\n    <el-form-item label=\"学号\">\n      <el-input v-model=\"reviewForm.sno\" disabled></el-input>\n    </el-form-item>\n    <el-form-item label=\"原宿舍\">\n      <el-input :value=\"reviewForm.dbname + ' - ' + reviewForm.doro\" disabled></el-input>\n    </el-form-item>\n    <el-form-item label=\"目标宿舍\">\n      <el-input :value=\"reviewForm.dbname2 + ' - ' + reviewForm.doro2\" disabled></el-input>\n    </el-form-item>\n    <el-form-item label=\"申请原因\">\n      <el-input type=\"textarea\" v-model=\"reviewForm.applicationreason\" :rows=\"3\" disabled></el-input>\n    </el-form-item>\n    <el-form-item label=\"审核结果\" prop=\"reviewstatus\">\n      <el-radio-group v-model=\"reviewForm.reviewstatus\">\n        <el-radio label=\"审核通过\">审核通过</el-radio>\n        <el-radio label=\"审核不通过\">审核不通过</el-radio>\n      </el-radio-group>\n    </el-form-item>\n    <el-form-item label=\"审核回复\" prop=\"reviewresponse\">\n      <el-input type=\"textarea\" v-model=\"reviewForm.reviewresponse\" placeholder=\"请输入审核回复\" :rows=\"4\"></el-input>\n    </el-form-item>\n  </el-form>\n  <div slot=\"footer\" class=\"dialog-footer\">\n    <el-button @click=\"reviewVisible = false\">取消</el-button>\n    <el-button type=\"primary\" @click=\"submitReview\" :loading=\"btnLoading\">确定</el-button>\n  </div>\n</el-dialog>\n\n    </div>\n</template>\n\n<script>\nimport request, { base } from \"../../../../utils/http\";\nexport default {\n  name: 'dormitorychange',\n  components: {\n    \n  },  \n    data() {\n      return {\n               filters: {\n          //列表查询参数\n          sno: '',\n          reviewstatus: '',\n        },\n\n        page: {\n          currentPage: 1, // 当前页\n          pageSize: 10, // 每页显示条目个数\n          totalCount: 0, // 总条目数\n        },\n        isClear: false,\n\n        listLoading: false, //列表加载状态\n        btnLoading: false, //保存按钮加载状态\n        datalist: [], //表格数据\n\n        // 审核相关\n        reviewVisible: false, // 审核对话框显示状态\n        reviewForm: {\n          id: null,\n          sno: '',\n          dbname: '',\n          doro: '',\n          dbname2: '',\n          doro2: '',\n          applicationreason: '',\n          reviewstatus: '',\n          reviewresponse: ''\n        },\n        reviewRules: {\n          reviewstatus: [{ required: true, message: '请选择审核结果', trigger: 'change' }],\n          reviewresponse: [{ required: true, message: '请输入审核回复', trigger: 'blur' }]\n        }\n\n      };\n    },\n    created() {\n      this.getDatas();\n    },\n\n \n    methods: {    \n\n              \n       // 删除宿舍更换\n        handleDelete(index, row) {\n          this.$confirm(\"确认删除该记录吗?\", \"提示\", {\n            confirmButtonText: \"确定\",\n            cancelButtonText: \"取消\",\n            type: \"warning\",\n          })\n            .then(() => {\n              this.listLoading = true;\n              let url = base + \"/dormitorychange/del?id=\" + row.id;\n              request.post(url).then((res) => {\n                this.listLoading = false;\n             \n                this.$message({\n                  message: \"删除成功\",\n                  type: \"success\",\n                  offset: 320,\n                });\n                this.getDatas();\n              });\n            })\n            .catch(() => { });\n        },\n                \n        // 分页\n        handleCurrentChange(val) {\n          this.page.currentPage = val;\n          this.getDatas();\n        },     \n     \n        //获取列表数据\n        getDatas() {\n          let para = {\n               sno:this.filters.sno,\n               reviewstatus:this.filters.reviewstatus,\n\n          };\n          this.listLoading = true;\n          let url = base + \"/dormitorychange/list?currentPage=\" + this.page.currentPage+ \"&pageSize=\" + this.page.pageSize;\n          request.post(url, para).then((res) => {\n            if (res.resdata.length > 0) {\n              this.isPage = true;\n            } else {\n              this.isPage = false;\n            }\n            this.page.totalCount = res.count;\n            this.datalist = res.resdata;\n            this.listLoading = false;\n          });\n        },\n                 //查询\n        query() {\n          this.getDatas();\n        },  \n           \n        // 查看\n        handleShow(index, row) {\n          this.$router.push({\n            path: \"/DormitorychangeDetail\",\n             query: {\n                id: row.id,\n              },\n          });\n        },\n    \n        // 编辑\n        handleEdit(index, row) {\n          this.$router.push({\n            path: \"/DormitorychangeEdit\",\n             query: {\n                id: row.id,\n              },\n          });\n        },\n\n        // 审核\n        handleReview(index, row) {\n          // 获取详细信息\n          let url = base + \"/dormitorychange/get?id=\" + row.id;\n          request.post(url).then((res) => {\n            if (res.code == 200) {\n              this.reviewForm = {\n                id: res.resdata.id,\n                sno: res.resdata.sno,\n                dbname: res.resdata.dbname || '宿舍楼' + res.resdata.dbid,\n                doro: res.resdata.doro,\n                dbname2: res.resdata.dbname2 || '宿舍楼' + res.resdata.dbid2,\n                doro2: res.resdata.doro2,\n                applicationreason: res.resdata.applicationreason,\n                reviewstatus: '',\n                reviewresponse: ''\n              };\n              this.reviewVisible = true;\n            }\n          });\n        },\n\n        // 提交审核\n        submitReview() {\n          this.$refs.reviewFormRef.validate((valid) => {\n            if (valid) {\n              this.btnLoading = true;\n              let url = base + \"/dormitorychange/review\";\n              let para = {\n                id: this.reviewForm.id,\n                reviewstatus: this.reviewForm.reviewstatus,\n                reviewresponse: this.reviewForm.reviewresponse\n              };\n              request.post(url, para).then((res) => {\n                if (res.code == 200) {\n                  this.$message({\n                    message: \"审核成功\",\n                    type: \"success\"\n                  });\n                  this.reviewVisible = false;\n                  this.getDatas(); // 刷新列表\n                } else {\n                  this.$message({\n                    message: res.msg || \"审核失败\",\n                    type: \"error\"\n                  });\n                }\n                this.btnLoading = false;\n              }).catch(() => {\n                this.$message({\n                  message: \"审核失败\",\n                  type: \"error\"\n                });\n                this.btnLoading = false;\n              });\n            }\n          });\n        },\n\n        // 重置审核表单\n        resetReviewForm() {\n          if (this.$refs.reviewFormRef) {\n            this.$refs.reviewFormRef.resetFields();\n          }\n          this.reviewForm = {\n            id: null,\n            sno: '',\n            dbname: '',\n            doro: '',\n            dbname2: '',\n            doro2: '',\n            applicationreason: '',\n            reviewstatus: '',\n            reviewresponse: ''\n          };\n        },\n\n        // 获取状态标签类型\n        getStatusType(status) {\n          switch (status) {\n            case '待审核':\n              return 'warning';\n            case '审核通过':\n              return 'success';\n            case '审核不通过':\n              return 'danger';\n            default:\n              return 'info';\n          }\n        }\n      },\n}\n\n</script>\n<style scoped>\n</style>\n \n\n"], "mappings": ";AAyFA,OAAOA,OAAO,IAAIC,IAAG,QAAS,wBAAwB;AACtD,eAAe;EACbC,IAAI,EAAE,iBAAiB;EACvBC,UAAU,EAAE,CAEZ,CAAC;EACCC,IAAIA,CAAA,EAAG;IACL,OAAO;MACEC,OAAO,EAAE;QACd;QACAC,GAAG,EAAE,EAAE;QACPC,YAAY,EAAE;MAChB,CAAC;MAEDC,IAAI,EAAE;QACJC,WAAW,EAAE,CAAC;QAAE;QAChBC,QAAQ,EAAE,EAAE;QAAE;QACdC,UAAU,EAAE,CAAC,CAAE;MACjB,CAAC;;MACDC,OAAO,EAAE,KAAK;MAEdC,WAAW,EAAE,KAAK;MAAE;MACpBC,UAAU,EAAE,KAAK;MAAE;MACnBC,QAAQ,EAAE,EAAE;MAAE;;MAEd;MACAC,aAAa,EAAE,KAAK;MAAE;MACtBC,UAAU,EAAE;QACVC,EAAE,EAAE,IAAI;QACRZ,GAAG,EAAE,EAAE;QACPa,MAAM,EAAE,EAAE;QACVC,IAAI,EAAE,EAAE;QACRC,OAAO,EAAE,EAAE;QACXC,KAAK,EAAE,EAAE;QACTC,iBAAiB,EAAE,EAAE;QACrBhB,YAAY,EAAE,EAAE;QAChBiB,cAAc,EAAE;MAClB,CAAC;MACDC,WAAW,EAAE;QACXlB,YAAY,EAAE,CAAC;UAAEmB,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAS,CAAC,CAAC;QACzEJ,cAAc,EAAE,CAAC;UAAEE,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAO,CAAC;MAC1E;IAEF,CAAC;EACH,CAAC;EACDC,OAAOA,CAAA,EAAG;IACR,IAAI,CAACC,QAAQ,CAAC,CAAC;EACjB,CAAC;EAGDC,OAAO,EAAE;IAGN;IACCC,YAAYA,CAACC,KAAK,EAAEC,GAAG,EAAE;MACvB,IAAI,CAACC,QAAQ,CAAC,WAAW,EAAE,IAAI,EAAE;QAC/BC,iBAAiB,EAAE,IAAI;QACvBC,gBAAgB,EAAE,IAAI;QACtBC,IAAI,EAAE;MACR,CAAC,EACEC,IAAI,CAAC,MAAM;QACV,IAAI,CAAC1B,WAAU,GAAI,IAAI;QACvB,IAAI2B,GAAE,GAAIvC,IAAG,GAAI,0BAAyB,GAAIiC,GAAG,CAAChB,EAAE;QACpDlB,OAAO,CAACyC,IAAI,CAACD,GAAG,CAAC,CAACD,IAAI,CAAEG,GAAG,IAAK;UAC9B,IAAI,CAAC7B,WAAU,GAAI,KAAK;UAExB,IAAI,CAAC8B,QAAQ,CAAC;YACZhB,OAAO,EAAE,MAAM;YACfW,IAAI,EAAE,SAAS;YACfM,MAAM,EAAE;UACV,CAAC,CAAC;UACF,IAAI,CAACd,QAAQ,CAAC,CAAC;QACjB,CAAC,CAAC;MACJ,CAAC,EACAe,KAAK,CAAC,MAAM,CAAE,CAAC,CAAC;IACrB,CAAC;IAED;IACAC,mBAAmBA,CAACC,GAAG,EAAE;MACvB,IAAI,CAACvC,IAAI,CAACC,WAAU,GAAIsC,GAAG;MAC3B,IAAI,CAACjB,QAAQ,CAAC,CAAC;IACjB,CAAC;IAED;IACAA,QAAQA,CAAA,EAAG;MACT,IAAIkB,IAAG,GAAI;QACN1C,GAAG,EAAC,IAAI,CAACD,OAAO,CAACC,GAAG;QACpBC,YAAY,EAAC,IAAI,CAACF,OAAO,CAACE;MAE/B,CAAC;MACD,IAAI,CAACM,WAAU,GAAI,IAAI;MACvB,IAAI2B,GAAE,GAAIvC,IAAG,GAAI,oCAAmC,GAAI,IAAI,CAACO,IAAI,CAACC,WAAW,GAAE,YAAW,GAAI,IAAI,CAACD,IAAI,CAACE,QAAQ;MAChHV,OAAO,CAACyC,IAAI,CAACD,GAAG,EAAEQ,IAAI,CAAC,CAACT,IAAI,CAAEG,GAAG,IAAK;QACpC,IAAIA,GAAG,CAACO,OAAO,CAACC,MAAK,GAAI,CAAC,EAAE;UAC1B,IAAI,CAACC,MAAK,GAAI,IAAI;QACpB,OAAO;UACL,IAAI,CAACA,MAAK,GAAI,KAAK;QACrB;QACA,IAAI,CAAC3C,IAAI,CAACG,UAAS,GAAI+B,GAAG,CAACU,KAAK;QAChC,IAAI,CAACrC,QAAO,GAAI2B,GAAG,CAACO,OAAO;QAC3B,IAAI,CAACpC,WAAU,GAAI,KAAK;MAC1B,CAAC,CAAC;IACJ,CAAC;IACQ;IACTwC,KAAKA,CAAA,EAAG;MACN,IAAI,CAACvB,QAAQ,CAAC,CAAC;IACjB,CAAC;IAED;IACAwB,UAAUA,CAACrB,KAAK,EAAEC,GAAG,EAAE;MACrB,IAAI,CAACqB,OAAO,CAACC,IAAI,CAAC;QAChBC,IAAI,EAAE,wBAAwB;QAC7BJ,KAAK,EAAE;UACJnC,EAAE,EAAEgB,GAAG,CAAChB;QACV;MACJ,CAAC,CAAC;IACJ,CAAC;IAED;IACAwC,UAAUA,CAACzB,KAAK,EAAEC,GAAG,EAAE;MACrB,IAAI,CAACqB,OAAO,CAACC,IAAI,CAAC;QAChBC,IAAI,EAAE,sBAAsB;QAC3BJ,KAAK,EAAE;UACJnC,EAAE,EAAEgB,GAAG,CAAChB;QACV;MACJ,CAAC,CAAC;IACJ,CAAC;IAED;IACAyC,YAAYA,CAAC1B,KAAK,EAAEC,GAAG,EAAE;MACvB;MACA,IAAIM,GAAE,GAAIvC,IAAG,GAAI,0BAAyB,GAAIiC,GAAG,CAAChB,EAAE;MACpDlB,OAAO,CAACyC,IAAI,CAACD,GAAG,CAAC,CAACD,IAAI,CAAEG,GAAG,IAAK;QAC9B,IAAIA,GAAG,CAACkB,IAAG,IAAK,GAAG,EAAE;UACnB,IAAI,CAAC3C,UAAS,GAAI;YAChBC,EAAE,EAAEwB,GAAG,CAACO,OAAO,CAAC/B,EAAE;YAClBZ,GAAG,EAAEoC,GAAG,CAACO,OAAO,CAAC3C,GAAG;YACpBa,MAAM,EAAEuB,GAAG,CAACO,OAAO,CAAC9B,MAAK,IAAK,KAAI,GAAIuB,GAAG,CAACO,OAAO,CAACY,IAAI;YACtDzC,IAAI,EAAEsB,GAAG,CAACO,OAAO,CAAC7B,IAAI;YACtBC,OAAO,EAAEqB,GAAG,CAACO,OAAO,CAAC5B,OAAM,IAAK,KAAI,GAAIqB,GAAG,CAACO,OAAO,CAACa,KAAK;YACzDxC,KAAK,EAAEoB,GAAG,CAACO,OAAO,CAAC3B,KAAK;YACxBC,iBAAiB,EAAEmB,GAAG,CAACO,OAAO,CAAC1B,iBAAiB;YAChDhB,YAAY,EAAE,EAAE;YAChBiB,cAAc,EAAE;UAClB,CAAC;UACD,IAAI,CAACR,aAAY,GAAI,IAAI;QAC3B;MACF,CAAC,CAAC;IACJ,CAAC;IAED;IACA+C,YAAYA,CAAA,EAAG;MACb,IAAI,CAACC,KAAK,CAACC,aAAa,CAACC,QAAQ,CAAEC,KAAK,IAAK;QAC3C,IAAIA,KAAK,EAAE;UACT,IAAI,CAACrD,UAAS,GAAI,IAAI;UACtB,IAAI0B,GAAE,GAAIvC,IAAG,GAAI,yBAAyB;UAC1C,IAAI+C,IAAG,GAAI;YACT9B,EAAE,EAAE,IAAI,CAACD,UAAU,CAACC,EAAE;YACtBX,YAAY,EAAE,IAAI,CAACU,UAAU,CAACV,YAAY;YAC1CiB,cAAc,EAAE,IAAI,CAACP,UAAU,CAACO;UAClC,CAAC;UACDxB,OAAO,CAACyC,IAAI,CAACD,GAAG,EAAEQ,IAAI,CAAC,CAACT,IAAI,CAAEG,GAAG,IAAK;YACpC,IAAIA,GAAG,CAACkB,IAAG,IAAK,GAAG,EAAE;cACnB,IAAI,CAACjB,QAAQ,CAAC;gBACZhB,OAAO,EAAE,MAAM;gBACfW,IAAI,EAAE;cACR,CAAC,CAAC;cACF,IAAI,CAACtB,aAAY,GAAI,KAAK;cAC1B,IAAI,CAACc,QAAQ,CAAC,CAAC,EAAE;YACnB,OAAO;cACL,IAAI,CAACa,QAAQ,CAAC;gBACZhB,OAAO,EAAEe,GAAG,CAAC0B,GAAE,IAAK,MAAM;gBAC1B9B,IAAI,EAAE;cACR,CAAC,CAAC;YACJ;YACA,IAAI,CAACxB,UAAS,GAAI,KAAK;UACzB,CAAC,CAAC,CAAC+B,KAAK,CAAC,MAAM;YACb,IAAI,CAACF,QAAQ,CAAC;cACZhB,OAAO,EAAE,MAAM;cACfW,IAAI,EAAE;YACR,CAAC,CAAC;YACF,IAAI,CAACxB,UAAS,GAAI,KAAK;UACzB,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;IACJ,CAAC;IAED;IACAuD,eAAeA,CAAA,EAAG;MAChB,IAAI,IAAI,CAACL,KAAK,CAACC,aAAa,EAAE;QAC5B,IAAI,CAACD,KAAK,CAACC,aAAa,CAACK,WAAW,CAAC,CAAC;MACxC;MACA,IAAI,CAACrD,UAAS,GAAI;QAChBC,EAAE,EAAE,IAAI;QACRZ,GAAG,EAAE,EAAE;QACPa,MAAM,EAAE,EAAE;QACVC,IAAI,EAAE,EAAE;QACRC,OAAO,EAAE,EAAE;QACXC,KAAK,EAAE,EAAE;QACTC,iBAAiB,EAAE,EAAE;QACrBhB,YAAY,EAAE,EAAE;QAChBiB,cAAc,EAAE;MAClB,CAAC;IACH,CAAC;IAED;IACA+C,aAAaA,CAACC,MAAM,EAAE;MACpB,QAAQA,MAAM;QACZ,KAAK,KAAK;UACR,OAAO,SAAS;QAClB,KAAK,MAAM;UACT,OAAO,SAAS;QAClB,KAAK,OAAO;UACV,OAAO,QAAQ;QACjB;UACE,OAAO,MAAM;MACjB;IACF;EACF;AACN"}]}