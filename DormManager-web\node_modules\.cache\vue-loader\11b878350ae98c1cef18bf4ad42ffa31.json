{"remainingRequest": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\admin\\leaveschool\\LeaveschoolDetail.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\admin\\leaveschool\\LeaveschoolDetail.vue", "mtime": 1749034452000}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749043011899}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749043012919}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749043011899}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749043013130}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQogICAgICAgIAogICAgICAgIGltcG9ydCByZXF1ZXN0LCB7IGJhc2UgfSBmcm9tICIuLi8uLi8uLi8uLi91dGlscy9odHRwIjsKICAgICAgICBleHBvcnQgZGVmYXVsdCB7CiAgICAgICAgICAgIG5hbWU6ICdMZWF2ZXNjaG9vbERldGFpbCcsCiAgICAgICAgICAgIGNvbXBvbmVudHM6IHsKICAgICAgICAgICAgfSwKICAgICAgICAgICAgZGF0YSgpIHsKICAgICAgICAgICAgICAgIHJldHVybiB7CiAgICAgICAgICAgICAgICAgICAgaWQ6ICcnLAogICAgICAgICAgICAgICAgICAgIGZvcm1EYXRhOiB7fSwgLy/ooajljZXmlbDmja4gICAgICAgICAKICAgICAgICAKICAgICAgICAgICAgICAgIH07CiAgICAgICAgICAgIH0sCiAgICAgICAgICAgIGNyZWF0ZWQoKSB7CiAgICAgICAgICAgICAgICB0aGlzLmlkID0gdGhpcy4kcm91dGUucXVlcnkuaWQ7IC8v6I635Y+W5Y+C5pWwCiAgICAgICAgICAgICAgICB0aGlzLmdldERhdGFzKCk7CiAgICAgICAgICAgIH0sCiAgICAgICAgCiAgICAgICAgCiAgICAgICAgICAgIG1ldGhvZHM6IHsKICAgICAgICAKICAgICAgICAgICAgICAgIC8v6I635Y+W5YiX6KGo5pWw5o2uCiAgICAgICAgICAgICAgICBnZXREYXRhcygpIHsKICAgICAgICAgICAgICAgICAgICBsZXQgcGFyYSA9IHsKICAgICAgICAgICAgICAgICAgICB9OwogICAgICAgICAgICAgICAgICAgIHRoaXMubGlzdExvYWRpbmcgPSB0cnVlOwogICAgICAgICAgICAgICAgICAgIGxldCB1cmwgPSBiYXNlICsgIi9sZWF2ZXNjaG9vbC9nZXQ/aWQ9IiArIHRoaXMuaWQ7CiAgICAgICAgICAgICAgICAgICAgcmVxdWVzdC5wb3N0KHVybCwgcGFyYSkudGhlbigocmVzKSA9PiB7CiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuZm9ybURhdGEgPSBKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KHJlcy5yZXNkYXRhKSk7CiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMubGlzdExvYWRpbmcgPSBmYWxzZTsKICAgICAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICAgIH0sCiAgICAgICAgCiAgICAgICAgICAgICAgICAvLyDov5Tlm54KICAgICAgICAgICAgICAgIGJhY2soKSB7CiAgICAgICAgICAgICAgICAgICAgLy/ov5Tlm57kuIrkuIDpobUKICAgICAgICAgICAgICAgICAgICB0aGlzLiRyb3V0ZXIuZ28oLTEpOwogICAgICAgICAgICAgICAgfSwKICAgICAgICAKICAgICAgICAgICAgfSwKICAgICAgICB9DQoNCg=="}, {"version": 3, "sources": ["I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\admin\\leaveschool\\LeaveschoolDetail.vue"], "names": [], "mappings": ";;QAgCQ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACX,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACZ,CAAC;YACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;gBACH,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;oBACH,CAAC,CAAC,EAAE,CAAC,CAAC;oBACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;gBAEvB,CAAC;YACL,CAAC;YACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;gBACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;gBACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnB,CAAC;;;YAGD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;gBAEL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;oBACP,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE;oBACX,CAAC;oBACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;oBACvB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACjD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;wBAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACvD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC5B,CAAC,CAAC;gBACN,CAAC;;gBAED,CAAC,EAAE,CAAC;gBACJ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;oBACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACvB,CAAC;;YAEL,CAAC;QACL", "file": "I:/product4/B7839DormManager/DormManager-web/src/views/admin/leaveschool/LeaveschoolDetail.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n    <div style=\"width: 100%;line-height: 30px;text-align: left;\">\r\n       <el-form :model=\"formData\" label-width=\"20%\" align=\"left\">\r\n<el-form-item label=\"离校登记id\">\r\n{{formData.lid}}</el-form-item>\r\n<el-form-item label=\"学号\">\r\n{{formData.sno}}</el-form-item>\r\n<el-form-item label=\"离校日期\">\r\n{{formData.ldate}}</el-form-item>\r\n<el-form-item label=\"离校原因\">\r\n{{formData.reason}}</el-form-item>\r\n<el-form-item label=\"目的地\">\r\n{{formData.destination}}</el-form-item>\r\n<el-form-item label=\"具体说明\">\r\n{{formData.note}}</el-form-item>\r\n<el-form-item label=\"提交时间\">\r\n{{formData.submittime}}</el-form-item>\r\n<el-form-item label=\"审核状态\">\r\n{{formData.lflag}}</el-form-item>\r\n<el-form-item label=\"审核回复\">\r\n{{formData.reply}}</el-form-item>\r\n<el-form-item>\r\n<el-button type=\"info\" size=\"small\" @click=\"back\" icon=\"el-icon-back\">返 回</el-button>\r\n</el-form-item>\r\n</el-form>\r\n\r\n\r\n    </div>\r\n</template>\r\n\r\n<script>\r\n        \n        import request, { base } from \"../../../../utils/http\";\n        export default {\n            name: 'LeaveschoolDetail',\n            components: {\n            },\n            data() {\n                return {\n                    id: '',\n                    formData: {}, //表单数据         \n        \n                };\n            },\n            created() {\n                this.id = this.$route.query.id; //获取参数\n                this.getDatas();\n            },\n        \n        \n            methods: {\n        \n                //获取列表数据\n                getDatas() {\n                    let para = {\n                    };\n                    this.listLoading = true;\n                    let url = base + \"/leaveschool/get?id=\" + this.id;\n                    request.post(url, para).then((res) => {\n                        this.formData = JSON.parse(JSON.stringify(res.resdata));\n                        this.listLoading = false;\n                    });\n                },\n        \n                // 返回\n                back() {\n                    //返回上一页\n                    this.$router.go(-1);\n                },\n        \n            },\n        }\r\n\r\n</script>\r\n<style scoped>\r\n</style>\r\n \r\n\r\n"]}]}