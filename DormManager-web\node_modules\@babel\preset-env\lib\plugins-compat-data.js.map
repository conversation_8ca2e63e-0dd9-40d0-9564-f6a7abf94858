{"version": 3, "names": ["_plugins", "require", "_pluginBugfixes", "_overlappingPlugins", "_availablePlugins", "keys", "Object", "plugins", "filterAvailable", "originalPlugins", "exports", "pluginsBugfixes", "originalPluginsBugfixes", "overlappingPlugins", "originalOverlappingPlugins", "data", "result", "plugin", "hasOwnProperty", "call", "availablePlugins"], "sources": ["../src/plugins-compat-data.ts"], "sourcesContent": ["import originalPlugins from \"@babel/compat-data/plugins\";\nimport originalPluginsBugfixes from \"@babel/compat-data/plugin-bugfixes\";\nimport originalOverlappingPlugins from \"@babel/compat-data/overlapping-plugins\";\nimport availablePlugins from \"./available-plugins.ts\";\n\nconst keys: <O extends object>(o: O) => (keyof O)[] = Object.keys;\n\nexport const plugins = filterAvailable(originalPlugins);\nexport const pluginsBugfixes = filterAvailable(originalPluginsBugfixes);\nexport const overlappingPlugins = filterAvailable(originalOverlappingPlugins);\n\n// @ts-expect-error: we extend this here, since it's a syntax plugin and thus\n// doesn't make sense to store it in a compat-data package.\noverlappingPlugins[\"syntax-import-attributes\"] = [\"syntax-import-assertions\"];\n\nfunction filterAvailable<Data extends { [name: string]: unknown }>(\n  data: Data,\n): { [Name in keyof Data & keyof typeof availablePlugins]: Data[Name] } {\n  const result = {} as any;\n  for (const plugin of keys(data)) {\n    if (Object.hasOwnProperty.call(availablePlugins, plugin)) {\n      result[plugin] = data[plugin];\n    }\n  }\n  return result;\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,QAAA,GAAAC,OAAA;AACA,IAAAC,eAAA,GAAAD,OAAA;AACA,IAAAE,mBAAA,GAAAF,OAAA;AACA,IAAAG,iBAAA,GAAAH,OAAA;AAEA,MAAMI,IAA6C,GAAGC,MAAM,CAACD,IAAI;AAE1D,MAAME,OAAO,GAAGC,eAAe,CAACC,QAAe,CAAC;AAACC,OAAA,CAAAH,OAAA,GAAAA,OAAA;AACjD,MAAMI,eAAe,GAAGH,eAAe,CAACI,eAAuB,CAAC;AAACF,OAAA,CAAAC,eAAA,GAAAA,eAAA;AACjE,MAAME,kBAAkB,GAAGL,eAAe,CAACM,mBAA0B,CAAC;AAACJ,OAAA,CAAAG,kBAAA,GAAAA,kBAAA;AAI9EA,kBAAkB,CAAC,0BAA0B,CAAC,GAAG,CAAC,0BAA0B,CAAC;AAE7E,SAASL,eAAeA,CACtBO,IAAU,EAC4D;EACtE,MAAMC,MAAM,GAAG,CAAC,CAAQ;EACxB,KAAK,MAAMC,MAAM,IAAIZ,IAAI,CAACU,IAAI,CAAC,EAAE;IAC/B,IAAIT,MAAM,CAACY,cAAc,CAACC,IAAI,CAACC,yBAAgB,EAAEH,MAAM,CAAC,EAAE;MACxDD,MAAM,CAACC,MAAM,CAAC,GAAGF,IAAI,CAACE,MAAM,CAAC;IAC/B;EACF;EACA,OAAOD,MAAM;AACf"}