{"version": 3, "names": ["_importDeferProxy", "init", "ns", "constV<PERSON>ue", "v", "proxy", "run", "arg1", "arg2", "arg3", "Proxy", "defineProperty", "deleteProperty", "get", "Reflect", "getOwnPropertyDescriptor", "getPrototypeOf", "isExtensible", "has", "ownKeys", "preventExtensions", "set", "setPrototypeOf"], "sources": ["../../src/helpers/importDeferProxy.js"], "sourcesContent": ["/* @minVersion 7.23.0 */\nexport default function _importDeferProxy(init) {\n  var ns = null;\n  var constValue = function (v) {\n    return function () {\n      return v;\n    };\n  };\n  var proxy = function (run) {\n    return function (arg1, arg2, arg3) {\n      if (ns === null) ns = init();\n      return run(ns, arg2, arg3);\n    };\n  };\n  return new Proxy(\n    {},\n    {\n      defineProperty: constValue(false),\n      deleteProperty: constValue(false),\n      get: proxy(Reflect.get),\n      getOwnPropertyDescriptor: proxy(Reflect.getOwnPropertyDescriptor),\n      getPrototypeOf: constValue(null),\n      isExtensible: constValue(false),\n      has: proxy(Reflect.has),\n      ownKeys: proxy(Reflect.ownKeys),\n      preventExtensions: constValue(true),\n      set: constValue(false),\n      setPrototypeOf: constValue(false),\n    }\n  );\n}\n"], "mappings": ";;;;;;AACe,SAASA,iBAAiBA,CAACC,IAAI,EAAE;EAC9C,IAAIC,EAAE,GAAG,IAAI;EACb,IAAIC,UAAU,GAAG,SAAAA,CAAUC,CAAC,EAAE;IAC5B,OAAO,YAAY;MACjB,OAAOA,CAAC;IACV,CAAC;EACH,CAAC;EACD,IAAIC,KAAK,GAAG,SAAAA,CAAUC,GAAG,EAAE;IACzB,OAAO,UAAUC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAE;MACjC,IAAIP,EAAE,KAAK,IAAI,EAAEA,EAAE,GAAGD,IAAI,CAAC,CAAC;MAC5B,OAAOK,GAAG,CAACJ,EAAE,EAAEM,IAAI,EAAEC,IAAI,CAAC;IAC5B,CAAC;EACH,CAAC;EACD,OAAO,IAAIC,KAAK,CACd,CAAC,CAAC,EACF;IACEC,cAAc,EAAER,UAAU,CAAC,KAAK,CAAC;IACjCS,cAAc,EAAET,UAAU,CAAC,KAAK,CAAC;IACjCU,GAAG,EAAER,KAAK,CAACS,OAAO,CAACD,GAAG,CAAC;IACvBE,wBAAwB,EAAEV,KAAK,CAACS,OAAO,CAACC,wBAAwB,CAAC;IACjEC,cAAc,EAAEb,UAAU,CAAC,IAAI,CAAC;IAChCc,YAAY,EAAEd,UAAU,CAAC,KAAK,CAAC;IAC/Be,GAAG,EAAEb,KAAK,CAACS,OAAO,CAACI,GAAG,CAAC;IACvBC,OAAO,EAAEd,KAAK,CAACS,OAAO,CAACK,OAAO,CAAC;IAC/BC,iBAAiB,EAAEjB,UAAU,CAAC,IAAI,CAAC;IACnCkB,GAAG,EAAElB,UAAU,CAAC,KAAK,CAAC;IACtBmB,cAAc,EAAEnB,UAAU,CAAC,KAAK;EAClC,CACF,CAAC;AACH"}