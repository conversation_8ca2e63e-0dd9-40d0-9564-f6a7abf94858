﻿import { createRouter, createWebHistory } from 'vue-router'

const routes = [
  {
    path: '/',
    name: 'Login',
    component: () => import('../views/Login'),
    meta: {
      requireAuth: false
    }
  },

  {
    path: '/main',
    name: 'Main',
    component: () => import('../views/Main'),
    redirect: "/home",
    children: [
      {
        path: '/home',
        name: 'Home',
        component: () => import('../views/admin/Home'),
        meta: {
          requireAuth: true, title: '首页'
        }

      },

      {
        path: '/systemnoticesAdd',
        name: 'SystemnoticesAdd',
        component: () => import('../views/admin/systemnotices/SystemnoticesAdd'),
        meta: { requiresAuth: true, title: '公告添加' }
      },
      {
        path: '/systemnoticesEdit',
        name: 'SystemnoticesEdit',
        component: () => import('../views/admin/systemnotices/SystemnoticesEdit'),
        meta: { requiresAuth: true, title: '公告修改' }
      },
      {
        path: '/systemnoticesManage',
        name: 'SystemnoticesManage',
        component: () => import('../views/admin/systemnotices/SystemnoticesManage'),
        meta: { requiresAuth: true, title: '公告管理' }
      },
      {
        path: '/systemnoticesManage2',
        name: 'SystemnoticesManage2',
        component: () => import('../views/admin/systemnotices/SystemnoticesManage2'),
        meta: { requiresAuth: true, title: '公告列表' }
      },
      {
        path: '/systemnoticesDetail',
        name: 'SystemnoticesDetail',
        component: () => import('../views/admin/systemnotices/SystemnoticesDetail'),
        meta: { requiresAuth: true, title: '公告详情' }
      },
      {
        path: '/repairmenAdd',
        name: 'RepairmenAdd',
        component: () => import('../views/admin/repairmen/RepairmenAdd'),
        meta: { requiresAuth: true, title: '维修员添加' }
      },
      {
        path: '/repairmenEdit',
        name: 'RepairmenEdit',
        component: () => import('../views/admin/repairmen/RepairmenEdit'),
        meta: { requiresAuth: true, title: '维修员修改' }
      },
      {
        path: '/repairmenManage',
        name: 'RepairmenManage',
        component: () => import('../views/admin/repairmen/RepairmenManage'),
        meta: { requiresAuth: true, title: '维修员管理' }
      },
      {
        path: '/repairmenDetail',
        name: 'RepairmenDetail',
        component: () => import('../views/admin/repairmen/RepairmenDetail'),
        meta: { requiresAuth: true, title: '维修员详情' }
      },
      {
        path: '/repairmenInfo',
        name: 'RepairmenInfo',
        component: () => import('../views/admin/repairmen/RepairmenInfo'),
        meta: { requiresAuth: true, title: '修改个人信息' }
      },
      {
        path: '/leaveschoolAdd',
        name: 'LeaveschoolAdd',
        component: () => import('../views/admin/leaveschool/LeaveschoolAdd'),
        meta: { requiresAuth: true, title: '离校登记添加' }
      },
      {
        path: '/leaveschoolEdit',
        name: 'LeaveschoolEdit',
        component: () => import('../views/admin/leaveschool/LeaveschoolEdit'),
        meta: { requiresAuth: true, title: '离校登记修改' }
      },
      {
        path: '/leaveschoolManage',
        name: 'LeaveschoolManage',
        component: () => import('../views/admin/leaveschool/LeaveschoolManage'),
        meta: { requiresAuth: true, title: '离校登记管理' }
      },
      {
        path: '/leaveschoolManage2',
        name: 'LeaveschoolManage2',
        component: () => import('../views/admin/leaveschool/LeaveschoolManage2'),
        meta: { requiresAuth: true, title: '离校登记列表' }
      },
      {
        path: '/leaveschoolDetail',
        name: 'LeaveschoolDetail',
        component: () => import('../views/admin/leaveschool/LeaveschoolDetail'),
        meta: { requiresAuth: true, title: '离校登记详情' }
      },
      {
        path: '/dormitoryscoreAdd',
        name: 'DormitoryscoreAdd',
        component: () => import('../views/admin/dormitoryscore/DormitoryscoreAdd'),
        meta: { requiresAuth: true, title: '宿舍评分添加' }
      },
      {
        path: '/dormitoryscoreEdit',
        name: 'DormitoryscoreEdit',
        component: () => import('../views/admin/dormitoryscore/DormitoryscoreEdit'),
        meta: { requiresAuth: true, title: '宿舍评分修改' }
      },
      {
        path: '/dormitoryscoreManage',
        name: 'DormitoryscoreManage',
        component: () => import('../views/admin/dormitoryscore/DormitoryscoreManage'),
        meta: { requiresAuth: true, title: '宿舍评分管理' }
      },
      {
        path: '/dormitoryscoreManage2',
        name: 'DormitoryscoreManage2',
        component: () => import('../views/admin/dormitoryscore/DormitoryscoreManage2'),
        meta: { requiresAuth: true, title: '宿舍评分列表' }
      },
      {
        path: '/dormitoryscoreDetail',
        name: 'DormitoryscoreDetail',
        component: () => import('../views/admin/dormitoryscore/DormitoryscoreDetail'),
        meta: { requiresAuth: true, title: '宿舍评分详情' }
      },
      {
        path: '/dormitorychangeAdd',
        name: 'DormitorychangeAdd',
        component: () => import('../views/admin/dormitorychange/DormitorychangeAdd'),
        meta: { requiresAuth: true, title: '宿舍更换添加' }
      },
      {
        path: '/dormitorychangeManage',
        name: 'DormitorychangeManage',
        component: () => import('../views/admin/dormitorychange/DormitorychangeManage'),
        meta: { requiresAuth: true, title: '宿舍更换管理' }
      },
      {
        path: '/dormitorychangeManage2',
        name: 'DormitorychangeManage2',
        component: () => import('../views/admin/dormitorychange/DormitorychangeManage2'),
        meta: { requiresAuth: true, title: '宿舍更换列表' }
      },
      {
        path: '/dormitorychangeDetail',
        name: 'DormitorychangeDetail',
        component: () => import('../views/admin/dormitorychange/DormitorychangeDetail'),
        meta: { requiresAuth: true, title: '宿舍更换详情' }
      },
      {
        path: '/waterelectricityfeeAdd',
        name: 'WaterelectricityfeeAdd',
        component: () => import('../views/admin/waterelectricityfee/WaterelectricityfeeAdd'),
        meta: { requiresAuth: true, title: '水电费添加' }
      },
      {
        path: '/waterelectricityfeeEdit',
        name: 'WaterelectricityfeeEdit',
        component: () => import('../views/admin/waterelectricityfee/WaterelectricityfeeEdit'),
        meta: { requiresAuth: true, title: '水电费修改' }
      },
      {
        path: '/waterelectricityfeeManage',
        name: 'WaterelectricityfeeManage',
        component: () => import('../views/admin/waterelectricityfee/WaterelectricityfeeManage'),
        meta: { requiresAuth: true, title: '水电费管理' }
      },
      {
        path: '/waterelectricityfeeManage2',
        name: 'WaterelectricityfeeManage2',
        component: () => import('../views/admin/waterelectricityfee/WaterelectricityfeeManage2'),
        meta: { requiresAuth: true, title: '水电费列表' }
      },
      {
        path: '/waterelectricityfeeDetail',
        name: 'WaterelectricityfeeDetail',
        component: () => import('../views/admin/waterelectricityfee/WaterelectricityfeeDetail'),
        meta: { requiresAuth: true, title: '水电费详情' }
      },
      {
        path: '/repairtypeAdd',
        name: 'RepairtypeAdd',
        component: () => import('../views/admin/repairtype/RepairtypeAdd'),
        meta: { requiresAuth: true, title: '报修类型添加' }
      },
      {
        path: '/repairtypeEdit',
        name: 'RepairtypeEdit',
        component: () => import('../views/admin/repairtype/RepairtypeEdit'),
        meta: { requiresAuth: true, title: '报修类型修改' }
      },
      {
        path: '/repairtypeManage',
        name: 'RepairtypeManage',
        component: () => import('../views/admin/repairtype/RepairtypeManage'),
        meta: { requiresAuth: true, title: '报修类型管理' }
      },
      {
        path: '/dormbuildingAdd',
        name: 'DormbuildingAdd',
        component: () => import('../views/admin/dormbuilding/DormbuildingAdd'),
        meta: { requiresAuth: true, title: '宿舍楼添加' }
      },
      {
        path: '/dormbuildingEdit',
        name: 'DormbuildingEdit',
        component: () => import('../views/admin/dormbuilding/DormbuildingEdit'),
        meta: { requiresAuth: true, title: '宿舍楼修改' }
      },
      {
        path: '/dormbuildingManage',
        name: 'DormbuildingManage',
        component: () => import('../views/admin/dormbuilding/DormbuildingManage'),
        meta: { requiresAuth: true, title: '宿舍楼管理' }
      },
      {
        path: '/studentAdd',
        name: 'StudentAdd',
        component: () => import('../views/admin/student/StudentAdd'),
        meta: { requiresAuth: true, title: '学生添加' }
      },
      {
        path: '/studentEdit',
        name: 'StudentEdit',
        component: () => import('../views/admin/student/StudentEdit'),
        meta: { requiresAuth: true, title: '学生修改' }
      },
      {
        path: '/studentManage',
        name: 'StudentManage',
        component: () => import('../views/admin/student/StudentManage'),
        meta: { requiresAuth: true, title: '学生管理' }
      },
      {
        path: '/studentDetail',
        name: 'StudentDetail',
        component: () => import('../views/admin/student/StudentDetail'),
        meta: { requiresAuth: true, title: '学生详情' }
      },
      {
        path: '/studentInfo',
        name: 'StudentInfo',
        component: () => import('../views/admin/student/StudentInfo'),
        meta: { requiresAuth: true, title: '修改个人信息' }
      },
      {
        path: '/myDormitory',
        name: 'MyDormitory',
        component: () => import('../views/admin/student/MyDormitory'),
        meta: { requiresAuth: true, title: '我的宿舍' }
      },
      {
        path: '/repairordersAdd',
        name: 'RepairordersAdd',
        component: () => import('../views/admin/repairorders/RepairordersAdd'),
        meta: { requiresAuth: true, title: '报修添加' }
      },
      {
        path: '/repairordersEdit',
        name: 'RepairordersEdit',
        component: () => import('../views/admin/repairorders/RepairordersEdit'),
        meta: { requiresAuth: true, title: '报修修改' }
      },
      {
        path: '/repairordersManage',
        name: 'RepairordersManage',
        component: () => import('../views/admin/repairorders/RepairordersManage'),
        meta: { requiresAuth: true, title: '报修管理' }
      },
      {
        path: '/repairordersManage2',
        name: 'RepairordersManage2',
        component: () => import('../views/admin/repairorders/RepairordersManage2'),
        meta: { requiresAuth: true, title: '报修列表' }
      },
      {
        path: '/repairordersDetail',
        name: 'RepairordersDetail',
        component: () => import('../views/admin/repairorders/RepairordersDetail'),
        meta: { requiresAuth: true, title: '报修详情' }
      },
      {
        path: '/registerinfoAdd',
        name: 'RegisterinfoAdd',
        component: () => import('../views/admin/registerinfo/RegisterinfoAdd'),
        meta: { requiresAuth: true, title: '返校登记添加' }
      },
      {
        path: '/registerinfoManage',
        name: 'RegisterinfoManage',
        component: () => import('../views/admin/registerinfo/RegisterinfoManage'),
        meta: { requiresAuth: true, title: '返校登记管理' }
      },
      {
        path: '/registerinfoManage2',
        name: 'RegisterinfoManage2',
        component: () => import('../views/admin/registerinfo/RegisterinfoManage2'),
        meta: { requiresAuth: true, title: '返校登记列表' }
      },
      {
        path: '/registerinfoDetail',
        name: 'RegisterinfoDetail',
        component: () => import('../views/admin/registerinfo/RegisterinfoDetail'),
        meta: { requiresAuth: true, title: '返校登记详情' }
      },
      {
        path: '/hostessAdd',
        name: 'HostessAdd',
        component: () => import('../views/admin/hostess/HostessAdd'),
        meta: { requiresAuth: true, title: '宿管阿姨添加' }
      },
      {
        path: '/hostessEdit',
        name: 'HostessEdit',
        component: () => import('../views/admin/hostess/HostessEdit'),
        meta: { requiresAuth: true, title: '宿管阿姨修改' }
      },
      {
        path: '/hostessManage',
        name: 'HostessManage',
        component: () => import('../views/admin/hostess/HostessManage'),
        meta: { requiresAuth: true, title: '宿管阿姨管理' }
      },
      {
        path: '/hostessDetail',
        name: 'HostessDetail',
        component: () => import('../views/admin/hostess/HostessDetail'),
        meta: { requiresAuth: true, title: '宿管阿姨详情' }
      },
      {
        path: '/hostessInfo',
        name: 'HostessInfo',
        component: () => import('../views/admin/hostess/HostessInfo'),
        meta: { requiresAuth: true, title: '修改个人信息' }
      },
      {
        path: '/dormitoryAdd',
        name: 'DormitoryAdd',
        component: () => import('../views/admin/dormitory/DormitoryAdd'),
        meta: { requiresAuth: true, title: '宿舍添加' }
      },
      {
        path: '/dormitoryEdit',
        name: 'DormitoryEdit',
        component: () => import('../views/admin/dormitory/DormitoryEdit'),
        meta: { requiresAuth: true, title: '宿舍修改' }
      },
      {
        path: '/dormitoryManage',
        name: 'DormitoryManage',
        component: () => import('../views/admin/dormitory/DormitoryManage'),
        meta: { requiresAuth: true, title: '宿舍管理' }
      },
      {
        path: '/total1',
        name: 'Total1',
        component: () => import('../views/admin/total/Total1'),
        meta: { requiresAuth: true, title: '图表1' }
      },
      {
        path: '/total2',
        name: 'Total2',
        component: () => import('../views/admin/total/Total2'),
        meta: { requiresAuth: true, title: '图表2' }
      },
      {
        path: '/total3',
        name: 'Total3',
        component: () => import('../views/admin/total/Total3'),
        meta: { requiresAuth: true, title: '图表3' }
      },
      {
        path: '/total4',
        name: 'Total4',
        component: () => import('../views/admin/total/Total4'),
        meta: { requiresAuth: true, title: '图表4' }
      },

      {
        path: '/password',
        name: 'Password',
        component: () => import('../views/admin/system/Password'),
        meta: {
          requireAuth: true, title: '修改密码'
        }
      },
    ]
  },


]



const router = createRouter({
  history: createWebHistory(process.env.BASE_URL),
  routes
})



router.beforeEach((to, from, next) => {
  if (to.path == '/') {
    sessionStorage.removeItem('userLname');
    sessionStorage.removeItem('role');
  }
  let currentUser = sessionStorage.getItem('userLname');
  console.log(to + "  to.meta.requireAuth");

  if (to.meta.requireAuth) {
    if (!currentUser && to.path != '/login') {
      next({ path: '/' });
    } else {
      next();
    }
  } else {

    next();
  }
})

export default router


