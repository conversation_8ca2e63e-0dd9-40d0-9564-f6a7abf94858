{"remainingRequest": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\admin\\Home.vue?vue&type=template&id=a44c444e", "dependencies": [{"path": "I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\admin\\Home.vue", "mtime": 1749034452000}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749043011899}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749043012919}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749043013784}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749043011899}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749043013130}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQoNCiAgICAgICAgICAgICAgICAgDQogIA0KDQogIDxkaXYgc3R5bGU9IndpZHRoOiAxMDAlO2xpbmUtaGVpZ2h0OiAzMHB4O3RleHQtYWxpZ246IGNlbnRlcjsgcGFkZGluZzogMTAwcHg7IiBpZD0iaG9tZSI+DQoNCiAgICDotKblj7fvvJo8YiBzdHlsZT0iY29sb3I6IHJlZDsiPnt7IHVzZXJMbmFtZSB9fTwvYj7vvIwNCiAgICDouqvku73vvJo8YiBzdHlsZT0iY29sb3I6IHJlZDsiPnt7IHJvbGUgfX08L2I+PGJyPg0KDQoNCg0KICAgIOaCqOWlve+8jOasoui/juS9v+eUqOWuv+iIjeeuoeeQhuezu+e7n++8gTxicj4NCg0KDQoNCiAgPC9kaXY+DQoNCg0KDQogDQo="}, {"version": 3, "sources": ["I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\admin\\Home.vue"], "names": [], "mappings": ";;;;;EAKE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEtF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7C,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;;;IAI3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;;;EAInB,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "I:/product4/B7839DormManager/DormManager-web/src/views/admin/Home.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n\r\n                 \r\n  \r\n\r\n  <div style=\"width: 100%;line-height: 30px;text-align: center; padding: 100px;\" id=\"home\">\r\n\r\n    账号：<b style=\"color: red;\">{{ userLname }}</b>，\r\n    身份：<b style=\"color: red;\">{{ role }}</b><br>\r\n\r\n\r\n\r\n    您好，欢迎使用宿舍管理系统！<br>\r\n\r\n\r\n\r\n  </div>\r\n\r\n\r\n\r\n \r\n</template>\r\n\r\n<script>\r\n  export default {\r\n    data() {\r\n      return {\r\n        userLname: \"\",\r\n        role: \"\",\r\n      };\r\n    },\r\n    mounted() {\r\n      this.userLname = sessionStorage.getItem(\"userLname\");\r\n      this.role = sessionStorage.getItem(\"role\");  \r\n\r\n    },\r\n  };\r\n\r\n</script>\r\n\r\n<style scoped></style>\r\n\r\n"]}]}