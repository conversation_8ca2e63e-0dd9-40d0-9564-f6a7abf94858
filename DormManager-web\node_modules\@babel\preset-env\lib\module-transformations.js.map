{"version": 3, "names": ["auto", "amd", "commonjs", "cjs", "systemjs", "umd", "exports", "default", "_default"], "sources": ["../src/module-transformations.ts"], "sourcesContent": ["type AvailablePlugins = typeof import(\"./available-plugins\").default;\n\nexport default {\n  auto: \"transform-modules-commonjs\",\n  amd: \"transform-modules-amd\",\n  commonjs: \"transform-modules-commonjs\",\n  cjs: \"transform-modules-commonjs\",\n  systemjs: \"transform-modules-systemjs\",\n  umd: \"transform-modules-umd\",\n} as { [transform: string]: keyof AvailablePlugins };\n"], "mappings": ";;;;;;eAEe;EACbA,IAAI,EAAE,4BAA4B;EAClCC,GAAG,EAAE,uBAAuB;EAC5BC,QAAQ,EAAE,4BAA4B;EACtCC,GAAG,EAAE,4BAA4B;EACjCC,QAAQ,EAAE,4BAA4B;EACtCC,GAAG,EAAE;AACP,CAAC;AAAAC,OAAA,CAAAC,OAAA,GAAAC,QAAA"}