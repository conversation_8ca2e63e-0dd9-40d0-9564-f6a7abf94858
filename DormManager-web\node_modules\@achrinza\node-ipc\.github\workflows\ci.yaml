name: CI

on:
  push:
    branches: [ v9 ]
  pull_request:
    branches: [ v9 ]

jobs:
  build:
    name: Test
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os:
          - ubuntu-latest
          - macos-latest
          - windows-latest
        node_version:
          - 8
          - 10
          - 12
          - 14
          - 16
          - 17
    steps:
    - uses: actions/checkout@v2
    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v1
      with:
        node-version: ${{ matrix.node-version }}
    - name: Install Dependencies
      run: npm ci --ignore-scripts
    - name: Run Tests
      if: matrix.os != 'windows-latest'
      run: npm test --ignore-scripts
    - name: Run Tests (Windows)
      if: matrix.os == 'windows-latest'
      run: npm run-script test-windows --ignore-scripts
  code-lint:
    name: Code Lint
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Use Node.js 16
        uses: actions/setup-node@v1
        with:
          node-version: 16 # LTS
      - name: Install Dependencies
        run: npm ci --ignore-scripts
      - name: Lockfile Lint
        run: |
          npm exec \
            --no-install \
            --package=lockfile-lint \
            -- \
            lockfile-lint \
              --allowed-hosts=npm \
              --path=./package-lock.json \
              --validate-https \
              --validate-package-names
