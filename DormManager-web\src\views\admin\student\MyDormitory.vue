<template>
  <div>
    <!-- 当前宿舍信息 -->
    <el-card class="box-card" style="margin-bottom: 20px;">
      <div slot="header" class="clearfix">
        <span style="font-size: 18px; font-weight: bold;">我的宿舍信息</span>
      </div>
      <div v-if="currentDormitory.doro">
        <el-row :gutter="20">
          <el-col :span="12">
            <p><strong>宿舍楼：</strong>{{ currentDormitory.dbname }}</p>
            <p><strong>宿舍编号：</strong>{{ currentDormitory.doro }}</p>
          </el-col>
          <el-col :span="12">
            <p><strong>性别限制：</strong>{{ currentDormitory.dorgender }}</p>
            <p><strong>床位数：</strong>{{ currentDormitory.dorcapacity }}</p>
          </el-col>
        </el-row>
      </div>
      <div v-else>
        <p style="color: #999;">暂未分配宿舍</p>
      </div>
    </el-card>

    <!-- 申请更换宿舍 -->
    <el-card class="box-card" style="margin-bottom: 20px;">
      <div slot="header" class="clearfix">
        <span style="font-size: 18px; font-weight: bold;">申请更换宿舍</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="showApplyForm = !showApplyForm">
          {{ showApplyForm ? '收起' : '展开' }}
        </el-button>
      </div>
      <div v-show="showApplyForm">
        <el-form :model="applyForm" :rules="applyRules" ref="applyFormRef" label-width="120px">
          <el-form-item label="新宿舍楼" prop="dbid2">
            <el-select v-model="applyForm.dbid2" placeholder="请选择宿舍楼" @change="onBuildingChange" style="width: 300px;">
              <el-option v-for="item in dormbuildingList" :key="item.dbid" :label="item.dbname" :value="item.dbid"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="新宿舍" prop="doro2">
            <el-select v-model="applyForm.doro2" placeholder="请先选择宿舍楼" :disabled="!applyForm.dbid2" style="width: 300px;">
              <el-option v-for="item in filteredDormitoryList" :key="item.doro" :label="item.doro" :value="item.doro"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="申请原因" prop="applicationreason">
            <el-input type="textarea" v-model="applyForm.applicationreason" placeholder="请输入申请更换宿舍的原因" :rows="4" style="width: 500px;"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="submitApplication" :loading="btnLoading">提交申请</el-button>
            <el-button @click="resetApplyForm">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <!-- 申请历史 -->
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span style="font-size: 18px; font-weight: bold;">申请历史</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="getApplicationHistory">刷新</el-button>
      </div>
      <el-table :data="applicationList" style="width: 100%" v-loading="listLoading">
        <el-table-column prop="submissiontime" label="申请时间" width="150"></el-table-column>
        <el-table-column label="原宿舍" width="150">
          <template slot-scope="scope">
            {{ scope.row.dbname }} - {{ scope.row.doro }}
          </template>
        </el-table-column>
        <el-table-column label="目标宿舍" width="150">
          <template slot-scope="scope">
            {{ scope.row.dbname2 }} - {{ scope.row.doro2 }}
          </template>
        </el-table-column>
        <el-table-column prop="applicationreason" label="申请原因" show-overflow-tooltip></el-table-column>
        <el-table-column prop="reviewstatus" label="审核状态" width="100">
          <template slot-scope="scope">
            <el-tag :type="getStatusType(scope.row.reviewstatus)">{{ scope.row.reviewstatus }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="reviewresponse" label="审核回复" show-overflow-tooltip></el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script>
import request, { base } from "../../../../utils/http";

export default {
  name: 'MyDormitory',
  data() {
    return {
      currentDormitory: {}, // 当前宿舍信息
      showApplyForm: false, // 是否显示申请表单
      btnLoading: false, // 提交按钮加载状态
      listLoading: false, // 列表加载状态
      
      // 申请表单
      applyForm: {
        sno: '',
        dbid: null, // 原宿舍楼ID
        doro: '', // 原宿舍编号
        dbid2: null, // 新宿舍楼ID
        doro2: '', // 新宿舍编号
        applicationreason: ''
      },
      
      // 表单验证规则
      applyRules: {
        dbid2: [{ required: true, message: '请选择新宿舍楼', trigger: 'change' }],
        doro2: [{ required: true, message: '请选择新宿舍', trigger: 'change' }],
        applicationreason: [{ required: true, message: '请输入申请原因', trigger: 'blur' }]
      },
      
      // 数据列表
      dormbuildingList: [], // 宿舍楼列表
      filteredDormitoryList: [], // 过滤后的宿舍列表
      applicationList: [], // 申请历史列表
      
      // 学生信息
      studentInfo: {}
    };
  },
  
  created() {
    this.getStudentInfo();
    this.getDormbuildingList();
    this.getApplicationHistory();
  },
  
  methods: {
    // 获取学生信息和当前宿舍信息
    getStudentInfo() {
      const user = JSON.parse(sessionStorage.getItem("user"));
      this.studentInfo = user;
      this.applyForm.sno = user.sno;
      
      let url = base + "/student/get?id=" + user.sno;
      request.post(url, {}).then((res) => {
        if (res.code == 200) {
          this.currentDormitory = res.resdata;
          // 设置原宿舍信息
          this.applyForm.dbid = res.resdata.dbid;
          this.applyForm.doro = res.resdata.doro;
        }
      });
    },
    
    // 获取宿舍楼列表
    getDormbuildingList() {
      let url = base + "/dormbuilding/list?currentPage=1&pageSize=1000";
      request.post(url, {}).then((res) => {
        this.dormbuildingList = res.resdata;
      });
    },
    
    // 宿舍楼变化时的处理
    onBuildingChange() {
      this.applyForm.doro2 = ''; // 清空宿舍选择
      this.updateDormitoryList();
    },
    
    // 更新宿舍列表（根据宿舍楼和学生性别过滤）
    updateDormitoryList() {
      if (!this.applyForm.dbid2 || !this.studentInfo.sex) {
        this.filteredDormitoryList = [];
        return;
      }

      let para = {
        dbid: this.applyForm.dbid2,
        dorgender: this.studentInfo.sex
      };
      let url = base + "/dormitory/listByBuildingAndGender";
      request.post(url, para).then((res) => {
        if (res.code == 200) {
          // 过滤掉当前宿舍
          this.filteredDormitoryList = res.resdata.filter(item => 
            !(item.dbid == this.applyForm.dbid && item.doro == this.applyForm.doro)
          );
        } else {
          this.filteredDormitoryList = [];
          this.$message({
            message: res.msg || "获取宿舍列表失败",
            type: "error"
          });
        }
      }).catch(() => {
        this.filteredDormitoryList = [];
        this.$message({
          message: "获取宿舍列表失败",
          type: "error"
        });
      });
    },
    
    // 提交申请
    submitApplication() {
      this.$refs.applyFormRef.validate((valid) => {
        if (valid) {
          // 检查是否选择了不同的宿舍
          if (this.applyForm.dbid == this.applyForm.dbid2 && this.applyForm.doro == this.applyForm.doro2) {
            this.$message({
              message: "新宿舍不能与当前宿舍相同",
              type: "warning"
            });
            return;
          }
          
          this.btnLoading = true;
          let url = base + "/dormitorychange/apply";
          request.post(url, this.applyForm).then((res) => {
            if (res.code == 200) {
              this.$message({
                message: "申请提交成功，请等待审核",
                type: "success"
              });
              this.resetApplyForm();
              this.showApplyForm = false;
              this.getApplicationHistory(); // 刷新申请历史
            } else {
              this.$message({
                message: res.msg || "申请提交失败",
                type: "error"
              });
            }
            this.btnLoading = false;
          }).catch(() => {
            this.$message({
              message: "申请提交失败",
              type: "error"
            });
            this.btnLoading = false;
          });
        }
      });
    },
    
    // 重置申请表单
    resetApplyForm() {
      this.$refs.applyFormRef.resetFields();
      this.applyForm.dbid2 = null;
      this.applyForm.doro2 = '';
      this.applyForm.applicationreason = '';
      this.filteredDormitoryList = [];
    },
    
    // 获取申请历史
    getApplicationHistory() {
      this.listLoading = true;
      let para = {
        sno: this.studentInfo.sno
      };
      let url = base + "/dormitorychange/list?currentPage=1&pageSize=100";
      request.post(url, para).then((res) => {
        if (res.code == 200) {
          this.applicationList = res.resdata;
        }
        this.listLoading = false;
      }).catch(() => {
        this.listLoading = false;
      });
    },
    
    // 获取状态标签类型
    getStatusType(status) {
      switch (status) {
        case '待审核':
          return 'warning';
        case '审核通过':
          return 'success';
        case '审核不通过':
          return 'danger';
        default:
          return 'info';
      }
    }
  }
};
</script>

<style scoped>
.box-card {
  margin-bottom: 20px;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both;
}
</style>
