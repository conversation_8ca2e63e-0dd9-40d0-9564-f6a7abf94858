{"remainingRequest": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\Login.vue?vue&type=template&id=26084dc2&scoped=true", "dependencies": [{"path": "I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\Login.vue", "mtime": 1749045164779}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749043011899}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749043012919}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749043013784}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749043011899}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749043013130}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgogIDxkaXYgY2xhc3M9ImF1dGgtd3JhcHBlciBhdXQtYmctaW1nIj4KICAgICAgICA8ZGl2IGNsYXNzPSJhdXRoLWNvbnRlbnQiICBzdHlsZT0id2lkdGg6IDQ1MHB4OyI+CiAgICAgICAgICAgICA8Zm9ybSBhY3Rpb249IiMiPgogICAgICAgICAgICA8ZGl2IGNsYXNzPSJjYXJkIiBzdHlsZT0icGFkZGluZy10b3A6IDMwcHg7IHBhZGRpbmctYm90dG9tOiAzMHB4OyAiPgogICAgICAgICAgICAgICAgPGRpdiBjbGFzcz0iY2FyZC1ib2R5IHRleHQtY2VudGVyIj4KICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJtYi00Ij4KICAgICAgICAgICAgICAgICAgICAgICAgPGkgY2xhc3M9ImZlYXRoZXIgaWNvbi11bmxvY2sgYXV0aC1pY29uIj48L2k+CiAgICAgICAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzPSJtYi00Ij7lrr/oiI3nrqHnkIbns7vnu588L2gzPgogICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9ImlucHV0LWdyb3VwIG1iLTMiPgogICAgICAgICAgICAgICAgICAgICAgICA8aW5wdXQgdHlwZT0idGV4dCIgY2xhc3M9ImZvcm0tY29udHJvbCIgcGxhY2Vob2xkZXI9IueUqOaIt+WQjSIgdi1tb2RlbD0ibG9naW5Nb2RlbC51c2VybmFtZSI+CiAgICAgICAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzcz0iaW5wdXQtZ3JvdXAgbWItNCI+CiAgICAgICAgICAgICAgICAgICAgICAgIDxpbnB1dCB0eXBlPSJwYXNzd29yZCIgY2xhc3M9ImZvcm0tY29udHJvbCIgcGxhY2Vob2xkZXI9IuWvhueggSIgdi1tb2RlbD0ibG9naW5Nb2RlbC5wYXNzd29yZCI+CiAgICAgICAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzcz0iZm9ybS1ncm91cCB0ZXh0LWxlZnQiPgogICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJjaGVja2JveCBjaGVja2JveC1maWxsIGQtaW5saW5lIj4KICAgICAgICAgICAgICAgICAgICAgICAgICA8ZWwtcmFkaW8gbGFiZWw9IueuoeeQhuWRmCIgdi1tb2RlbD0ibG9naW5Nb2RlbC5yYWRpbyI+566h55CG5ZGYPC9lbC1yYWRpbz4KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZWwtcmFkaW8gbGFiZWw9IuWtpueUnyIgdi1tb2RlbD0ibG9naW5Nb2RlbC5yYWRpbyI+5a2m55SfPC9lbC1yYWRpbz4KICAgICAgPGVsLXJhZGlvIGxhYmVsPSLnu7Tkv67lkZgiIHYtbW9kZWw9ImxvZ2luTW9kZWwucmFkaW8iPue7tOS/ruWRmDwvZWwtcmFkaW8+CiAgICAgIDxlbC1yYWRpbyBsYWJlbD0i5a6/566h6Zi/5aeoIiB2LW1vZGVsPSJsb2dpbk1vZGVsLnJhZGlvIj7lrr/nrqHpmL/lp6g8L2VsLXJhZGlvPgoKCiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgICAgICAgIDwvZGl2PgoKCiAgICAgICAgICAgIDxlbC1idXR0b24gY2xhc3M9ImJ0biBidG4tcHJpbWFyeSBzaGFkb3ctMiBtYi00IiAgQGNsaWNrPSJsb2dpbiI+55m75b2VPC9lbC1idXR0b24+CiAgICAgICAgICAgICAgICAgICAgIDxhIGhyZWY9IiMiICBAY2xpY2s9InRvcmVnIj4g5paw5a2m55Sf5rOo5YaMIDwvYT4KIDxlbC1kaWFsb2cgdGl0bGU9IuWtpueUn+azqOWGjCIgIHYtbW9kZWw9ImZvcm1WaXNpYmxlIiB3aWR0aD0iNDAlIiA6Y2xvc2Utb24tY2xpY2stbW9kYWw9ImZhbHNlIj4KIDxlbC1mb3JtIDptb2RlbD0iZm9ybURhdGEiIGxhYmVsLXdpZHRoPSIyMCUiIHJlZj0iZm9ybURhdGFSZWYiIDpydWxlcz0icnVsZXMiICBhbGlnbj0ibGVmdCI+CjxlbC1mb3JtLWl0ZW0gbGFiZWw9IuWtpuWPtyIgcHJvcD0ic25vIj4KPGVsLWlucHV0IHYtbW9kZWw9ImZvcm1EYXRhLnNubyIgcGxhY2Vob2xkZXI9IuWtpuWPtyIgIHN0eWxlPSJ3aWR0aDo1MCU7IiA+PC9lbC1pbnB1dD4KPC9lbC1mb3JtLWl0ZW0+CjxlbC1mb3JtLWl0ZW0gbGFiZWw9IueZu+W9leWvhueggSIgcHJvcD0icGFzc3dvcmQiPgo8ZWwtaW5wdXQgdHlwZT0icGFzc3dvcmQiIHYtbW9kZWw9ImZvcm1EYXRhLnBhc3N3b3JkIiBwbGFjZWhvbGRlcj0i55m75b2V5a+G56CBIiAgc3R5bGU9IndpZHRoOjUwJTsiID48L2VsLWlucHV0Pgo8L2VsLWZvcm0taXRlbT4KPGVsLWZvcm0taXRlbSBsYWJlbD0i56Gu6K6k5a+G56CBIiBwcm9wPSJwYXNzd29yZDIiPgo8ZWwtaW5wdXQgdHlwZT0icGFzc3dvcmQiIHYtbW9kZWw9ImZvcm1EYXRhLnBhc3N3b3JkMiIgcGxhY2Vob2xkZXI9IuehruiupOWvhueggSIgIHN0eWxlPSJ3aWR0aDo1MCU7IiA+PC9lbC1pbnB1dD4KPC9lbC1mb3JtLWl0ZW0+CjxlbC1mb3JtLWl0ZW0gbGFiZWw9IuWnk+WQjSIgcHJvcD0ic3RuYW1lIj4KPGVsLWlucHV0IHYtbW9kZWw9ImZvcm1EYXRhLnN0bmFtZSIgcGxhY2Vob2xkZXI9IuWnk+WQjSIgIHN0eWxlPSJ3aWR0aDo1MCU7IiA+PC9lbC1pbnB1dD4KPC9lbC1mb3JtLWl0ZW0+CjxlbC1mb3JtLWl0ZW0gbGFiZWw9IuaAp+WIqyIgcHJvcD0ic2V4Ij4KPGVsLXJhZGlvLWdyb3VwIHYtbW9kZWw9ImZvcm1EYXRhLnNleCIgQGNoYW5nZT0ib25HZW5kZXJDaGFuZ2UiPgo8ZWwtcmFkaW8gbGFiZWw9IueUtyI+CueUtwo8L2VsLXJhZGlvPgo8ZWwtcmFkaW8gbGFiZWw9IuWlsyI+CuWlswo8L2VsLXJhZGlvPgo8L2VsLXJhZGlvLWdyb3VwPgo8L2VsLWZvcm0taXRlbT4KPGVsLWZvcm0taXRlbSBsYWJlbD0i5omL5py65Y+356CBIiBwcm9wPSJwaG9uZSI+CjxlbC1pbnB1dCB2LW1vZGVsPSJmb3JtRGF0YS5waG9uZSIgcGxhY2Vob2xkZXI9IuaJi+acuuWPt+eggSIgIHN0eWxlPSJ3aWR0aDo1MCU7IiA+PC9lbC1pbnB1dD4KPC9lbC1mb3JtLWl0ZW0+CjxlbC1mb3JtLWl0ZW0gbGFiZWw9IuWuv+iIjealvCIgcHJvcD0iZGJpZCI+CjxlbC1zZWxlY3Qgdi1tb2RlbD0iZm9ybURhdGEuZGJpZCIgcGxhY2Vob2xkZXI9Iuivt+mAieaLqSIgIHNpemU9InNtYWxsIiBAY2hhbmdlPSJvbkJ1aWxkaW5nQ2hhbmdlIj4KPGVsLW9wdGlvbiB2LWZvcj0iaXRlbSBpbiBkb3JtYnVpbGRpbmdMaXN0IiA6a2V5PSJpdGVtLmRiaWQiIDpsYWJlbD0iaXRlbS5kYm5hbWUiIDp2YWx1ZT0iaXRlbS5kYmlkIj48L2VsLW9wdGlvbj4KPC9lbC1zZWxlY3Q+CjwvZWwtZm9ybS1pdGVtPgo8ZWwtZm9ybS1pdGVtIGxhYmVsPSLlrr/oiI0iIHByb3A9ImRvcm8iPgo8ZWwtc2VsZWN0IHYtbW9kZWw9ImZvcm1EYXRhLmRvcm8iIHBsYWNlaG9sZGVyPSLor7flhYjpgInmi6nlrr/oiI3mpbzlkozmgKfliKsiICBzaXplPSJzbWFsbCIgOmRpc2FibGVkPSIhZm9ybURhdGEuZGJpZCB8fCAhZm9ybURhdGEuc2V4Ij4KPGVsLW9wdGlvbiB2LWZvcj0iaXRlbSBpbiBmaWx0ZXJlZERvcm1pdG9yeUxpc3QiIDprZXk9Iml0ZW0uZG9ybyIgOmxhYmVsPSJpdGVtLmRvcm8iIDp2YWx1ZT0iaXRlbS5kb3JvIj48L2VsLW9wdGlvbj4KPC9lbC1zZWxlY3Q+CjwvZWwtZm9ybS1pdGVtPgo8ZWwtZm9ybS1pdGVtIGxhYmVsPSLkuJPkuJoiIHByb3A9InNwZWNpYWx0eSI+CjxlbC1pbnB1dCB2LW1vZGVsPSJmb3JtRGF0YS5zcGVjaWFsdHkiIHBsYWNlaG9sZGVyPSLkuJPkuJoiICBzdHlsZT0id2lkdGg6NTAlOyIgPjwvZWwtaW5wdXQ+CjwvZWwtZm9ybS1pdGVtPgo8ZWwtZm9ybS1pdGVtIGxhYmVsPSLnj63nuqciIHByb3A9ImNsc25hbWUiPgo8ZWwtaW5wdXQgdi1tb2RlbD0iZm9ybURhdGEuY2xzbmFtZSIgcGxhY2Vob2xkZXI9IuePree6pyIgIHN0eWxlPSJ3aWR0aDo1MCU7IiA+PC9lbC1pbnB1dD4KPC9lbC1mb3JtLWl0ZW0+CjxlbC1mb3JtLWl0ZW0+CjxlbC1idXR0b24gdHlwZT0icHJpbWFyeSIgQGNsaWNrPSJyZWciIDpsb2FkaW5nPSJidG5Mb2FkaW5nIiA+5rOoIOWGjDwvZWwtYnV0dG9uPgo8L2VsLWZvcm0taXRlbT4KPC9lbC1mb3JtPgo8L2VsLWRpYWxvZz4KCgogICAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICA8L2Zvcm0+CgogICAgICAgIDwvZGl2PgogICAgPC9kaXY+CgoKCg=="}, {"version": 3, "sources": ["I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\Login.vue"], "names": [], "mappings": ";;EAEE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aAC3C,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjB,CAAC,CAAC,CAAC,EAAE,CAAC,CA<PERSON>,<PERSON><PERSON>,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC/D,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC9B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACb,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAChD,CAAC,CAAC,CAAC,CAAC,CAAC;oBACL,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC3B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;wBACzB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC3F,CAAC,CAAC,CAAC,CAAC,CAAC;oBACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;wBACzB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC9F,CAAC,CAAC,CAAC,CAAC,CAAC;uBACF,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAChC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;0BAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;mCACrD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;;wBAG9C,CAAC,CAAC,CAAC,CAAC,CAAC;oBACT,CAAC,CAAC,CAAC,CAAC,CAAC;;;YAGb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;qBACrE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;CAC1D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CACxF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACjF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACxG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACzG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACpF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACnB;AACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACnB;AACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACrF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAChH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACrH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACnH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACvF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACrF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;;gBAGK,CAAC,CAAC,CAAC,CAAC,CAAC;YACT,CAAC,CAAC,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEV,CAAC,CAAC,CAAC,CAAC,CAAC;IACT,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "I:/product4/B7839DormManager/DormManager-web/src/views/Login.vue", "sourceRoot": "", "sourcesContent": ["<template>\n\n  <div class=\"auth-wrapper aut-bg-img\">\n        <div class=\"auth-content\"  style=\"width: 450px;\">\n             <form action=\"#\">\n            <div class=\"card\" style=\"padding-top: 30px; padding-bottom: 30px; \">\n                <div class=\"card-body text-center\">\n                    <div class=\"mb-4\">\n                        <i class=\"feather icon-unlock auth-icon\"></i>\n                    </div>\n                    <h3 class=\"mb-4\">宿舍管理系统</h3>\n                    <div class=\"input-group mb-3\">\n                        <input type=\"text\" class=\"form-control\" placeholder=\"用户名\" v-model=\"loginModel.username\">\n                    </div>\n                    <div class=\"input-group mb-4\">\n                        <input type=\"password\" class=\"form-control\" placeholder=\"密码\" v-model=\"loginModel.password\">\n                    </div>\n                       <div class=\"form-group text-left\">\n                        <div class=\"checkbox checkbox-fill d-inline\">\n                          <el-radio label=\"管理员\" v-model=\"loginModel.radio\">管理员</el-radio>\n                                   <el-radio label=\"学生\" v-model=\"loginModel.radio\">学生</el-radio>\n      <el-radio label=\"维修员\" v-model=\"loginModel.radio\">维修员</el-radio>\n      <el-radio label=\"宿管阿姨\" v-model=\"loginModel.radio\">宿管阿姨</el-radio>\n\n\n                        </div>\n                    </div>\n\n\n            <el-button class=\"btn btn-primary shadow-2 mb-4\"  @click=\"login\">登录</el-button>\n                     <a href=\"#\"  @click=\"toreg\"> 新学生注册 </a>\n <el-dialog title=\"学生注册\"  v-model=\"formVisible\" width=\"40%\" :close-on-click-modal=\"false\">\n <el-form :model=\"formData\" label-width=\"20%\" ref=\"formDataRef\" :rules=\"rules\"  align=\"left\">\n<el-form-item label=\"学号\" prop=\"sno\">\n<el-input v-model=\"formData.sno\" placeholder=\"学号\"  style=\"width:50%;\" ></el-input>\n</el-form-item>\n<el-form-item label=\"登录密码\" prop=\"password\">\n<el-input type=\"password\" v-model=\"formData.password\" placeholder=\"登录密码\"  style=\"width:50%;\" ></el-input>\n</el-form-item>\n<el-form-item label=\"确认密码\" prop=\"password2\">\n<el-input type=\"password\" v-model=\"formData.password2\" placeholder=\"确认密码\"  style=\"width:50%;\" ></el-input>\n</el-form-item>\n<el-form-item label=\"姓名\" prop=\"stname\">\n<el-input v-model=\"formData.stname\" placeholder=\"姓名\"  style=\"width:50%;\" ></el-input>\n</el-form-item>\n<el-form-item label=\"性别\" prop=\"sex\">\n<el-radio-group v-model=\"formData.sex\" @change=\"onGenderChange\">\n<el-radio label=\"男\">\n男\n</el-radio>\n<el-radio label=\"女\">\n女\n</el-radio>\n</el-radio-group>\n</el-form-item>\n<el-form-item label=\"手机号码\" prop=\"phone\">\n<el-input v-model=\"formData.phone\" placeholder=\"手机号码\"  style=\"width:50%;\" ></el-input>\n</el-form-item>\n<el-form-item label=\"宿舍楼\" prop=\"dbid\">\n<el-select v-model=\"formData.dbid\" placeholder=\"请选择\"  size=\"small\" @change=\"onBuildingChange\">\n<el-option v-for=\"item in dormbuildingList\" :key=\"item.dbid\" :label=\"item.dbname\" :value=\"item.dbid\"></el-option>\n</el-select>\n</el-form-item>\n<el-form-item label=\"宿舍\" prop=\"doro\">\n<el-select v-model=\"formData.doro\" placeholder=\"请先选择宿舍楼和性别\"  size=\"small\" :disabled=\"!formData.dbid || !formData.sex\">\n<el-option v-for=\"item in filteredDormitoryList\" :key=\"item.doro\" :label=\"item.doro\" :value=\"item.doro\"></el-option>\n</el-select>\n</el-form-item>\n<el-form-item label=\"专业\" prop=\"specialty\">\n<el-input v-model=\"formData.specialty\" placeholder=\"专业\"  style=\"width:50%;\" ></el-input>\n</el-form-item>\n<el-form-item label=\"班级\" prop=\"clsname\">\n<el-input v-model=\"formData.clsname\" placeholder=\"班级\"  style=\"width:50%;\" ></el-input>\n</el-form-item>\n<el-form-item>\n<el-button type=\"primary\" @click=\"reg\" :loading=\"btnLoading\" >注 册</el-button>\n</el-form-item>\n</el-form>\n</el-dialog>\n\n\n                </div>\n            </div>\n            </form>\n\n        </div>\n    </div>\n\n\n\n</template>\n<script>\nimport request, { base } from \"../../utils/http\";\nexport default {\n  name: \"Login\",\n  data() {\n    return {\n      year: new Date().getFullYear(),\n      loginModel: {\n        username: \"\",\n        password: \"\",\n        radio: \"管理员\",\n      },\n      loginModel2: {},\n     add: true, //是否是添加\n      formVisible: false,\n      formData:{},\n      dormbuildingList: [], //宿舍楼列表\n      dormitoryList: [], //所有宿舍列表\n      filteredDormitoryList: [], //过滤后的宿舍列表\n\n      addrules: {\n          sno: [{ required: true, message: '请输入学号', trigger: 'blur' },],\n          password: [{ required: true, message: '请输入登录密码', trigger: 'blur' }],\n          password2: [{ required: true, message: '请输入登录密码', trigger: 'blur' },{ validator: (rule, value, callback) => { if (value !== this.formData.password) { callback(new Error('两次输入密码不一致!')); } else { callback(); } }, trigger: 'blur' },],\n          stname: [{ required: true, message: '请输入姓名', trigger: 'blur' },],\n          sex: [{ required: true, message: '请输入性别', trigger: 'blur' },],\n          phone: [{ required: true, message: '请输入手机号码', trigger: 'blur' },        { pattern: /^1[3456789]\\d{9}$/, message: '手机号码格式不正确', trigger: 'blur' },],\n          doro: [{ required: true, message: '请选择宿舍', trigger: 'onchange' }],\n          specialty: [{ required: true, message: '请输入专业', trigger: 'blur' },],\n          clsname: [{ required: true, message: '请输入班级', trigger: 'blur' },],\n      },\n\n\n      btnLoading: false, //按钮是否在加载中\n\n\n    };\n  },\n  mounted() {},\n  created() {\n\n  },\n  methods: {\n    login() {\n      let that = this;\n\n      if (that.loginModel.username == \"\") {\n        that.$message({\n          message: \"请输入账号\",\n          type: \"warning\",\n        });\n        return;\n      }\n      if (that.loginModel.password == \"\") {\n        that.$message({\n          message: \"请输入密码\",\n          type: \"warning\",\n        });\n        return;\n      }\n\n      this.loading = true;\n     var role = that.loginModel.radio; //获取身份\nif (role == '管理员') {\n      let url = base + \"/admin/login\";\n      this.loginModel2.aname = this.loginModel.username;\n      this.loginModel2.loginpassword = this.loginModel.password;\n      request.post(url, this.loginModel2).then((res) => {\n        this.loading = false;\n        if (res.code == 200) {\n          console.log(JSON.stringify(res.resdata));\n          sessionStorage.setItem(\"user\", JSON.stringify(res.resdata));\n          sessionStorage.setItem(\"userLname\", res.resdata.aname);\n          sessionStorage.setItem(\"role\", \"管理员\");\n          this.$router.push(\"/main\");\n        } else {\n          this.$message({\n            message: res.msg,\n            type: \"error\",\n          });\n        }\n      });\n          }\nelse if (role == '宿管阿姨') {\n      let url = base + \"/hostess/login\";\n      this.loginModel2.hno = this.loginModel.username;\n      this.loginModel2.password = this.loginModel.password;\n      request.post(url, this.loginModel2).then((res) => {\n        this.loading = false;\n        if (res.code == 200) {\n          console.log(JSON.stringify(res.resdata));\n          sessionStorage.setItem(\"user\", JSON.stringify(res.resdata));\n          sessionStorage.setItem(\"userLname\", res.resdata.hno);\n          sessionStorage.setItem(\"role\", \"宿管阿姨\");\n          this.$router.push(\"/main\");\n        } else {\n          this.$message({\n            message: res.msg,\n            type: \"error\",\n          });\n        }\n      });\n          }\nelse if (role == '维修员') {\n      let url = base + \"/repairmen/login\";\n      this.loginModel2.rno = this.loginModel.username;\n      this.loginModel2.password = this.loginModel.password;\n      request.post(url, this.loginModel2).then((res) => {\n        this.loading = false;\n        if (res.code == 200) {\n          console.log(JSON.stringify(res.resdata));\n          sessionStorage.setItem(\"user\", JSON.stringify(res.resdata));\n          sessionStorage.setItem(\"userLname\", res.resdata.rno);\n          sessionStorage.setItem(\"role\", \"维修员\");\n          this.$router.push(\"/main\");\n        } else {\n          this.$message({\n            message: res.msg,\n            type: \"error\",\n          });\n        }\n      });\n          }\nelse if (role == '学生') {\n      let url = base + \"/student/login\";\n      this.loginModel2.sno = this.loginModel.username;\n      this.loginModel2.password = this.loginModel.password;\n      request.post(url, this.loginModel2).then((res) => {\n        this.loading = false;\n        if (res.code == 200) {\n          console.log(JSON.stringify(res.resdata));\n          sessionStorage.setItem(\"user\", JSON.stringify(res.resdata));\n          sessionStorage.setItem(\"userLname\", res.resdata.sno);\n          sessionStorage.setItem(\"role\", \"学生\");\n          this.$router.push(\"/main\");\n        } else {\n          this.$message({\n            message: res.msg,\n            type: \"error\",\n          });\n        }\n      });\n          }\n\n\n    },\n\n    toreg() {\n    this.formVisible = true;\n    this.add = true;\n    this.isClear = true;\n    this.rules = this.addrules;\n    this.getdormbuildingList(); // 加载宿舍楼列表\n    this.$nextTick(() => {\n        this.$refs[\"formDataRef\"].resetFields();\n    });\n},\n\n//注册\nreg() {\n    //表单验证\n    this.$refs[\"formDataRef\"].validate((valid) => {\n\n        if (valid) {\n            let url = base + \"/student/add\"; //请求地址\n            this.btnLoading = true; //按钮加载状态\n            request.post(url, this.formData).then((res) => { //请求接口\n                if (res.code == 200) {\n                    this.$message({\n                        message: \"恭喜您，注册成功，请登录！\",\n                        type: \"success\",\n                        offset: 320,\n                    });\n                    this.formVisible = false; //关闭表单\n                    this.btnLoading = false; //按钮加载状态\n                    this.$refs[\"formDataRef\"].resetFields(); //重置表单\n                    this.$refs[\"formDataRef\"].clearValidate();\n                }\n                else if (res.code == 201) {\n                    this.$message({\n                        message: res.msg,\n                        type: \"error\",\n                        offset: 320,\n                    });\n                }\n                else {\n                    this.$message({\n                        message: \"服务器错误\",\n                        type: \"error\",\n                        offset: 320,\n                    });\n                }\n            });\n        }\n    });\n},\n// 获取宿舍楼列表\ngetdormbuildingList() {\n  let para = {};\n  let url = base + \"/dormbuilding/list?currentPage=1&pageSize=1000\";\n  request.post(url, para).then((res) => {\n    this.dormbuildingList = res.resdata;\n  });\n},\n\n// 宿舍楼变化时的处理\nonBuildingChange() {\n  this.formData.doro = ''; // 清空宿舍选择\n  this.updateDormitoryList();\n},\n\n// 性别变化时的处理\nonGenderChange() {\n  this.formData.doro = ''; // 清空宿舍选择\n  this.updateDormitoryList();\n},\n\n// 更新宿舍列表\nupdateDormitoryList() {\n  if (!this.formData.dbid || !this.formData.sex) {\n    this.filteredDormitoryList = [];\n    return;\n  }\n\n  let para = {\n    dbid: this.formData.dbid,\n    dorgender: this.formData.sex\n  };\n  let url = base + \"/dormitory/listByBuildingAndGender\";\n  request.post(url, para).then((res) => {\n    if (res.code == 200) {\n      this.filteredDormitoryList = res.resdata;\n    } else {\n      this.filteredDormitoryList = [];\n      this.$message({\n        message: res.msg || \"获取宿舍列表失败\",\n        type: \"error\",\n        offset: 320,\n      });\n    }\n  }).catch(() => {\n    this.filteredDormitoryList = [];\n    this.$message({\n      message: \"获取宿舍列表失败\",\n      type: \"error\",\n      offset: 320,\n    });\n  });\n},\n\n\n\n\n\n  },\n};\n</script>\n\n<style scoped>\n@import url(../assets/css/htstyle.css);\n\n</style>\n\n\n"]}]}