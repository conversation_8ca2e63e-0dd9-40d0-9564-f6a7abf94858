{"version": 3, "names": ["_helperFunctionName", "require", "_template", "_t", "blockStatement", "callExpression", "functionExpression", "isAssignmentPattern", "isFunctionDeclaration", "isRestElement", "returnStatement", "isCallExpression", "buildAnonymousExpressionWrapper", "template", "expression", "buildNamedExpressionWrapper", "buildDeclarationWrapper", "statements", "classOrObjectMethod", "path", "callId", "node", "body", "container", "async", "generator", "get", "unwrapFunctionEnvironment", "plainFunction", "inPath", "noNewArrows", "ignoreFunctionLength", "functionId", "nodeParams", "params", "isArrowFunctionExpression", "_path$arrowFunctionTo", "arrowFunctionToExpression", "isDeclaration", "built", "id", "type", "param", "push", "scope", "generateUidIdentifier", "wrapperArgs", "NAME", "REF", "name", "FUNCTION", "PARAMS", "replaceWith", "insertAfter", "returnFn", "callee", "argument", "nameFunction", "parent", "length", "wrapFunction", "isMethod"], "sources": ["../src/index.ts"], "sourcesContent": ["import type { NodePath } from \"@babel/traverse\";\nimport nameFunction from \"@babel/helper-function-name\";\nimport template from \"@babel/template\";\nimport {\n  blockStatement,\n  callExpression,\n  functionExpression,\n  isAssignmentPattern,\n  isFunctionDeclaration,\n  isRestElement,\n  returnStatement,\n  isCallExpression,\n} from \"@babel/types\";\nimport type * as t from \"@babel/types\";\n\ntype ExpressionWrapperBuilder<ExtraBody extends t.Node[]> = (\n  replacements?: Parameters<ReturnType<typeof template.expression>>[0],\n) => t.CallExpression & {\n  callee: t.FunctionExpression & {\n    body: {\n      body: [\n        t.VariableDeclaration & {\n          declarations: [\n            { init: t.FunctionExpression | t.ArrowFunctionExpression },\n          ];\n        },\n        ...ExtraBody,\n      ];\n    };\n  };\n};\n\nconst buildAnonymousExpressionWrapper = template.expression(`\n  (function () {\n    var REF = FUNCTION;\n    return function NAME(PARAMS) {\n      return REF.apply(this, arguments);\n    };\n  })()\n`) as ExpressionWrapperBuilder<\n  [t.ReturnStatement & { argument: t.FunctionExpression }]\n>;\n\nconst buildNamedExpressionWrapper = template.expression(`\n  (function () {\n    var REF = FUNCTION;\n    function NAME(PARAMS) {\n      return REF.apply(this, arguments);\n    }\n    return NAME;\n  })()\n`) as ExpressionWrapperBuilder<\n  [t.FunctionDeclaration, t.ReturnStatement & { argument: t.Identifier }]\n>;\n\nconst buildDeclarationWrapper = template.statements(`\n  function NAME(PARAMS) { return REF.apply(this, arguments); }\n  function REF() {\n    REF = FUNCTION;\n    return REF.apply(this, arguments);\n  }\n`);\n\nfunction classOrObjectMethod(\n  path: NodePath<t.ClassMethod | t.ClassPrivateMethod | t.ObjectMethod>,\n  callId: t.Expression,\n) {\n  const node = path.node;\n  const body = node.body;\n\n  const container = functionExpression(\n    null,\n    [],\n    blockStatement(body.body),\n    true,\n  );\n  body.body = [\n    returnStatement(callExpression(callExpression(callId, [container]), [])),\n  ];\n\n  // Regardless of whether or not the wrapped function is a an async method\n  // or generator the outer function should not be\n  node.async = false;\n  node.generator = false;\n\n  // Unwrap the wrapper IIFE's environment so super and this and such still work.\n  (\n    path.get(\"body.body.0.argument.callee.arguments.0\") as NodePath\n  ).unwrapFunctionEnvironment();\n}\n\nfunction plainFunction(\n  inPath: NodePath<Exclude<t.Function, t.Method>>,\n  callId: t.Expression,\n  noNewArrows: boolean,\n  ignoreFunctionLength: boolean,\n) {\n  let path: NodePath<\n    | t.FunctionDeclaration\n    | t.FunctionExpression\n    | t.CallExpression\n    | t.ArrowFunctionExpression\n  > = inPath;\n  let node;\n  let functionId = null;\n  const nodeParams = inPath.node.params;\n\n  if (path.isArrowFunctionExpression()) {\n    if (process.env.BABEL_8_BREAKING) {\n      path = path.arrowFunctionToExpression({ noNewArrows });\n    } else {\n      // arrowFunctionToExpression returns undefined in @babel/traverse < 7.18.10\n      path = path.arrowFunctionToExpression({ noNewArrows }) ?? path;\n    }\n    node = path.node as\n      | t.FunctionDeclaration\n      | t.FunctionExpression\n      | t.CallExpression;\n  } else {\n    node = path.node as t.FunctionDeclaration | t.FunctionExpression;\n  }\n\n  const isDeclaration = isFunctionDeclaration(node);\n\n  let built = node;\n  if (!isCallExpression(node)) {\n    functionId = node.id;\n    node.id = null;\n    node.type = \"FunctionExpression\";\n    built = callExpression(callId, [\n      node as Exclude<typeof node, t.FunctionDeclaration>,\n    ]);\n  }\n\n  const params: t.Identifier[] = [];\n  for (const param of nodeParams) {\n    if (isAssignmentPattern(param) || isRestElement(param)) {\n      break;\n    }\n    params.push(path.scope.generateUidIdentifier(\"x\"));\n  }\n\n  const wrapperArgs = {\n    NAME: functionId || null,\n    REF: path.scope.generateUidIdentifier(functionId ? functionId.name : \"ref\"),\n    FUNCTION: built,\n    PARAMS: params,\n  };\n\n  if (isDeclaration) {\n    const container = buildDeclarationWrapper(wrapperArgs);\n    path.replaceWith(container[0]);\n    path.insertAfter(container[1]);\n  } else {\n    let container;\n\n    if (functionId) {\n      container = buildNamedExpressionWrapper(wrapperArgs);\n    } else {\n      container = buildAnonymousExpressionWrapper(wrapperArgs);\n\n      const returnFn = container.callee.body.body[1].argument;\n      nameFunction({\n        node: returnFn,\n        parent: (path as NodePath<t.FunctionExpression>).parent,\n        scope: path.scope,\n      });\n      functionId = returnFn.id;\n    }\n\n    if (functionId || (!ignoreFunctionLength && params.length)) {\n      path.replaceWith(container);\n    } else {\n      // we can omit this wrapper as the conditions it protects for do not apply\n      path.replaceWith(built);\n    }\n  }\n}\n\nexport default function wrapFunction(\n  path: NodePath<t.Function>,\n  callId: t.Expression,\n  // TODO(Babel 8): Consider defaulting to false for spec compliance\n  noNewArrows: boolean = true,\n  ignoreFunctionLength: boolean = false,\n) {\n  if (path.isMethod()) {\n    classOrObjectMethod(path, callId);\n  } else {\n    plainFunction(\n      path as NodePath<Exclude<t.Function, t.Method>>,\n      callId,\n      noNewArrows,\n      ignoreFunctionLength,\n    );\n  }\n}\n"], "mappings": ";;;;;;AACA,IAAAA,mBAAA,GAAAC,OAAA;AACA,IAAAC,SAAA,GAAAD,OAAA;AACA,IAAAE,EAAA,GAAAF,OAAA;AASsB;EARpBG,cAAc;EACdC,cAAc;EACdC,kBAAkB;EAClBC,mBAAmB;EACnBC,qBAAqB;EACrBC,aAAa;EACbC,eAAe;EACfC;AAAgB,IAAAR,EAAA;AAqBlB,MAAMS,+BAA+B,GAAGC,iBAAQ,CAACC,UAAU,CAAE;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAEA;AAED,MAAMC,2BAA2B,GAAGF,iBAAQ,CAACC,UAAU,CAAE;AACzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAEA;AAED,MAAME,uBAAuB,GAAGH,iBAAQ,CAACI,UAAU,CAAE;AACrD;AACA;AACA;AACA;AACA;AACA,CAAC,CAAC;AAEF,SAASC,mBAAmBA,CAC1BC,IAAqE,EACrEC,MAAoB,EACpB;EACA,MAAMC,IAAI,GAAGF,IAAI,CAACE,IAAI;EACtB,MAAMC,IAAI,GAAGD,IAAI,CAACC,IAAI;EAEtB,MAAMC,SAAS,GAAGjB,kBAAkB,CAClC,IAAI,EACJ,EAAE,EACFF,cAAc,CAACkB,IAAI,CAACA,IAAI,CAAC,EACzB,IACF,CAAC;EACDA,IAAI,CAACA,IAAI,GAAG,CACVZ,eAAe,CAACL,cAAc,CAACA,cAAc,CAACe,MAAM,EAAE,CAACG,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CACzE;EAIDF,IAAI,CAACG,KAAK,GAAG,KAAK;EAClBH,IAAI,CAACI,SAAS,GAAG,KAAK;EAIpBN,IAAI,CAACO,GAAG,CAAC,yCAAyC,CAAC,CACnDC,yBAAyB,CAAC,CAAC;AAC/B;AAEA,SAASC,aAAaA,CACpBC,MAA+C,EAC/CT,MAAoB,EACpBU,WAAoB,EACpBC,oBAA6B,EAC7B;EACA,IAAIZ,IAKH,GAAGU,MAAM;EACV,IAAIR,IAAI;EACR,IAAIW,UAAU,GAAG,IAAI;EACrB,MAAMC,UAAU,GAAGJ,MAAM,CAACR,IAAI,CAACa,MAAM;EAErC,IAAIf,IAAI,CAACgB,yBAAyB,CAAC,CAAC,EAAE;IAG7B;MAAA,IAAAC,qBAAA;MAELjB,IAAI,IAAAiB,qBAAA,GAAGjB,IAAI,CAACkB,yBAAyB,CAAC;QAAEP;MAAY,CAAC,CAAC,YAAAM,qBAAA,GAAIjB,IAAI;IAChE;IACAE,IAAI,GAAGF,IAAI,CAACE,IAGQ;EACtB,CAAC,MAAM;IACLA,IAAI,GAAGF,IAAI,CAACE,IAAoD;EAClE;EAEA,MAAMiB,aAAa,GAAG9B,qBAAqB,CAACa,IAAI,CAAC;EAEjD,IAAIkB,KAAK,GAAGlB,IAAI;EAChB,IAAI,CAACV,gBAAgB,CAACU,IAAI,CAAC,EAAE;IAC3BW,UAAU,GAAGX,IAAI,CAACmB,EAAE;IACpBnB,IAAI,CAACmB,EAAE,GAAG,IAAI;IACdnB,IAAI,CAACoB,IAAI,GAAG,oBAAoB;IAChCF,KAAK,GAAGlC,cAAc,CAACe,MAAM,EAAE,CAC7BC,IAAI,CACL,CAAC;EACJ;EAEA,MAAMa,MAAsB,GAAG,EAAE;EACjC,KAAK,MAAMQ,KAAK,IAAIT,UAAU,EAAE;IAC9B,IAAI1B,mBAAmB,CAACmC,KAAK,CAAC,IAAIjC,aAAa,CAACiC,KAAK,CAAC,EAAE;MACtD;IACF;IACAR,MAAM,CAACS,IAAI,CAACxB,IAAI,CAACyB,KAAK,CAACC,qBAAqB,CAAC,GAAG,CAAC,CAAC;EACpD;EAEA,MAAMC,WAAW,GAAG;IAClBC,IAAI,EAAEf,UAAU,IAAI,IAAI;IACxBgB,GAAG,EAAE7B,IAAI,CAACyB,KAAK,CAACC,qBAAqB,CAACb,UAAU,GAAGA,UAAU,CAACiB,IAAI,GAAG,KAAK,CAAC;IAC3EC,QAAQ,EAAEX,KAAK;IACfY,MAAM,EAAEjB;EACV,CAAC;EAED,IAAII,aAAa,EAAE;IACjB,MAAMf,SAAS,GAAGP,uBAAuB,CAAC8B,WAAW,CAAC;IACtD3B,IAAI,CAACiC,WAAW,CAAC7B,SAAS,CAAC,CAAC,CAAC,CAAC;IAC9BJ,IAAI,CAACkC,WAAW,CAAC9B,SAAS,CAAC,CAAC,CAAC,CAAC;EAChC,CAAC,MAAM;IACL,IAAIA,SAAS;IAEb,IAAIS,UAAU,EAAE;MACdT,SAAS,GAAGR,2BAA2B,CAAC+B,WAAW,CAAC;IACtD,CAAC,MAAM;MACLvB,SAAS,GAAGX,+BAA+B,CAACkC,WAAW,CAAC;MAExD,MAAMQ,QAAQ,GAAG/B,SAAS,CAACgC,MAAM,CAACjC,IAAI,CAACA,IAAI,CAAC,CAAC,CAAC,CAACkC,QAAQ;MACvD,IAAAC,2BAAY,EAAC;QACXpC,IAAI,EAAEiC,QAAQ;QACdI,MAAM,EAAGvC,IAAI,CAAoCuC,MAAM;QACvDd,KAAK,EAAEzB,IAAI,CAACyB;MACd,CAAC,CAAC;MACFZ,UAAU,GAAGsB,QAAQ,CAACd,EAAE;IAC1B;IAEA,IAAIR,UAAU,IAAK,CAACD,oBAAoB,IAAIG,MAAM,CAACyB,MAAO,EAAE;MAC1DxC,IAAI,CAACiC,WAAW,CAAC7B,SAAS,CAAC;IAC7B,CAAC,MAAM;MAELJ,IAAI,CAACiC,WAAW,CAACb,KAAK,CAAC;IACzB;EACF;AACF;AAEe,SAASqB,YAAYA,CAClCzC,IAA0B,EAC1BC,MAAoB,EAEpBU,WAAoB,GAAG,IAAI,EAC3BC,oBAA6B,GAAG,KAAK,EACrC;EACA,IAAIZ,IAAI,CAAC0C,QAAQ,CAAC,CAAC,EAAE;IACnB3C,mBAAmB,CAACC,IAAI,EAAEC,MAAM,CAAC;EACnC,CAAC,MAAM;IACLQ,aAAa,CACXT,IAAI,EACJC,MAAM,EACNU,WAAW,EACXC,oBACF,CAAC;EACH;AACF"}