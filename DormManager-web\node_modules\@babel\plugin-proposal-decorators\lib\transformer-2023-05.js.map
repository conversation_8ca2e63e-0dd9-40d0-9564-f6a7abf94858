{"version": 3, "names": ["_core", "require", "_pluginSyntaxDecorators", "_helperReplaceSupers", "_helperSplitExportDeclaration", "incrementId", "id", "idx", "length", "unshift", "current", "createPrivateUidGeneratorForClass", "classPath", "currentPrivateId", "privateNames", "Set", "traverse", "PrivateName", "path", "add", "node", "name", "reifiedId", "String", "fromCharCode", "has", "t", "privateName", "identifier", "createLazyPrivateUidGeneratorForClass", "generator", "replaceClassWithVar", "type", "varId", "scope", "generateUidIdentifierBasedOnNode", "classId", "rename", "insertBefore", "variableDeclaration", "variableDeclarator", "get", "replaceWith", "cloneNode", "className", "parent", "generateDeclaredUidIdentifier", "parentPath", "newClassExpr", "classExpression", "superClass", "body", "newPath", "sequenceExpression", "generateClassProperty", "key", "value", "isStatic", "classPrivateProperty", "undefined", "classProperty", "addProxyAccessorsFor", "element", "original<PERSON>ey", "<PERSON><PERSON><PERSON>", "version", "isComputed", "static", "thisArg", "thisExpression", "getterBody", "blockStatement", "returnStatement", "memberExpression", "setterBody", "expressionStatement", "assignmentExpression", "getter", "setter", "classPrivateMethod", "classMethod", "insertAfter", "extractProxyAccessorsFor", "template", "expression", "ast", "FIELD", "ACCESSOR", "METHOD", "GETTER", "SETTER", "STATIC_OLD_VERSION", "STATIC", "DECORATORS_HAVE_THIS", "getElementKind", "kind", "isDecoratorInfo", "info", "filteredOrderedDecoratorInfo", "filtered", "filter", "el", "generateDecorationList", "decorators", "decoratorsThis", "decsCount", "hasOneThis", "some", "Boolean", "decs", "i", "push", "unaryExpression", "numericLiteral", "hasThis", "generateDecorationExprs", "arrayExpression", "map", "flag", "privateMethods", "extractElementLocalAssignments", "decorationInfo", "localIds", "locals", "Array", "isArray", "addCallAccessorsFor", "getId", "setId", "callExpression", "isNotTsParameter", "movePrivateAccessor", "methodLocalVar", "params", "block", "isClassDecoratableElementPath", "staticBlockToIIFE", "arrowFunctionExpression", "maybeSequenceExpression", "exprs", "transformClass", "state", "constant<PERSON>uper", "classDecorators", "hasElementDecorators", "generateClassPrivateUid", "computed", "newId", "valueNode", "newField", "elementDecoratorInfo", "firstFieldPath", "constructorPath", "requiresProtoInit", "requiresStaticInit", "decoratedPrivateMethods", "protoInitLocal", "staticInitLocal", "classInitLocal", "classIdLocal", "assignments", "scopeParent", "memoiseExpression", "hint", "localEvaluatedId", "Map", "maybeExtractDecorator", "decorator", "isMemberExpression", "object", "is<PERSON><PERSON><PERSON>", "isThisExpression", "set", "classDecorator", "generateUidIdentifier", "lastInstancePrivateName", "needsInstancePrivateBrandCheck", "hasDecorators", "decoratorPath", "isPrivate", "isClassPrivateProperty", "isClassMethod", "newFieldInitId", "newValue", "initId", "valuePath", "v", "replaceSupers", "ReplaceSupers", "methodPath", "objectRef", "superRef", "file", "refToPreserve", "replace", "async", "isAsync", "functionExpression", "remove", "nameExpr", "stringLiteral", "d", "elementDecorations", "classDecorationsFlag", "classDecorations", "dec", "elementLocals", "protoInitCall", "CallExpression", "exit", "skip", "super", "spreadElement", "restElement", "size", "parentParentPath", "left", "buildCodeFrameError", "classLocals", "classInitInjected", "classInitCall", "originalClass", "statics", "staticBlocks", "for<PERSON>ach", "isStaticBlock", "isProperty", "isClassProperty", "isClassPrivateMethod", "allValues", "staticsClass", "addHelper", "staticBlock", "toStatement", "constructorBody", "newExpr", "newExpression", "arguments", "maybeGenerateMemoised", "createLocalsAssignment", "expr", "crawl", "maybePrivateBranName", "lhs", "rhs", "args", "availableHelper", "arrayPattern", "objectPattern", "objectProperty", "_default", "assertVersion", "assumption", "loose", "_assumption", "VISITED", "WeakSet", "inherits", "syntaxDecorators", "visitor", "ExportNamedDeclaration|ExportDefaultDeclaration", "_declaration$decorato", "declaration", "splitExportDeclaration", "Class"], "sources": ["../src/transformer-2023-05.ts"], "sourcesContent": ["import type { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from \"@babel/traverse\";\nimport { types as t, template } from \"@babel/core\";\nimport syntaxDecorators from \"@babel/plugin-syntax-decorators\";\nimport ReplaceSupers from \"@babel/helper-replace-supers\";\nimport splitExportDeclaration from \"@babel/helper-split-export-declaration\";\nimport * as charCodes from \"charcodes\";\nimport type { PluginAPI, PluginObject, PluginPass } from \"@babel/core\";\nimport type { Options } from \"./index.ts\";\n\ntype ClassDecoratableElement =\n  | t.ClassMethod\n  | t.ClassPrivateMethod\n  | t.ClassProperty\n  | t.ClassPrivateProperty\n  | t.ClassAccessorProperty;\n\ntype ClassElement =\n  | ClassDecoratableElement\n  | t.TSDeclareMethod\n  | t.TSIndexSignature\n  | t.StaticBlock;\n\ntype DecoratorVersionKind = \"2023-05\" | \"2023-01\" | \"2022-03\" | \"2021-12\";\n\nfunction incrementId(id: number[], idx = id.length - 1): void {\n  // If index is -1, id needs an additional character, unshift A\n  if (idx === -1) {\n    id.unshift(charCodes.uppercaseA);\n    return;\n  }\n\n  const current = id[idx];\n\n  if (current === charCodes.uppercaseZ) {\n    // if current is Z, skip to a\n    id[idx] = charCodes.lowercaseA;\n  } else if (current === charCodes.lowercaseZ) {\n    // if current is z, reset to A and carry the 1\n    id[idx] = charCodes.uppercaseA;\n    incrementId(id, idx - 1);\n  } else {\n    // else, increment by one\n    id[idx] = current + 1;\n  }\n}\n\n/**\n * Generates a new private name that is unique to the given class. This can be\n * used to create extra class fields and methods for the implementation, while\n * keeping the length of those names as small as possible. This is important for\n * minification purposes (though private names can generally be minified,\n * transpilations and polyfills cannot yet).\n */\nfunction createPrivateUidGeneratorForClass(\n  classPath: NodePath<t.ClassDeclaration | t.ClassExpression>,\n): () => t.PrivateName {\n  const currentPrivateId: number[] = [];\n  const privateNames = new Set<string>();\n\n  classPath.traverse({\n    PrivateName(path) {\n      privateNames.add(path.node.id.name);\n    },\n  });\n\n  return (): t.PrivateName => {\n    let reifiedId;\n    do {\n      incrementId(currentPrivateId);\n      reifiedId = String.fromCharCode(...currentPrivateId);\n    } while (privateNames.has(reifiedId));\n\n    return t.privateName(t.identifier(reifiedId));\n  };\n}\n\n/**\n * Wraps the above generator function so that it's run lazily the first time\n * it's actually required. Several types of decoration do not require this, so it\n * saves iterating the class elements an additional time and allocating the space\n * for the Sets of element names.\n */\nfunction createLazyPrivateUidGeneratorForClass(\n  classPath: NodePath<t.ClassDeclaration | t.ClassExpression>,\n): () => t.PrivateName {\n  let generator: () => t.PrivateName;\n\n  return (): t.PrivateName => {\n    if (!generator) {\n      generator = createPrivateUidGeneratorForClass(classPath);\n    }\n\n    return generator();\n  };\n}\n\n/**\n * Takes a class definition and replaces it with an equivalent class declaration\n * which is then assigned to a local variable. This allows us to reassign the\n * local variable with the decorated version of the class. The class definition\n * retains its original name so that `toString` is not affected, other\n * references to the class are renamed instead.\n */\nfunction replaceClassWithVar(\n  path: NodePath<t.ClassDeclaration | t.ClassExpression>,\n): [t.Identifier, NodePath<t.ClassDeclaration | t.ClassExpression>] {\n  if (path.type === \"ClassDeclaration\") {\n    const varId = path.scope.generateUidIdentifierBasedOnNode(path.node.id);\n    const classId = t.identifier(path.node.id.name);\n\n    path.scope.rename(classId.name, varId.name);\n\n    path.insertBefore(\n      t.variableDeclaration(\"let\", [t.variableDeclarator(varId)]),\n    );\n    path.get(\"id\").replaceWith(classId);\n\n    return [t.cloneNode(varId), path];\n  } else {\n    let className: string;\n    let varId: t.Identifier;\n\n    if (path.node.id) {\n      className = path.node.id.name;\n      varId = path.scope.parent.generateDeclaredUidIdentifier(className);\n      path.scope.rename(className, varId.name);\n    } else if (\n      path.parentPath.node.type === \"VariableDeclarator\" &&\n      path.parentPath.node.id.type === \"Identifier\"\n    ) {\n      className = path.parentPath.node.id.name;\n      varId = path.scope.parent.generateDeclaredUidIdentifier(className);\n    } else {\n      varId =\n        path.scope.parent.generateDeclaredUidIdentifier(\"decorated_class\");\n    }\n\n    const newClassExpr = t.classExpression(\n      className && t.identifier(className),\n      path.node.superClass,\n      path.node.body,\n    );\n\n    const [newPath] = path.replaceWith(\n      t.sequenceExpression([newClassExpr, varId]),\n    );\n\n    return [\n      t.cloneNode(varId),\n      newPath.get(\"expressions.0\") as NodePath<t.ClassExpression>,\n    ];\n  }\n}\n\nfunction generateClassProperty(\n  key: t.PrivateName | t.Identifier,\n  value: t.Expression | undefined,\n  isStatic: boolean,\n): t.ClassPrivateProperty | t.ClassProperty {\n  if (key.type === \"PrivateName\") {\n    return t.classPrivateProperty(key, value, undefined, isStatic);\n  } else {\n    return t.classProperty(key, value, undefined, undefined, isStatic);\n  }\n}\n\nfunction addProxyAccessorsFor(\n  className: t.Identifier,\n  element: NodePath<ClassDecoratableElement>,\n  originalKey: t.PrivateName | t.Expression,\n  targetKey: t.PrivateName,\n  version: DecoratorVersionKind,\n  isComputed = false,\n): void {\n  const { static: isStatic } = element.node;\n\n  const thisArg =\n    version === \"2023-05\" && isStatic ? className : t.thisExpression();\n\n  const getterBody = t.blockStatement([\n    t.returnStatement(\n      t.memberExpression(t.cloneNode(thisArg), t.cloneNode(targetKey)),\n    ),\n  ]);\n\n  const setterBody = t.blockStatement([\n    t.expressionStatement(\n      t.assignmentExpression(\n        \"=\",\n        t.memberExpression(t.cloneNode(thisArg), t.cloneNode(targetKey)),\n        t.identifier(\"v\"),\n      ),\n    ),\n  ]);\n\n  let getter: t.ClassMethod | t.ClassPrivateMethod,\n    setter: t.ClassMethod | t.ClassPrivateMethod;\n\n  if (originalKey.type === \"PrivateName\") {\n    getter = t.classPrivateMethod(\n      \"get\",\n      t.cloneNode(originalKey),\n      [],\n      getterBody,\n      isStatic,\n    );\n    setter = t.classPrivateMethod(\n      \"set\",\n      t.cloneNode(originalKey),\n      [t.identifier(\"v\")],\n      setterBody,\n      isStatic,\n    );\n  } else {\n    getter = t.classMethod(\n      \"get\",\n      t.cloneNode(originalKey),\n      [],\n      getterBody,\n      isComputed,\n      isStatic,\n    );\n    setter = t.classMethod(\n      \"set\",\n      t.cloneNode(originalKey),\n      [t.identifier(\"v\")],\n      setterBody,\n      isComputed,\n      isStatic,\n    );\n  }\n\n  element.insertAfter(setter);\n  element.insertAfter(getter);\n}\n\nfunction extractProxyAccessorsFor(\n  targetKey: t.PrivateName,\n  version: DecoratorVersionKind,\n): (t.FunctionExpression | t.ArrowFunctionExpression)[] {\n  if (version !== \"2023-05\" && version !== \"2023-01\") {\n    return [\n      template.expression.ast`\n        function () {\n          return this.${t.cloneNode(targetKey)};\n        }\n      ` as t.FunctionExpression,\n      template.expression.ast`\n        function (value) {\n          this.${t.cloneNode(targetKey)} = value;\n        }\n      ` as t.FunctionExpression,\n    ];\n  }\n  return [\n    template.expression.ast`\n      o => o.${t.cloneNode(targetKey)}\n    ` as t.ArrowFunctionExpression,\n    template.expression.ast`\n      (o, v) => o.${t.cloneNode(targetKey)} = v\n    ` as t.ArrowFunctionExpression,\n  ];\n}\n\n// 3 bits reserved to this (0-7)\nconst FIELD = 0;\nconst ACCESSOR = 1;\nconst METHOD = 2;\nconst GETTER = 3;\nconst SETTER = 4;\n\nconst STATIC_OLD_VERSION = 5; // Before 2023-05\nconst STATIC = 8; // 1 << 3\nconst DECORATORS_HAVE_THIS = 16; // 1 << 3\n\nfunction getElementKind(element: NodePath<ClassDecoratableElement>): number {\n  switch (element.node.type) {\n    case \"ClassProperty\":\n    case \"ClassPrivateProperty\":\n      return FIELD;\n    case \"ClassAccessorProperty\":\n      return ACCESSOR;\n    case \"ClassMethod\":\n    case \"ClassPrivateMethod\":\n      if (element.node.kind === \"get\") {\n        return GETTER;\n      } else if (element.node.kind === \"set\") {\n        return SETTER;\n      } else {\n        return METHOD;\n      }\n  }\n}\n\n// Information about the decorators applied to an element\ninterface DecoratorInfo {\n  // The expressions of the decorators themselves\n  decorators: t.Expression[];\n  decoratorsThis: t.Expression[];\n\n  // The kind of the decorated value, matches the kind value passed to applyDecs\n  kind: number;\n\n  // whether or not the field is static\n  isStatic: boolean;\n\n  // The name of the decorator\n  name: t.StringLiteral | t.Expression;\n\n  privateMethods:\n    | (t.FunctionExpression | t.ArrowFunctionExpression)[]\n    | undefined;\n\n  // The names of local variables that will be used/returned from the decoration\n  locals: t.Identifier | t.Identifier[] | undefined;\n}\n\n// Information about a computed property key. These must be evaluated\n// interspersed with decorator expressions, which is why they get added to the\n// array of DecoratorInfos later on.\ninterface ComputedPropInfo {\n  localComputedNameId: t.Identifier;\n  keyNode: t.Expression;\n}\n\nfunction isDecoratorInfo(\n  info: DecoratorInfo | ComputedPropInfo,\n): info is DecoratorInfo {\n  return \"decorators\" in info;\n}\n\nfunction filteredOrderedDecoratorInfo(\n  info: (DecoratorInfo | ComputedPropInfo)[],\n): DecoratorInfo[] {\n  const filtered = info.filter(isDecoratorInfo);\n\n  return [\n    ...filtered.filter(\n      el => el.isStatic && el.kind >= ACCESSOR && el.kind <= SETTER,\n    ),\n    ...filtered.filter(\n      el => !el.isStatic && el.kind >= ACCESSOR && el.kind <= SETTER,\n    ),\n    ...filtered.filter(el => el.isStatic && el.kind === FIELD),\n    ...filtered.filter(el => !el.isStatic && el.kind === FIELD),\n  ];\n}\n\nfunction generateDecorationList(\n  decorators: t.Expression[],\n  decoratorsThis: (t.Expression | null)[],\n  version: DecoratorVersionKind,\n) {\n  const decsCount = decorators.length;\n  const hasOneThis = decoratorsThis.some(Boolean);\n  const decs: t.Expression[] = [];\n  for (let i = 0; i < decsCount; i++) {\n    if (version === \"2023-05\" && hasOneThis) {\n      decs.push(\n        decoratorsThis[i] || t.unaryExpression(\"void\", t.numericLiteral(0)),\n      );\n    }\n    decs.push(decorators[i]);\n  }\n\n  return { hasThis: hasOneThis, decs };\n}\n\nfunction generateDecorationExprs(\n  info: (DecoratorInfo | ComputedPropInfo)[],\n  version: DecoratorVersionKind,\n): t.ArrayExpression {\n  return t.arrayExpression(\n    filteredOrderedDecoratorInfo(info).map(el => {\n      const { decs, hasThis } = generateDecorationList(\n        el.decorators,\n        el.decoratorsThis,\n        version,\n      );\n\n      let flag = el.kind;\n      if (el.isStatic) {\n        flag += version === \"2023-05\" ? STATIC : STATIC_OLD_VERSION;\n      }\n      if (hasThis) flag += DECORATORS_HAVE_THIS;\n\n      return t.arrayExpression([\n        decs.length === 1 ? decs[0] : t.arrayExpression(decs),\n        t.numericLiteral(flag),\n        el.name,\n        ...(el.privateMethods || []),\n      ]);\n    }),\n  );\n}\n\nfunction extractElementLocalAssignments(\n  decorationInfo: (DecoratorInfo | ComputedPropInfo)[],\n) {\n  const localIds: t.Identifier[] = [];\n\n  for (const el of filteredOrderedDecoratorInfo(decorationInfo)) {\n    const { locals } = el;\n\n    if (Array.isArray(locals)) {\n      localIds.push(...locals);\n    } else if (locals !== undefined) {\n      localIds.push(locals);\n    }\n  }\n\n  return localIds;\n}\n\nfunction addCallAccessorsFor(\n  element: NodePath,\n  key: t.PrivateName,\n  getId: t.Identifier,\n  setId: t.Identifier,\n) {\n  element.insertAfter(\n    t.classPrivateMethod(\n      \"get\",\n      t.cloneNode(key),\n      [],\n      t.blockStatement([\n        t.returnStatement(\n          t.callExpression(t.cloneNode(getId), [t.thisExpression()]),\n        ),\n      ]),\n    ),\n  );\n\n  element.insertAfter(\n    t.classPrivateMethod(\n      \"set\",\n      t.cloneNode(key),\n      [t.identifier(\"v\")],\n      t.blockStatement([\n        t.expressionStatement(\n          t.callExpression(t.cloneNode(setId), [\n            t.thisExpression(),\n            t.identifier(\"v\"),\n          ]),\n        ),\n      ]),\n    ),\n  );\n}\n\nfunction isNotTsParameter(\n  node: t.Identifier | t.Pattern | t.RestElement | t.TSParameterProperty,\n): node is t.Identifier | t.Pattern | t.RestElement {\n  return node.type !== \"TSParameterProperty\";\n}\n\nfunction movePrivateAccessor(\n  element: NodePath<t.ClassPrivateMethod>,\n  key: t.PrivateName,\n  methodLocalVar: t.Identifier,\n  isStatic: boolean,\n) {\n  let params: (t.Identifier | t.RestElement)[];\n  let block: t.Statement[];\n\n  if (element.node.kind === \"set\") {\n    params = [t.identifier(\"v\")];\n    block = [\n      t.expressionStatement(\n        t.callExpression(methodLocalVar, [\n          t.thisExpression(),\n          t.identifier(\"v\"),\n        ]),\n      ),\n    ];\n  } else {\n    params = [];\n    block = [\n      t.returnStatement(t.callExpression(methodLocalVar, [t.thisExpression()])),\n    ];\n  }\n\n  element.replaceWith(\n    t.classPrivateMethod(\n      element.node.kind,\n      t.cloneNode(key),\n      params,\n      t.blockStatement(block),\n      isStatic,\n    ),\n  );\n}\n\nfunction isClassDecoratableElementPath(\n  path: NodePath<ClassElement>,\n): path is NodePath<ClassDecoratableElement> {\n  const { type } = path;\n\n  return (\n    type !== \"TSDeclareMethod\" &&\n    type !== \"TSIndexSignature\" &&\n    type !== \"StaticBlock\"\n  );\n}\n\nfunction staticBlockToIIFE(block: t.StaticBlock) {\n  return t.callExpression(\n    t.arrowFunctionExpression([], t.blockStatement(block.body)),\n    [],\n  );\n}\n\nfunction maybeSequenceExpression(exprs: t.Expression[]) {\n  if (exprs.length === 0) return t.unaryExpression(\"void\", t.numericLiteral(0));\n  if (exprs.length === 1) return exprs[0];\n  return t.sequenceExpression(exprs);\n}\n\nfunction transformClass(\n  path: NodePath<t.ClassExpression | t.ClassDeclaration>,\n  state: PluginPass,\n  constantSuper: boolean,\n  version: DecoratorVersionKind,\n): NodePath {\n  const body = path.get(\"body.body\");\n\n  const classDecorators = path.node.decorators;\n  let hasElementDecorators = false;\n\n  const generateClassPrivateUid = createLazyPrivateUidGeneratorForClass(path);\n\n  // Iterate over the class to see if we need to decorate it, and also to\n  // transform simple auto accessors which are not decorated\n  for (const element of body) {\n    if (!isClassDecoratableElementPath(element)) {\n      continue;\n    }\n\n    if (element.node.decorators && element.node.decorators.length > 0) {\n      hasElementDecorators = true;\n    } else if (element.node.type === \"ClassAccessorProperty\") {\n      const { key, value, static: isStatic, computed } = element.node;\n\n      const newId = generateClassPrivateUid();\n\n      const valueNode = value ? t.cloneNode(value) : undefined;\n\n      const newField = generateClassProperty(newId, valueNode, isStatic);\n\n      const [newPath] = element.replaceWith(newField);\n      addProxyAccessorsFor(\n        path.node.id,\n        newPath,\n        key,\n        newId,\n        version,\n        computed,\n      );\n    }\n  }\n\n  // If nothing is decorated, return\n  if (!classDecorators && !hasElementDecorators) return;\n\n  const elementDecoratorInfo: (DecoratorInfo | ComputedPropInfo)[] = [];\n\n  // The initializer of the first non-static field will be injected with the protoInit call\n  let firstFieldPath:\n    | NodePath<t.ClassProperty | t.ClassPrivateProperty>\n    | undefined;\n  let constructorPath: NodePath<t.ClassMethod> | undefined;\n  let requiresProtoInit = false;\n  let requiresStaticInit = false;\n  const decoratedPrivateMethods = new Set<string>();\n\n  let protoInitLocal: t.Identifier,\n    staticInitLocal: t.Identifier,\n    classInitLocal: t.Identifier,\n    classIdLocal: t.Identifier;\n  const assignments: t.AssignmentExpression[] = [];\n  const scopeParent: Scope = path.scope.parent;\n\n  const memoiseExpression = (expression: t.Expression, hint: string) => {\n    const localEvaluatedId = scopeParent.generateDeclaredUidIdentifier(hint);\n    assignments.push(t.assignmentExpression(\"=\", localEvaluatedId, expression));\n    return t.cloneNode(localEvaluatedId);\n  };\n\n  const decoratorsThis = new Map<t.Decorator, t.Expression>();\n  const maybeExtractDecorator = (decorator: t.Decorator) => {\n    const { expression } = decorator;\n    if (version === \"2023-05\" && t.isMemberExpression(expression)) {\n      let object;\n      if (\n        t.isSuper(expression.object) ||\n        t.isThisExpression(expression.object)\n      ) {\n        object = memoiseExpression(t.thisExpression(), \"obj\");\n      } else if (!scopeParent.isStatic(expression.object)) {\n        object = memoiseExpression(expression.object, \"obj\");\n        expression.object = object;\n      } else {\n        object = expression.object;\n      }\n      decoratorsThis.set(decorator, t.cloneNode(object));\n    }\n    if (!scopeParent.isStatic(expression)) {\n      decorator.expression = memoiseExpression(expression, \"dec\");\n    }\n  };\n\n  if (classDecorators) {\n    classInitLocal = scopeParent.generateDeclaredUidIdentifier(\"initClass\");\n\n    const [classId, classPath] = replaceClassWithVar(path);\n    path = classPath;\n    classIdLocal = classId;\n\n    path.node.decorators = null;\n\n    for (const classDecorator of classDecorators) {\n      maybeExtractDecorator(classDecorator);\n    }\n  } else {\n    if (!path.node.id) {\n      path.node.id = path.scope.generateUidIdentifier(\"Class\");\n    }\n    classIdLocal = t.cloneNode(path.node.id);\n  }\n\n  let lastInstancePrivateName: t.PrivateName;\n  let needsInstancePrivateBrandCheck = false;\n\n  if (hasElementDecorators) {\n    for (const element of body) {\n      if (!isClassDecoratableElementPath(element)) {\n        continue;\n      }\n\n      const { node } = element;\n      const decorators = element.get(\"decorators\");\n\n      const hasDecorators = Array.isArray(decorators) && decorators.length > 0;\n\n      if (hasDecorators) {\n        for (const decoratorPath of decorators) {\n          maybeExtractDecorator(decoratorPath.node);\n        }\n      }\n\n      const isComputed =\n        \"computed\" in element.node && element.node.computed === true;\n      if (isComputed) {\n        if (!scopeParent.isStatic(node.key)) {\n          node.key = memoiseExpression(node.key as t.Expression, \"computedKey\");\n        }\n      }\n\n      const kind = getElementKind(element);\n      const { key } = node;\n\n      const isPrivate = key.type === \"PrivateName\";\n\n      const isStatic = !!element.node.static;\n\n      let name = \"computedKey\";\n\n      if (isPrivate) {\n        name = key.id.name;\n      } else if (!isComputed && key.type === \"Identifier\") {\n        name = key.name;\n      }\n\n      if (isPrivate && !isStatic) {\n        if (hasDecorators) {\n          needsInstancePrivateBrandCheck = true;\n        }\n        if (t.isClassPrivateProperty(node) || !lastInstancePrivateName) {\n          lastInstancePrivateName = key;\n        }\n      }\n\n      if (element.isClassMethod({ kind: \"constructor\" })) {\n        constructorPath = element;\n      }\n\n      if (hasDecorators) {\n        let locals: t.Identifier | t.Identifier[];\n        let privateMethods: Array<\n          t.FunctionExpression | t.ArrowFunctionExpression\n        >;\n\n        if (kind === ACCESSOR) {\n          const { value } = element.node as t.ClassAccessorProperty;\n\n          const params: t.Expression[] = [t.thisExpression()];\n\n          if (value) {\n            params.push(t.cloneNode(value));\n          }\n\n          const newId = generateClassPrivateUid();\n          const newFieldInitId =\n            element.scope.parent.generateDeclaredUidIdentifier(`init_${name}`);\n          const newValue = t.callExpression(\n            t.cloneNode(newFieldInitId),\n            params,\n          );\n\n          const newField = generateClassProperty(newId, newValue, isStatic);\n          const [newPath] = element.replaceWith(newField);\n\n          if (isPrivate) {\n            privateMethods = extractProxyAccessorsFor(newId, version);\n\n            const getId = newPath.scope.parent.generateDeclaredUidIdentifier(\n              `get_${name}`,\n            );\n            const setId = newPath.scope.parent.generateDeclaredUidIdentifier(\n              `set_${name}`,\n            );\n\n            addCallAccessorsFor(newPath, key, getId, setId);\n\n            locals = [newFieldInitId, getId, setId];\n          } else {\n            addProxyAccessorsFor(\n              path.node.id,\n              newPath,\n              key,\n              newId,\n              version,\n              isComputed,\n            );\n            locals = newFieldInitId;\n          }\n        } else if (kind === FIELD) {\n          const initId = element.scope.parent.generateDeclaredUidIdentifier(\n            `init_${name}`,\n          );\n          const valuePath = (\n            element as NodePath<t.ClassProperty | t.ClassPrivateProperty>\n          ).get(\"value\");\n\n          valuePath.replaceWith(\n            t.callExpression(\n              t.cloneNode(initId),\n              [t.thisExpression(), valuePath.node].filter(v => v),\n            ),\n          );\n\n          locals = initId;\n\n          if (isPrivate) {\n            privateMethods = extractProxyAccessorsFor(key, version);\n          }\n        } else if (isPrivate) {\n          locals = element.scope.parent.generateDeclaredUidIdentifier(\n            `call_${name}`,\n          );\n\n          const replaceSupers = new ReplaceSupers({\n            constantSuper,\n            methodPath: element as NodePath<t.ClassPrivateMethod>,\n            objectRef: classIdLocal,\n            superRef: path.node.superClass,\n            file: state.file,\n            refToPreserve: classIdLocal,\n          });\n\n          replaceSupers.replace();\n\n          const {\n            params,\n            body,\n            async: isAsync,\n          } = element.node as t.ClassPrivateMethod;\n\n          privateMethods = [\n            t.functionExpression(\n              undefined,\n              params.filter(isNotTsParameter),\n              body,\n              isAsync,\n            ),\n          ];\n\n          if (kind === GETTER || kind === SETTER) {\n            movePrivateAccessor(\n              element as NodePath<t.ClassPrivateMethod>,\n              t.cloneNode(key),\n              t.cloneNode(locals),\n              isStatic,\n            );\n          } else {\n            const node = element.node as t.ClassPrivateMethod;\n\n            // Unshift\n            path.node.body.body.unshift(\n              t.classPrivateProperty(key, t.cloneNode(locals), [], node.static),\n            );\n\n            decoratedPrivateMethods.add(key.id.name);\n\n            element.remove();\n          }\n        }\n\n        let nameExpr: t.Expression;\n\n        if (isComputed) {\n          nameExpr = t.cloneNode(key as t.Expression);\n        } else if (key.type === \"PrivateName\") {\n          nameExpr = t.stringLiteral(key.id.name);\n        } else if (key.type === \"Identifier\") {\n          nameExpr = t.stringLiteral(key.name);\n        } else {\n          nameExpr = t.cloneNode(key as t.Expression);\n        }\n\n        elementDecoratorInfo.push({\n          kind,\n          decorators: decorators.map(d => d.node.expression),\n          decoratorsThis: decorators.map(d => decoratorsThis.get(d.node)),\n          name: nameExpr,\n          isStatic,\n          privateMethods,\n          locals,\n        });\n\n        if (kind !== FIELD) {\n          if (isStatic) {\n            requiresStaticInit = true;\n          } else {\n            requiresProtoInit = true;\n          }\n        }\n\n        if (element.node) {\n          element.node.decorators = null;\n        }\n\n        if (\n          !firstFieldPath &&\n          !isStatic &&\n          (kind === FIELD || kind === ACCESSOR)\n        ) {\n          firstFieldPath = element as NodePath<\n            t.ClassProperty | t.ClassPrivateProperty\n          >;\n        }\n      }\n    }\n  }\n\n  const elementDecorations = generateDecorationExprs(\n    elementDecoratorInfo,\n    version,\n  );\n  let classDecorationsFlag = 0;\n  let classDecorations: t.Expression[] = [];\n  if (classDecorators) {\n    const { hasThis, decs } = generateDecorationList(\n      classDecorators.map(el => el.expression),\n      classDecorators.map(dec => decoratorsThis.get(dec)),\n      version,\n    );\n    classDecorationsFlag = hasThis ? 1 : 0;\n    classDecorations = decs;\n  }\n\n  const elementLocals: t.Identifier[] =\n    extractElementLocalAssignments(elementDecoratorInfo);\n\n  if (requiresProtoInit) {\n    protoInitLocal = scopeParent.generateDeclaredUidIdentifier(\"initProto\");\n    elementLocals.push(protoInitLocal);\n\n    const protoInitCall = t.callExpression(t.cloneNode(protoInitLocal), [\n      t.thisExpression(),\n    ]);\n\n    if (firstFieldPath) {\n      const value = firstFieldPath.get(\"value\");\n      const body: t.Expression[] = [protoInitCall];\n\n      if (value.node) {\n        body.push(value.node);\n      }\n\n      value.replaceWith(t.sequenceExpression(body));\n    } else if (constructorPath) {\n      if (path.node.superClass) {\n        path.traverse({\n          CallExpression: {\n            exit(path) {\n              if (!path.get(\"callee\").isSuper()) return;\n\n              path.replaceWith(\n                t.callExpression(t.cloneNode(protoInitLocal), [path.node]),\n              );\n\n              path.skip();\n            },\n          },\n        });\n      } else {\n        constructorPath.node.body.body.unshift(\n          t.expressionStatement(protoInitCall),\n        );\n      }\n    } else {\n      const body: t.Statement[] = [t.expressionStatement(protoInitCall)];\n\n      if (path.node.superClass) {\n        body.unshift(\n          t.expressionStatement(\n            t.callExpression(t.super(), [\n              t.spreadElement(t.identifier(\"args\")),\n            ]),\n          ),\n        );\n      }\n\n      path.node.body.body.unshift(\n        t.classMethod(\n          \"constructor\",\n          t.identifier(\"constructor\"),\n          [t.restElement(t.identifier(\"args\"))],\n          t.blockStatement(body),\n        ),\n      );\n    }\n  }\n\n  if (requiresStaticInit) {\n    staticInitLocal = scopeParent.generateDeclaredUidIdentifier(\"initStatic\");\n    elementLocals.push(staticInitLocal);\n  }\n\n  if (decoratedPrivateMethods.size > 0) {\n    path.traverse({\n      PrivateName(path) {\n        if (!decoratedPrivateMethods.has(path.node.id.name)) return;\n\n        const parentPath = path.parentPath;\n        const parentParentPath = parentPath.parentPath;\n\n        if (\n          // this.bar().#x = 123;\n          (parentParentPath.node.type === \"AssignmentExpression\" &&\n            parentParentPath.node.left === parentPath.node) ||\n          // this.#x++;\n          parentParentPath.node.type === \"UpdateExpression\" ||\n          // ([...this.#x] = foo);\n          parentParentPath.node.type === \"RestElement\" ||\n          // ([this.#x] = foo);\n          parentParentPath.node.type === \"ArrayPattern\" ||\n          // ({ a: this.#x } = bar);\n          (parentParentPath.node.type === \"ObjectProperty\" &&\n            parentParentPath.node.value === parentPath.node &&\n            parentParentPath.parentPath.type === \"ObjectPattern\") ||\n          // for (this.#x of []);\n          (parentParentPath.node.type === \"ForOfStatement\" &&\n            parentParentPath.node.left === parentPath.node)\n        ) {\n          throw path.buildCodeFrameError(\n            `Decorated private methods are not updatable, but \"#${path.node.id.name}\" is updated via this expression.`,\n          );\n        }\n      },\n    });\n  }\n\n  const classLocals: t.Identifier[] = [];\n  let classInitInjected = false;\n  const classInitCall =\n    classInitLocal && t.callExpression(t.cloneNode(classInitLocal), []);\n\n  const originalClass = path.node;\n\n  if (classDecorators) {\n    classLocals.push(classIdLocal, classInitLocal);\n    const statics: (\n      | t.ClassProperty\n      | t.ClassPrivateProperty\n      | t.ClassPrivateMethod\n    )[] = [];\n    let staticBlocks: t.StaticBlock[] = [];\n    path.get(\"body.body\").forEach(element => {\n      // Static blocks cannot be compiled to \"instance blocks\", but we can inline\n      // them as IIFEs in the next property.\n      if (element.isStaticBlock()) {\n        staticBlocks.push(element.node);\n        element.remove();\n        return;\n      }\n\n      const isProperty =\n        element.isClassProperty() || element.isClassPrivateProperty();\n\n      if (\n        (isProperty || element.isClassPrivateMethod()) &&\n        element.node.static\n      ) {\n        if (isProperty && staticBlocks.length > 0) {\n          const allValues: t.Expression[] = staticBlocks.map(staticBlockToIIFE);\n          if (element.node.value) allValues.push(element.node.value);\n          element.node.value = maybeSequenceExpression(allValues);\n          staticBlocks = [];\n        }\n\n        element.node.static = false;\n        statics.push(element.node);\n        element.remove();\n      }\n    });\n\n    if (statics.length > 0 || staticBlocks.length > 0) {\n      const staticsClass = template.expression.ast`\n        class extends ${state.addHelper(\"identity\")} {}\n      ` as t.ClassExpression;\n      staticsClass.body.body = [\n        t.staticBlock([\n          t.toStatement(originalClass, true) ||\n            // If toStatement returns false, originalClass must be an anonymous ClassExpression,\n            // because `export default @dec ...` has been handled in the export visitor before.\n            t.expressionStatement(originalClass as t.ClassExpression),\n        ]),\n        ...statics,\n      ];\n\n      const constructorBody: t.Expression[] = [];\n\n      const newExpr = t.newExpression(staticsClass, []);\n\n      if (staticBlocks.length > 0) {\n        constructorBody.push(...staticBlocks.map(staticBlockToIIFE));\n      }\n      if (classInitCall) {\n        classInitInjected = true;\n        constructorBody.push(classInitCall);\n      }\n      if (constructorBody.length > 0) {\n        constructorBody.unshift(\n          t.callExpression(t.super(), [t.cloneNode(classIdLocal)]),\n        );\n\n        staticsClass.body.body.push(\n          t.classMethod(\n            \"constructor\",\n            t.identifier(\"constructor\"),\n            [],\n            t.blockStatement([\n              t.expressionStatement(t.sequenceExpression(constructorBody)),\n            ]),\n          ),\n        );\n      } else {\n        newExpr.arguments.push(t.cloneNode(classIdLocal));\n      }\n\n      path.replaceWith(newExpr);\n    }\n  }\n  if (!classInitInjected && classInitCall) {\n    path.node.body.body.push(\n      t.staticBlock([t.expressionStatement(classInitCall)]),\n    );\n  }\n\n  let { superClass } = originalClass;\n  if (superClass && (process.env.BABEL_8_BREAKING || version === \"2023-05\")) {\n    const id = path.scope.maybeGenerateMemoised(superClass);\n    if (id) {\n      originalClass.superClass = t.assignmentExpression(\"=\", id, superClass);\n      superClass = id;\n    }\n  }\n\n  originalClass.body.body.unshift(\n    t.staticBlock(\n      [\n        t.expressionStatement(\n          createLocalsAssignment(\n            elementLocals,\n            classLocals,\n            elementDecorations,\n            t.arrayExpression(classDecorations),\n            t.numericLiteral(classDecorationsFlag),\n            needsInstancePrivateBrandCheck ? lastInstancePrivateName : null,\n            t.cloneNode(superClass),\n            state,\n            version,\n          ),\n        ),\n        requiresStaticInit &&\n          t.expressionStatement(\n            t.callExpression(t.cloneNode(staticInitLocal), [\n              t.thisExpression(),\n            ]),\n          ),\n      ].filter(Boolean),\n    ),\n  );\n\n  // When path is a ClassExpression, path.insertBefore will convert `path`\n  // into a SequenceExpression\n  path.insertBefore(assignments.map(expr => t.expressionStatement(expr)));\n\n  // Recrawl the scope to make sure new identifiers are properly synced\n  path.scope.crawl();\n\n  return path;\n}\n\nfunction createLocalsAssignment(\n  elementLocals: t.Identifier[],\n  classLocals: t.Identifier[],\n  elementDecorations: t.ArrayExpression,\n  classDecorations: t.ArrayExpression,\n  classDecorationsFlag: t.NumericLiteral,\n  maybePrivateBranName: t.PrivateName | null,\n  superClass: null | t.Expression,\n  state: PluginPass,\n  version: DecoratorVersionKind,\n) {\n  let lhs, rhs;\n  const args: t.Expression[] = [\n    t.thisExpression(),\n    elementDecorations,\n    classDecorations,\n  ];\n\n  if (!process.env.BABEL_8_BREAKING) {\n    if (\n      version === \"2021-12\" ||\n      (version === \"2022-03\" && !state.availableHelper(\"applyDecs2203R\"))\n    ) {\n      const lhs = t.arrayPattern([...elementLocals, ...classLocals]);\n      const rhs = t.callExpression(\n        state.addHelper(version === \"2021-12\" ? \"applyDecs\" : \"applyDecs2203\"),\n        args,\n      );\n      return t.assignmentExpression(\"=\", lhs, rhs);\n    }\n  }\n\n  if (process.env.BABEL_8_BREAKING || version === \"2023-05\") {\n    if (\n      maybePrivateBranName ||\n      superClass ||\n      classDecorationsFlag.value !== 0\n    ) {\n      args.push(classDecorationsFlag);\n    }\n    if (maybePrivateBranName) {\n      args.push(\n        template.expression.ast`\n            _ => ${t.cloneNode(maybePrivateBranName)} in _\n          ` as t.ArrowFunctionExpression,\n      );\n    } else if (superClass) {\n      args.push(t.unaryExpression(\"void\", t.numericLiteral(0)));\n    }\n    if (superClass) args.push(superClass);\n    rhs = t.callExpression(state.addHelper(\"applyDecs2305\"), args);\n  } else if (version === \"2023-01\") {\n    if (maybePrivateBranName) {\n      args.push(\n        template.expression.ast`\n            _ => ${t.cloneNode(maybePrivateBranName)} in _\n          ` as t.ArrowFunctionExpression,\n      );\n    }\n    rhs = t.callExpression(state.addHelper(\"applyDecs2301\"), args);\n  } else {\n    rhs = t.callExpression(state.addHelper(\"applyDecs2203R\"), args);\n  }\n  // optimize `{ c: [classLocals] } = applyapplyDecs2203R(...)` to\n  // `[classLocals] = applyapplyDecs2203R(...).c`\n  if (elementLocals.length > 0) {\n    if (classLocals.length > 0) {\n      lhs = t.objectPattern([\n        t.objectProperty(t.identifier(\"e\"), t.arrayPattern(elementLocals)),\n        t.objectProperty(t.identifier(\"c\"), t.arrayPattern(classLocals)),\n      ]);\n    } else {\n      lhs = t.arrayPattern(elementLocals);\n      rhs = t.memberExpression(rhs, t.identifier(\"e\"), false, false);\n    }\n  } else {\n    // invariant: classLocals.length > 0\n    lhs = t.arrayPattern(classLocals);\n    rhs = t.memberExpression(rhs, t.identifier(\"c\"), false, false);\n  }\n\n  return t.assignmentExpression(\"=\", lhs, rhs);\n}\n\nexport default function (\n  { assertVersion, assumption }: PluginAPI,\n  { loose }: Options,\n  // TODO(Babel 8): Only keep 2023-05\n  version: \"2023-05\" | \"2023-01\" | \"2022-03\" | \"2021-12\",\n): PluginObject {\n  if (process.env.BABEL_8_BREAKING) {\n    assertVersion(\"^7.21.0\");\n  } else {\n    if (version === \"2023-05\" || version === \"2023-01\") {\n      assertVersion(\"^7.21.0\");\n    } else if (version === \"2021-12\") {\n      assertVersion(\"^7.16.0\");\n    } else {\n      assertVersion(\"^7.19.0\");\n    }\n  }\n\n  const VISITED = new WeakSet<NodePath>();\n  const constantSuper = assumption(\"constantSuper\") ?? loose;\n\n  return {\n    name: \"proposal-decorators\",\n    inherits: syntaxDecorators,\n\n    visitor: {\n      \"ExportNamedDeclaration|ExportDefaultDeclaration\"(\n        path: NodePath<t.ExportNamedDeclaration | t.ExportDefaultDeclaration>,\n      ) {\n        const { declaration } = path.node;\n        if (\n          declaration?.type === \"ClassDeclaration\" &&\n          // When compiling class decorators we need to replace the class\n          // binding, so we must split it in two separate declarations.\n          declaration.decorators?.length > 0\n        ) {\n          splitExportDeclaration(path);\n        }\n      },\n\n      Class(path, state) {\n        if (VISITED.has(path)) return;\n\n        const newPath = transformClass(path, state, constantSuper, version);\n        if (newPath) VISITED.add(newPath);\n      },\n    },\n  };\n}\n"], "mappings": ";;;;;;AACA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,uBAAA,GAAAD,OAAA;AACA,IAAAE,oBAAA,GAAAF,OAAA;AACA,IAAAG,6BAAA,GAAAH,OAAA;AAoBA,SAASI,WAAWA,CAACC,EAAY,EAAEC,GAAG,GAAGD,EAAE,CAACE,MAAM,GAAG,CAAC,EAAQ;EAE5D,IAAID,GAAG,KAAK,CAAC,CAAC,EAAE;IACdD,EAAE,CAACG,OAAO,GAAqB,CAAC;IAChC;EACF;EAEA,MAAMC,OAAO,GAAGJ,EAAE,CAACC,GAAG,CAAC;EAEvB,IAAIG,OAAO,OAAyB,EAAE;IAEpCJ,EAAE,CAACC,GAAG,CAAC,KAAuB;EAChC,CAAC,MAAM,IAAIG,OAAO,QAAyB,EAAE;IAE3CJ,EAAE,CAACC,GAAG,CAAC,KAAuB;IAC9BF,WAAW,CAACC,EAAE,EAAEC,GAAG,GAAG,CAAC,CAAC;EAC1B,CAAC,MAAM;IAELD,EAAE,CAACC,GAAG,CAAC,GAAGG,OAAO,GAAG,CAAC;EACvB;AACF;AASA,SAASC,iCAAiCA,CACxCC,SAA2D,EACtC;EACrB,MAAMC,gBAA0B,GAAG,EAAE;EACrC,MAAMC,YAAY,GAAG,IAAIC,GAAG,CAAS,CAAC;EAEtCH,SAAS,CAACI,QAAQ,CAAC;IACjBC,WAAWA,CAACC,IAAI,EAAE;MAChBJ,YAAY,CAACK,GAAG,CAACD,IAAI,CAACE,IAAI,CAACd,EAAE,CAACe,IAAI,CAAC;IACrC;EACF,CAAC,CAAC;EAEF,OAAO,MAAqB;IAC1B,IAAIC,SAAS;IACb,GAAG;MACDjB,WAAW,CAACQ,gBAAgB,CAAC;MAC7BS,SAAS,GAAGC,MAAM,CAACC,YAAY,CAAC,GAAGX,gBAAgB,CAAC;IACtD,CAAC,QAAQC,YAAY,CAACW,GAAG,CAACH,SAAS,CAAC;IAEpC,OAAOI,WAAC,CAACC,WAAW,CAACD,WAAC,CAACE,UAAU,CAACN,SAAS,CAAC,CAAC;EAC/C,CAAC;AACH;AAQA,SAASO,qCAAqCA,CAC5CjB,SAA2D,EACtC;EACrB,IAAIkB,SAA8B;EAElC,OAAO,MAAqB;IAC1B,IAAI,CAACA,SAAS,EAAE;MACdA,SAAS,GAAGnB,iCAAiC,CAACC,SAAS,CAAC;IAC1D;IAEA,OAAOkB,SAAS,CAAC,CAAC;EACpB,CAAC;AACH;AASA,SAASC,mBAAmBA,CAC1Bb,IAAsD,EACY;EAClE,IAAIA,IAAI,CAACc,IAAI,KAAK,kBAAkB,EAAE;IACpC,MAAMC,KAAK,GAAGf,IAAI,CAACgB,KAAK,CAACC,gCAAgC,CAACjB,IAAI,CAACE,IAAI,CAACd,EAAE,CAAC;IACvE,MAAM8B,OAAO,GAAGV,WAAC,CAACE,UAAU,CAACV,IAAI,CAACE,IAAI,CAACd,EAAE,CAACe,IAAI,CAAC;IAE/CH,IAAI,CAACgB,KAAK,CAACG,MAAM,CAACD,OAAO,CAACf,IAAI,EAAEY,KAAK,CAACZ,IAAI,CAAC;IAE3CH,IAAI,CAACoB,YAAY,CACfZ,WAAC,CAACa,mBAAmB,CAAC,KAAK,EAAE,CAACb,WAAC,CAACc,kBAAkB,CAACP,KAAK,CAAC,CAAC,CAC5D,CAAC;IACDf,IAAI,CAACuB,GAAG,CAAC,IAAI,CAAC,CAACC,WAAW,CAACN,OAAO,CAAC;IAEnC,OAAO,CAACV,WAAC,CAACiB,SAAS,CAACV,KAAK,CAAC,EAAEf,IAAI,CAAC;EACnC,CAAC,MAAM;IACL,IAAI0B,SAAiB;IACrB,IAAIX,KAAmB;IAEvB,IAAIf,IAAI,CAACE,IAAI,CAACd,EAAE,EAAE;MAChBsC,SAAS,GAAG1B,IAAI,CAACE,IAAI,CAACd,EAAE,CAACe,IAAI;MAC7BY,KAAK,GAAGf,IAAI,CAACgB,KAAK,CAACW,MAAM,CAACC,6BAA6B,CAACF,SAAS,CAAC;MAClE1B,IAAI,CAACgB,KAAK,CAACG,MAAM,CAACO,SAAS,EAAEX,KAAK,CAACZ,IAAI,CAAC;IAC1C,CAAC,MAAM,IACLH,IAAI,CAAC6B,UAAU,CAAC3B,IAAI,CAACY,IAAI,KAAK,oBAAoB,IAClDd,IAAI,CAAC6B,UAAU,CAAC3B,IAAI,CAACd,EAAE,CAAC0B,IAAI,KAAK,YAAY,EAC7C;MACAY,SAAS,GAAG1B,IAAI,CAAC6B,UAAU,CAAC3B,IAAI,CAACd,EAAE,CAACe,IAAI;MACxCY,KAAK,GAAGf,IAAI,CAACgB,KAAK,CAACW,MAAM,CAACC,6BAA6B,CAACF,SAAS,CAAC;IACpE,CAAC,MAAM;MACLX,KAAK,GACHf,IAAI,CAACgB,KAAK,CAACW,MAAM,CAACC,6BAA6B,CAAC,iBAAiB,CAAC;IACtE;IAEA,MAAME,YAAY,GAAGtB,WAAC,CAACuB,eAAe,CACpCL,SAAS,IAAIlB,WAAC,CAACE,UAAU,CAACgB,SAAS,CAAC,EACpC1B,IAAI,CAACE,IAAI,CAAC8B,UAAU,EACpBhC,IAAI,CAACE,IAAI,CAAC+B,IACZ,CAAC;IAED,MAAM,CAACC,OAAO,CAAC,GAAGlC,IAAI,CAACwB,WAAW,CAChChB,WAAC,CAAC2B,kBAAkB,CAAC,CAACL,YAAY,EAAEf,KAAK,CAAC,CAC5C,CAAC;IAED,OAAO,CACLP,WAAC,CAACiB,SAAS,CAACV,KAAK,CAAC,EAClBmB,OAAO,CAACX,GAAG,CAAC,eAAe,CAAC,CAC7B;EACH;AACF;AAEA,SAASa,qBAAqBA,CAC5BC,GAAiC,EACjCC,KAA+B,EAC/BC,QAAiB,EACyB;EAC1C,IAAIF,GAAG,CAACvB,IAAI,KAAK,aAAa,EAAE;IAC9B,OAAON,WAAC,CAACgC,oBAAoB,CAACH,GAAG,EAAEC,KAAK,EAAEG,SAAS,EAAEF,QAAQ,CAAC;EAChE,CAAC,MAAM;IACL,OAAO/B,WAAC,CAACkC,aAAa,CAACL,GAAG,EAAEC,KAAK,EAAEG,SAAS,EAAEA,SAAS,EAAEF,QAAQ,CAAC;EACpE;AACF;AAEA,SAASI,oBAAoBA,CAC3BjB,SAAuB,EACvBkB,OAA0C,EAC1CC,WAAyC,EACzCC,SAAwB,EACxBC,OAA6B,EAC7BC,UAAU,GAAG,KAAK,EACZ;EACN,MAAM;IAAEC,MAAM,EAAEV;EAAS,CAAC,GAAGK,OAAO,CAAC1C,IAAI;EAEzC,MAAMgD,OAAO,GACXH,OAAO,KAAK,SAAS,IAAIR,QAAQ,GAAGb,SAAS,GAAGlB,WAAC,CAAC2C,cAAc,CAAC,CAAC;EAEpE,MAAMC,UAAU,GAAG5C,WAAC,CAAC6C,cAAc,CAAC,CAClC7C,WAAC,CAAC8C,eAAe,CACf9C,WAAC,CAAC+C,gBAAgB,CAAC/C,WAAC,CAACiB,SAAS,CAACyB,OAAO,CAAC,EAAE1C,WAAC,CAACiB,SAAS,CAACqB,SAAS,CAAC,CACjE,CAAC,CACF,CAAC;EAEF,MAAMU,UAAU,GAAGhD,WAAC,CAAC6C,cAAc,CAAC,CAClC7C,WAAC,CAACiD,mBAAmB,CACnBjD,WAAC,CAACkD,oBAAoB,CACpB,GAAG,EACHlD,WAAC,CAAC+C,gBAAgB,CAAC/C,WAAC,CAACiB,SAAS,CAACyB,OAAO,CAAC,EAAE1C,WAAC,CAACiB,SAAS,CAACqB,SAAS,CAAC,CAAC,EAChEtC,WAAC,CAACE,UAAU,CAAC,GAAG,CAClB,CACF,CAAC,CACF,CAAC;EAEF,IAAIiD,MAA4C,EAC9CC,MAA4C;EAE9C,IAAIf,WAAW,CAAC/B,IAAI,KAAK,aAAa,EAAE;IACtC6C,MAAM,GAAGnD,WAAC,CAACqD,kBAAkB,CAC3B,KAAK,EACLrD,WAAC,CAACiB,SAAS,CAACoB,WAAW,CAAC,EACxB,EAAE,EACFO,UAAU,EACVb,QACF,CAAC;IACDqB,MAAM,GAAGpD,WAAC,CAACqD,kBAAkB,CAC3B,KAAK,EACLrD,WAAC,CAACiB,SAAS,CAACoB,WAAW,CAAC,EACxB,CAACrC,WAAC,CAACE,UAAU,CAAC,GAAG,CAAC,CAAC,EACnB8C,UAAU,EACVjB,QACF,CAAC;EACH,CAAC,MAAM;IACLoB,MAAM,GAAGnD,WAAC,CAACsD,WAAW,CACpB,KAAK,EACLtD,WAAC,CAACiB,SAAS,CAACoB,WAAW,CAAC,EACxB,EAAE,EACFO,UAAU,EACVJ,UAAU,EACVT,QACF,CAAC;IACDqB,MAAM,GAAGpD,WAAC,CAACsD,WAAW,CACpB,KAAK,EACLtD,WAAC,CAACiB,SAAS,CAACoB,WAAW,CAAC,EACxB,CAACrC,WAAC,CAACE,UAAU,CAAC,GAAG,CAAC,CAAC,EACnB8C,UAAU,EACVR,UAAU,EACVT,QACF,CAAC;EACH;EAEAK,OAAO,CAACmB,WAAW,CAACH,MAAM,CAAC;EAC3BhB,OAAO,CAACmB,WAAW,CAACJ,MAAM,CAAC;AAC7B;AAEA,SAASK,wBAAwBA,CAC/BlB,SAAwB,EACxBC,OAA6B,EACyB;EACtD,IAAIA,OAAO,KAAK,SAAS,IAAIA,OAAO,KAAK,SAAS,EAAE;IAClD,OAAO,CACLkB,cAAQ,CAACC,UAAU,CAACC,GAAI;AAC9B;AACA,wBAAwB3D,WAAC,CAACiB,SAAS,CAACqB,SAAS,CAAE;AAC/C;AACA,OAAO,EACDmB,cAAQ,CAACC,UAAU,CAACC,GAAI;AAC9B;AACA,iBAAiB3D,WAAC,CAACiB,SAAS,CAACqB,SAAS,CAAE;AACxC;AACA,OAAO,CACF;EACH;EACA,OAAO,CACLmB,cAAQ,CAACC,UAAU,CAACC,GAAI;AAC5B,eAAe3D,WAAC,CAACiB,SAAS,CAACqB,SAAS,CAAE;AACtC,KAAK,EACDmB,cAAQ,CAACC,UAAU,CAACC,GAAI;AAC5B,oBAAoB3D,WAAC,CAACiB,SAAS,CAACqB,SAAS,CAAE;AAC3C,KAAK,CACF;AACH;AAGA,MAAMsB,KAAK,GAAG,CAAC;AACf,MAAMC,QAAQ,GAAG,CAAC;AAClB,MAAMC,MAAM,GAAG,CAAC;AAChB,MAAMC,MAAM,GAAG,CAAC;AAChB,MAAMC,MAAM,GAAG,CAAC;AAEhB,MAAMC,kBAAkB,GAAG,CAAC;AAC5B,MAAMC,MAAM,GAAG,CAAC;AAChB,MAAMC,oBAAoB,GAAG,EAAE;AAE/B,SAASC,cAAcA,CAAChC,OAA0C,EAAU;EAC1E,QAAQA,OAAO,CAAC1C,IAAI,CAACY,IAAI;IACvB,KAAK,eAAe;IACpB,KAAK,sBAAsB;MACzB,OAAOsD,KAAK;IACd,KAAK,uBAAuB;MAC1B,OAAOC,QAAQ;IACjB,KAAK,aAAa;IAClB,KAAK,oBAAoB;MACvB,IAAIzB,OAAO,CAAC1C,IAAI,CAAC2E,IAAI,KAAK,KAAK,EAAE;QAC/B,OAAON,MAAM;MACf,CAAC,MAAM,IAAI3B,OAAO,CAAC1C,IAAI,CAAC2E,IAAI,KAAK,KAAK,EAAE;QACtC,OAAOL,MAAM;MACf,CAAC,MAAM;QACL,OAAOF,MAAM;MACf;EACJ;AACF;AAiCA,SAASQ,eAAeA,CACtBC,IAAsC,EACf;EACvB,OAAO,YAAY,IAAIA,IAAI;AAC7B;AAEA,SAASC,4BAA4BA,CACnCD,IAA0C,EACzB;EACjB,MAAME,QAAQ,GAAGF,IAAI,CAACG,MAAM,CAACJ,eAAe,CAAC;EAE7C,OAAO,CACL,GAAGG,QAAQ,CAACC,MAAM,CAChBC,EAAE,IAAIA,EAAE,CAAC5C,QAAQ,IAAI4C,EAAE,CAACN,IAAI,IAAIR,QAAQ,IAAIc,EAAE,CAACN,IAAI,IAAIL,MACzD,CAAC,EACD,GAAGS,QAAQ,CAACC,MAAM,CAChBC,EAAE,IAAI,CAACA,EAAE,CAAC5C,QAAQ,IAAI4C,EAAE,CAACN,IAAI,IAAIR,QAAQ,IAAIc,EAAE,CAACN,IAAI,IAAIL,MAC1D,CAAC,EACD,GAAGS,QAAQ,CAACC,MAAM,CAACC,EAAE,IAAIA,EAAE,CAAC5C,QAAQ,IAAI4C,EAAE,CAACN,IAAI,KAAKT,KAAK,CAAC,EAC1D,GAAGa,QAAQ,CAACC,MAAM,CAACC,EAAE,IAAI,CAACA,EAAE,CAAC5C,QAAQ,IAAI4C,EAAE,CAACN,IAAI,KAAKT,KAAK,CAAC,CAC5D;AACH;AAEA,SAASgB,sBAAsBA,CAC7BC,UAA0B,EAC1BC,cAAuC,EACvCvC,OAA6B,EAC7B;EACA,MAAMwC,SAAS,GAAGF,UAAU,CAAC/F,MAAM;EACnC,MAAMkG,UAAU,GAAGF,cAAc,CAACG,IAAI,CAACC,OAAO,CAAC;EAC/C,MAAMC,IAAoB,GAAG,EAAE;EAC/B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,SAAS,EAAEK,CAAC,EAAE,EAAE;IAClC,IAAI7C,OAAO,KAAK,SAAS,IAAIyC,UAAU,EAAE;MACvCG,IAAI,CAACE,IAAI,CACPP,cAAc,CAACM,CAAC,CAAC,IAAIpF,WAAC,CAACsF,eAAe,CAAC,MAAM,EAAEtF,WAAC,CAACuF,cAAc,CAAC,CAAC,CAAC,CACpE,CAAC;IACH;IACAJ,IAAI,CAACE,IAAI,CAACR,UAAU,CAACO,CAAC,CAAC,CAAC;EAC1B;EAEA,OAAO;IAAEI,OAAO,EAAER,UAAU;IAAEG;EAAK,CAAC;AACtC;AAEA,SAASM,uBAAuBA,CAC9BlB,IAA0C,EAC1ChC,OAA6B,EACV;EACnB,OAAOvC,WAAC,CAAC0F,eAAe,CACtBlB,4BAA4B,CAACD,IAAI,CAAC,CAACoB,GAAG,CAAChB,EAAE,IAAI;IAC3C,MAAM;MAAEQ,IAAI;MAAEK;IAAQ,CAAC,GAAGZ,sBAAsB,CAC9CD,EAAE,CAACE,UAAU,EACbF,EAAE,CAACG,cAAc,EACjBvC,OACF,CAAC;IAED,IAAIqD,IAAI,GAAGjB,EAAE,CAACN,IAAI;IAClB,IAAIM,EAAE,CAAC5C,QAAQ,EAAE;MACf6D,IAAI,IAAIrD,OAAO,KAAK,SAAS,GAAG2B,MAAM,GAAGD,kBAAkB;IAC7D;IACA,IAAIuB,OAAO,EAAEI,IAAI,IAAIzB,oBAAoB;IAEzC,OAAOnE,WAAC,CAAC0F,eAAe,CAAC,CACvBP,IAAI,CAACrG,MAAM,KAAK,CAAC,GAAGqG,IAAI,CAAC,CAAC,CAAC,GAAGnF,WAAC,CAAC0F,eAAe,CAACP,IAAI,CAAC,EACrDnF,WAAC,CAACuF,cAAc,CAACK,IAAI,CAAC,EACtBjB,EAAE,CAAChF,IAAI,EACP,IAAIgF,EAAE,CAACkB,cAAc,IAAI,EAAE,CAAC,CAC7B,CAAC;EACJ,CAAC,CACH,CAAC;AACH;AAEA,SAASC,8BAA8BA,CACrCC,cAAoD,EACpD;EACA,MAAMC,QAAwB,GAAG,EAAE;EAEnC,KAAK,MAAMrB,EAAE,IAAIH,4BAA4B,CAACuB,cAAc,CAAC,EAAE;IAC7D,MAAM;MAAEE;IAAO,CAAC,GAAGtB,EAAE;IAErB,IAAIuB,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC,EAAE;MACzBD,QAAQ,CAACX,IAAI,CAAC,GAAGY,MAAM,CAAC;IAC1B,CAAC,MAAM,IAAIA,MAAM,KAAKhE,SAAS,EAAE;MAC/B+D,QAAQ,CAACX,IAAI,CAACY,MAAM,CAAC;IACvB;EACF;EAEA,OAAOD,QAAQ;AACjB;AAEA,SAASI,mBAAmBA,CAC1BhE,OAAiB,EACjBP,GAAkB,EAClBwE,KAAmB,EACnBC,KAAmB,EACnB;EACAlE,OAAO,CAACmB,WAAW,CACjBvD,WAAC,CAACqD,kBAAkB,CAClB,KAAK,EACLrD,WAAC,CAACiB,SAAS,CAACY,GAAG,CAAC,EAChB,EAAE,EACF7B,WAAC,CAAC6C,cAAc,CAAC,CACf7C,WAAC,CAAC8C,eAAe,CACf9C,WAAC,CAACuG,cAAc,CAACvG,WAAC,CAACiB,SAAS,CAACoF,KAAK,CAAC,EAAE,CAACrG,WAAC,CAAC2C,cAAc,CAAC,CAAC,CAAC,CAC3D,CAAC,CACF,CACH,CACF,CAAC;EAEDP,OAAO,CAACmB,WAAW,CACjBvD,WAAC,CAACqD,kBAAkB,CAClB,KAAK,EACLrD,WAAC,CAACiB,SAAS,CAACY,GAAG,CAAC,EAChB,CAAC7B,WAAC,CAACE,UAAU,CAAC,GAAG,CAAC,CAAC,EACnBF,WAAC,CAAC6C,cAAc,CAAC,CACf7C,WAAC,CAACiD,mBAAmB,CACnBjD,WAAC,CAACuG,cAAc,CAACvG,WAAC,CAACiB,SAAS,CAACqF,KAAK,CAAC,EAAE,CACnCtG,WAAC,CAAC2C,cAAc,CAAC,CAAC,EAClB3C,WAAC,CAACE,UAAU,CAAC,GAAG,CAAC,CAClB,CACH,CAAC,CACF,CACH,CACF,CAAC;AACH;AAEA,SAASsG,gBAAgBA,CACvB9G,IAAsE,EACpB;EAClD,OAAOA,IAAI,CAACY,IAAI,KAAK,qBAAqB;AAC5C;AAEA,SAASmG,mBAAmBA,CAC1BrE,OAAuC,EACvCP,GAAkB,EAClB6E,cAA4B,EAC5B3E,QAAiB,EACjB;EACA,IAAI4E,MAAwC;EAC5C,IAAIC,KAAoB;EAExB,IAAIxE,OAAO,CAAC1C,IAAI,CAAC2E,IAAI,KAAK,KAAK,EAAE;IAC/BsC,MAAM,GAAG,CAAC3G,WAAC,CAACE,UAAU,CAAC,GAAG,CAAC,CAAC;IAC5B0G,KAAK,GAAG,CACN5G,WAAC,CAACiD,mBAAmB,CACnBjD,WAAC,CAACuG,cAAc,CAACG,cAAc,EAAE,CAC/B1G,WAAC,CAAC2C,cAAc,CAAC,CAAC,EAClB3C,WAAC,CAACE,UAAU,CAAC,GAAG,CAAC,CAClB,CACH,CAAC,CACF;EACH,CAAC,MAAM;IACLyG,MAAM,GAAG,EAAE;IACXC,KAAK,GAAG,CACN5G,WAAC,CAAC8C,eAAe,CAAC9C,WAAC,CAACuG,cAAc,CAACG,cAAc,EAAE,CAAC1G,WAAC,CAAC2C,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAC1E;EACH;EAEAP,OAAO,CAACpB,WAAW,CACjBhB,WAAC,CAACqD,kBAAkB,CAClBjB,OAAO,CAAC1C,IAAI,CAAC2E,IAAI,EACjBrE,WAAC,CAACiB,SAAS,CAACY,GAAG,CAAC,EAChB8E,MAAM,EACN3G,WAAC,CAAC6C,cAAc,CAAC+D,KAAK,CAAC,EACvB7E,QACF,CACF,CAAC;AACH;AAEA,SAAS8E,6BAA6BA,CACpCrH,IAA4B,EACe;EAC3C,MAAM;IAAEc;EAAK,CAAC,GAAGd,IAAI;EAErB,OACEc,IAAI,KAAK,iBAAiB,IAC1BA,IAAI,KAAK,kBAAkB,IAC3BA,IAAI,KAAK,aAAa;AAE1B;AAEA,SAASwG,iBAAiBA,CAACF,KAAoB,EAAE;EAC/C,OAAO5G,WAAC,CAACuG,cAAc,CACrBvG,WAAC,CAAC+G,uBAAuB,CAAC,EAAE,EAAE/G,WAAC,CAAC6C,cAAc,CAAC+D,KAAK,CAACnF,IAAI,CAAC,CAAC,EAC3D,EACF,CAAC;AACH;AAEA,SAASuF,uBAAuBA,CAACC,KAAqB,EAAE;EACtD,IAAIA,KAAK,CAACnI,MAAM,KAAK,CAAC,EAAE,OAAOkB,WAAC,CAACsF,eAAe,CAAC,MAAM,EAAEtF,WAAC,CAACuF,cAAc,CAAC,CAAC,CAAC,CAAC;EAC7E,IAAI0B,KAAK,CAACnI,MAAM,KAAK,CAAC,EAAE,OAAOmI,KAAK,CAAC,CAAC,CAAC;EACvC,OAAOjH,WAAC,CAAC2B,kBAAkB,CAACsF,KAAK,CAAC;AACpC;AAEA,SAASC,cAAcA,CACrB1H,IAAsD,EACtD2H,KAAiB,EACjBC,aAAsB,EACtB7E,OAA6B,EACnB;EACV,MAAMd,IAAI,GAAGjC,IAAI,CAACuB,GAAG,CAAC,WAAW,CAAC;EAElC,MAAMsG,eAAe,GAAG7H,IAAI,CAACE,IAAI,CAACmF,UAAU;EAC5C,IAAIyC,oBAAoB,GAAG,KAAK;EAEhC,MAAMC,uBAAuB,GAAGpH,qCAAqC,CAACX,IAAI,CAAC;EAI3E,KAAK,MAAM4C,OAAO,IAAIX,IAAI,EAAE;IAC1B,IAAI,CAACoF,6BAA6B,CAACzE,OAAO,CAAC,EAAE;MAC3C;IACF;IAEA,IAAIA,OAAO,CAAC1C,IAAI,CAACmF,UAAU,IAAIzC,OAAO,CAAC1C,IAAI,CAACmF,UAAU,CAAC/F,MAAM,GAAG,CAAC,EAAE;MACjEwI,oBAAoB,GAAG,IAAI;IAC7B,CAAC,MAAM,IAAIlF,OAAO,CAAC1C,IAAI,CAACY,IAAI,KAAK,uBAAuB,EAAE;MACxD,MAAM;QAAEuB,GAAG;QAAEC,KAAK;QAAEW,MAAM,EAAEV,QAAQ;QAAEyF;MAAS,CAAC,GAAGpF,OAAO,CAAC1C,IAAI;MAE/D,MAAM+H,KAAK,GAAGF,uBAAuB,CAAC,CAAC;MAEvC,MAAMG,SAAS,GAAG5F,KAAK,GAAG9B,WAAC,CAACiB,SAAS,CAACa,KAAK,CAAC,GAAGG,SAAS;MAExD,MAAM0F,QAAQ,GAAG/F,qBAAqB,CAAC6F,KAAK,EAAEC,SAAS,EAAE3F,QAAQ,CAAC;MAElE,MAAM,CAACL,OAAO,CAAC,GAAGU,OAAO,CAACpB,WAAW,CAAC2G,QAAQ,CAAC;MAC/CxF,oBAAoB,CAClB3C,IAAI,CAACE,IAAI,CAACd,EAAE,EACZ8C,OAAO,EACPG,GAAG,EACH4F,KAAK,EACLlF,OAAO,EACPiF,QACF,CAAC;IACH;EACF;EAGA,IAAI,CAACH,eAAe,IAAI,CAACC,oBAAoB,EAAE;EAE/C,MAAMM,oBAA0D,GAAG,EAAE;EAGrE,IAAIC,cAES;EACb,IAAIC,eAAoD;EACxD,IAAIC,iBAAiB,GAAG,KAAK;EAC7B,IAAIC,kBAAkB,GAAG,KAAK;EAC9B,MAAMC,uBAAuB,GAAG,IAAI5I,GAAG,CAAS,CAAC;EAEjD,IAAI6I,cAA4B,EAC9BC,eAA6B,EAC7BC,cAA4B,EAC5BC,YAA0B;EAC5B,MAAMC,WAAqC,GAAG,EAAE;EAChD,MAAMC,WAAkB,GAAG/I,IAAI,CAACgB,KAAK,CAACW,MAAM;EAE5C,MAAMqH,iBAAiB,GAAGA,CAAC9E,UAAwB,EAAE+E,IAAY,KAAK;IACpE,MAAMC,gBAAgB,GAAGH,WAAW,CAACnH,6BAA6B,CAACqH,IAAI,CAAC;IACxEH,WAAW,CAACjD,IAAI,CAACrF,WAAC,CAACkD,oBAAoB,CAAC,GAAG,EAAEwF,gBAAgB,EAAEhF,UAAU,CAAC,CAAC;IAC3E,OAAO1D,WAAC,CAACiB,SAAS,CAACyH,gBAAgB,CAAC;EACtC,CAAC;EAED,MAAM5D,cAAc,GAAG,IAAI6D,GAAG,CAA4B,CAAC;EAC3D,MAAMC,qBAAqB,GAAIC,SAAsB,IAAK;IACxD,MAAM;MAAEnF;IAAW,CAAC,GAAGmF,SAAS;IAChC,IAAItG,OAAO,KAAK,SAAS,IAAIvC,WAAC,CAAC8I,kBAAkB,CAACpF,UAAU,CAAC,EAAE;MAC7D,IAAIqF,MAAM;MACV,IACE/I,WAAC,CAACgJ,OAAO,CAACtF,UAAU,CAACqF,MAAM,CAAC,IAC5B/I,WAAC,CAACiJ,gBAAgB,CAACvF,UAAU,CAACqF,MAAM,CAAC,EACrC;QACAA,MAAM,GAAGP,iBAAiB,CAACxI,WAAC,CAAC2C,cAAc,CAAC,CAAC,EAAE,KAAK,CAAC;MACvD,CAAC,MAAM,IAAI,CAAC4F,WAAW,CAACxG,QAAQ,CAAC2B,UAAU,CAACqF,MAAM,CAAC,EAAE;QACnDA,MAAM,GAAGP,iBAAiB,CAAC9E,UAAU,CAACqF,MAAM,EAAE,KAAK,CAAC;QACpDrF,UAAU,CAACqF,MAAM,GAAGA,MAAM;MAC5B,CAAC,MAAM;QACLA,MAAM,GAAGrF,UAAU,CAACqF,MAAM;MAC5B;MACAjE,cAAc,CAACoE,GAAG,CAACL,SAAS,EAAE7I,WAAC,CAACiB,SAAS,CAAC8H,MAAM,CAAC,CAAC;IACpD;IACA,IAAI,CAACR,WAAW,CAACxG,QAAQ,CAAC2B,UAAU,CAAC,EAAE;MACrCmF,SAAS,CAACnF,UAAU,GAAG8E,iBAAiB,CAAC9E,UAAU,EAAE,KAAK,CAAC;IAC7D;EACF,CAAC;EAED,IAAI2D,eAAe,EAAE;IACnBe,cAAc,GAAGG,WAAW,CAACnH,6BAA6B,CAAC,WAAW,CAAC;IAEvE,MAAM,CAACV,OAAO,EAAExB,SAAS,CAAC,GAAGmB,mBAAmB,CAACb,IAAI,CAAC;IACtDA,IAAI,GAAGN,SAAS;IAChBmJ,YAAY,GAAG3H,OAAO;IAEtBlB,IAAI,CAACE,IAAI,CAACmF,UAAU,GAAG,IAAI;IAE3B,KAAK,MAAMsE,cAAc,IAAI9B,eAAe,EAAE;MAC5CuB,qBAAqB,CAACO,cAAc,CAAC;IACvC;EACF,CAAC,MAAM;IACL,IAAI,CAAC3J,IAAI,CAACE,IAAI,CAACd,EAAE,EAAE;MACjBY,IAAI,CAACE,IAAI,CAACd,EAAE,GAAGY,IAAI,CAACgB,KAAK,CAAC4I,qBAAqB,CAAC,OAAO,CAAC;IAC1D;IACAf,YAAY,GAAGrI,WAAC,CAACiB,SAAS,CAACzB,IAAI,CAACE,IAAI,CAACd,EAAE,CAAC;EAC1C;EAEA,IAAIyK,uBAAsC;EAC1C,IAAIC,8BAA8B,GAAG,KAAK;EAE1C,IAAIhC,oBAAoB,EAAE;IACxB,KAAK,MAAMlF,OAAO,IAAIX,IAAI,EAAE;MAC1B,IAAI,CAACoF,6BAA6B,CAACzE,OAAO,CAAC,EAAE;QAC3C;MACF;MAEA,MAAM;QAAE1C;MAAK,CAAC,GAAG0C,OAAO;MACxB,MAAMyC,UAAU,GAAGzC,OAAO,CAACrB,GAAG,CAAC,YAAY,CAAC;MAE5C,MAAMwI,aAAa,GAAGrD,KAAK,CAACC,OAAO,CAACtB,UAAU,CAAC,IAAIA,UAAU,CAAC/F,MAAM,GAAG,CAAC;MAExE,IAAIyK,aAAa,EAAE;QACjB,KAAK,MAAMC,aAAa,IAAI3E,UAAU,EAAE;UACtC+D,qBAAqB,CAACY,aAAa,CAAC9J,IAAI,CAAC;QAC3C;MACF;MAEA,MAAM8C,UAAU,GACd,UAAU,IAAIJ,OAAO,CAAC1C,IAAI,IAAI0C,OAAO,CAAC1C,IAAI,CAAC8H,QAAQ,KAAK,IAAI;MAC9D,IAAIhF,UAAU,EAAE;QACd,IAAI,CAAC+F,WAAW,CAACxG,QAAQ,CAACrC,IAAI,CAACmC,GAAG,CAAC,EAAE;UACnCnC,IAAI,CAACmC,GAAG,GAAG2G,iBAAiB,CAAC9I,IAAI,CAACmC,GAAG,EAAkB,aAAa,CAAC;QACvE;MACF;MAEA,MAAMwC,IAAI,GAAGD,cAAc,CAAChC,OAAO,CAAC;MACpC,MAAM;QAAEP;MAAI,CAAC,GAAGnC,IAAI;MAEpB,MAAM+J,SAAS,GAAG5H,GAAG,CAACvB,IAAI,KAAK,aAAa;MAE5C,MAAMyB,QAAQ,GAAG,CAAC,CAACK,OAAO,CAAC1C,IAAI,CAAC+C,MAAM;MAEtC,IAAI9C,IAAI,GAAG,aAAa;MAExB,IAAI8J,SAAS,EAAE;QACb9J,IAAI,GAAGkC,GAAG,CAACjD,EAAE,CAACe,IAAI;MACpB,CAAC,MAAM,IAAI,CAAC6C,UAAU,IAAIX,GAAG,CAACvB,IAAI,KAAK,YAAY,EAAE;QACnDX,IAAI,GAAGkC,GAAG,CAAClC,IAAI;MACjB;MAEA,IAAI8J,SAAS,IAAI,CAAC1H,QAAQ,EAAE;QAC1B,IAAIwH,aAAa,EAAE;UACjBD,8BAA8B,GAAG,IAAI;QACvC;QACA,IAAItJ,WAAC,CAAC0J,sBAAsB,CAAChK,IAAI,CAAC,IAAI,CAAC2J,uBAAuB,EAAE;UAC9DA,uBAAuB,GAAGxH,GAAG;QAC/B;MACF;MAEA,IAAIO,OAAO,CAACuH,aAAa,CAAC;QAAEtF,IAAI,EAAE;MAAc,CAAC,CAAC,EAAE;QAClDyD,eAAe,GAAG1F,OAAO;MAC3B;MAEA,IAAImH,aAAa,EAAE;QACjB,IAAItD,MAAqC;QACzC,IAAIJ,cAEH;QAED,IAAIxB,IAAI,KAAKR,QAAQ,EAAE;UACrB,MAAM;YAAE/B;UAAM,CAAC,GAAGM,OAAO,CAAC1C,IAA+B;UAEzD,MAAMiH,MAAsB,GAAG,CAAC3G,WAAC,CAAC2C,cAAc,CAAC,CAAC,CAAC;UAEnD,IAAIb,KAAK,EAAE;YACT6E,MAAM,CAACtB,IAAI,CAACrF,WAAC,CAACiB,SAAS,CAACa,KAAK,CAAC,CAAC;UACjC;UAEA,MAAM2F,KAAK,GAAGF,uBAAuB,CAAC,CAAC;UACvC,MAAMqC,cAAc,GAClBxH,OAAO,CAAC5B,KAAK,CAACW,MAAM,CAACC,6BAA6B,CAAE,QAAOzB,IAAK,EAAC,CAAC;UACpE,MAAMkK,QAAQ,GAAG7J,WAAC,CAACuG,cAAc,CAC/BvG,WAAC,CAACiB,SAAS,CAAC2I,cAAc,CAAC,EAC3BjD,MACF,CAAC;UAED,MAAMgB,QAAQ,GAAG/F,qBAAqB,CAAC6F,KAAK,EAAEoC,QAAQ,EAAE9H,QAAQ,CAAC;UACjE,MAAM,CAACL,OAAO,CAAC,GAAGU,OAAO,CAACpB,WAAW,CAAC2G,QAAQ,CAAC;UAE/C,IAAI8B,SAAS,EAAE;YACb5D,cAAc,GAAGrC,wBAAwB,CAACiE,KAAK,EAAElF,OAAO,CAAC;YAEzD,MAAM8D,KAAK,GAAG3E,OAAO,CAAClB,KAAK,CAACW,MAAM,CAACC,6BAA6B,CAC7D,OAAMzB,IAAK,EACd,CAAC;YACD,MAAM2G,KAAK,GAAG5E,OAAO,CAAClB,KAAK,CAACW,MAAM,CAACC,6BAA6B,CAC7D,OAAMzB,IAAK,EACd,CAAC;YAEDyG,mBAAmB,CAAC1E,OAAO,EAAEG,GAAG,EAAEwE,KAAK,EAAEC,KAAK,CAAC;YAE/CL,MAAM,GAAG,CAAC2D,cAAc,EAAEvD,KAAK,EAAEC,KAAK,CAAC;UACzC,CAAC,MAAM;YACLnE,oBAAoB,CAClB3C,IAAI,CAACE,IAAI,CAACd,EAAE,EACZ8C,OAAO,EACPG,GAAG,EACH4F,KAAK,EACLlF,OAAO,EACPC,UACF,CAAC;YACDyD,MAAM,GAAG2D,cAAc;UACzB;QACF,CAAC,MAAM,IAAIvF,IAAI,KAAKT,KAAK,EAAE;UACzB,MAAMkG,MAAM,GAAG1H,OAAO,CAAC5B,KAAK,CAACW,MAAM,CAACC,6BAA6B,CAC9D,QAAOzB,IAAK,EACf,CAAC;UACD,MAAMoK,SAAS,GACb3H,OAAO,CACPrB,GAAG,CAAC,OAAO,CAAC;UAEdgJ,SAAS,CAAC/I,WAAW,CACnBhB,WAAC,CAACuG,cAAc,CACdvG,WAAC,CAACiB,SAAS,CAAC6I,MAAM,CAAC,EACnB,CAAC9J,WAAC,CAAC2C,cAAc,CAAC,CAAC,EAAEoH,SAAS,CAACrK,IAAI,CAAC,CAACgF,MAAM,CAACsF,CAAC,IAAIA,CAAC,CACpD,CACF,CAAC;UAED/D,MAAM,GAAG6D,MAAM;UAEf,IAAIL,SAAS,EAAE;YACb5D,cAAc,GAAGrC,wBAAwB,CAAC3B,GAAG,EAAEU,OAAO,CAAC;UACzD;QACF,CAAC,MAAM,IAAIkH,SAAS,EAAE;UACpBxD,MAAM,GAAG7D,OAAO,CAAC5B,KAAK,CAACW,MAAM,CAACC,6BAA6B,CACxD,QAAOzB,IAAK,EACf,CAAC;UAED,MAAMsK,aAAa,GAAG,IAAIC,4BAAa,CAAC;YACtC9C,aAAa;YACb+C,UAAU,EAAE/H,OAAyC;YACrDgI,SAAS,EAAE/B,YAAY;YACvBgC,QAAQ,EAAE7K,IAAI,CAACE,IAAI,CAAC8B,UAAU;YAC9B8I,IAAI,EAAEnD,KAAK,CAACmD,IAAI;YAChBC,aAAa,EAAElC;UACjB,CAAC,CAAC;UAEF4B,aAAa,CAACO,OAAO,CAAC,CAAC;UAEvB,MAAM;YACJ7D,MAAM;YACNlF,IAAI;YACJgJ,KAAK,EAAEC;UACT,CAAC,GAAGtI,OAAO,CAAC1C,IAA4B;UAExCmG,cAAc,GAAG,CACf7F,WAAC,CAAC2K,kBAAkB,CAClB1I,SAAS,EACT0E,MAAM,CAACjC,MAAM,CAAC8B,gBAAgB,CAAC,EAC/B/E,IAAI,EACJiJ,OACF,CAAC,CACF;UAED,IAAIrG,IAAI,KAAKN,MAAM,IAAIM,IAAI,KAAKL,MAAM,EAAE;YACtCyC,mBAAmB,CACjBrE,OAAO,EACPpC,WAAC,CAACiB,SAAS,CAACY,GAAG,CAAC,EAChB7B,WAAC,CAACiB,SAAS,CAACgF,MAAM,CAAC,EACnBlE,QACF,CAAC;UACH,CAAC,MAAM;YACL,MAAMrC,IAAI,GAAG0C,OAAO,CAAC1C,IAA4B;YAGjDF,IAAI,CAACE,IAAI,CAAC+B,IAAI,CAACA,IAAI,CAAC1C,OAAO,CACzBiB,WAAC,CAACgC,oBAAoB,CAACH,GAAG,EAAE7B,WAAC,CAACiB,SAAS,CAACgF,MAAM,CAAC,EAAE,EAAE,EAAEvG,IAAI,CAAC+C,MAAM,CAClE,CAAC;YAEDwF,uBAAuB,CAACxI,GAAG,CAACoC,GAAG,CAACjD,EAAE,CAACe,IAAI,CAAC;YAExCyC,OAAO,CAACwI,MAAM,CAAC,CAAC;UAClB;QACF;QAEA,IAAIC,QAAsB;QAE1B,IAAIrI,UAAU,EAAE;UACdqI,QAAQ,GAAG7K,WAAC,CAACiB,SAAS,CAACY,GAAmB,CAAC;QAC7C,CAAC,MAAM,IAAIA,GAAG,CAACvB,IAAI,KAAK,aAAa,EAAE;UACrCuK,QAAQ,GAAG7K,WAAC,CAAC8K,aAAa,CAACjJ,GAAG,CAACjD,EAAE,CAACe,IAAI,CAAC;QACzC,CAAC,MAAM,IAAIkC,GAAG,CAACvB,IAAI,KAAK,YAAY,EAAE;UACpCuK,QAAQ,GAAG7K,WAAC,CAAC8K,aAAa,CAACjJ,GAAG,CAAClC,IAAI,CAAC;QACtC,CAAC,MAAM;UACLkL,QAAQ,GAAG7K,WAAC,CAACiB,SAAS,CAACY,GAAmB,CAAC;QAC7C;QAEA+F,oBAAoB,CAACvC,IAAI,CAAC;UACxBhB,IAAI;UACJQ,UAAU,EAAEA,UAAU,CAACc,GAAG,CAACoF,CAAC,IAAIA,CAAC,CAACrL,IAAI,CAACgE,UAAU,CAAC;UAClDoB,cAAc,EAAED,UAAU,CAACc,GAAG,CAACoF,CAAC,IAAIjG,cAAc,CAAC/D,GAAG,CAACgK,CAAC,CAACrL,IAAI,CAAC,CAAC;UAC/DC,IAAI,EAAEkL,QAAQ;UACd9I,QAAQ;UACR8D,cAAc;UACdI;QACF,CAAC,CAAC;QAEF,IAAI5B,IAAI,KAAKT,KAAK,EAAE;UAClB,IAAI7B,QAAQ,EAAE;YACZiG,kBAAkB,GAAG,IAAI;UAC3B,CAAC,MAAM;YACLD,iBAAiB,GAAG,IAAI;UAC1B;QACF;QAEA,IAAI3F,OAAO,CAAC1C,IAAI,EAAE;UAChB0C,OAAO,CAAC1C,IAAI,CAACmF,UAAU,GAAG,IAAI;QAChC;QAEA,IACE,CAACgD,cAAc,IACf,CAAC9F,QAAQ,KACRsC,IAAI,KAAKT,KAAK,IAAIS,IAAI,KAAKR,QAAQ,CAAC,EACrC;UACAgE,cAAc,GAAGzF,OAEhB;QACH;MACF;IACF;EACF;EAEA,MAAM4I,kBAAkB,GAAGvF,uBAAuB,CAChDmC,oBAAoB,EACpBrF,OACF,CAAC;EACD,IAAI0I,oBAAoB,GAAG,CAAC;EAC5B,IAAIC,gBAAgC,GAAG,EAAE;EACzC,IAAI7D,eAAe,EAAE;IACnB,MAAM;MAAE7B,OAAO;MAAEL;IAAK,CAAC,GAAGP,sBAAsB,CAC9CyC,eAAe,CAAC1B,GAAG,CAAChB,EAAE,IAAIA,EAAE,CAACjB,UAAU,CAAC,EACxC2D,eAAe,CAAC1B,GAAG,CAACwF,GAAG,IAAIrG,cAAc,CAAC/D,GAAG,CAACoK,GAAG,CAAC,CAAC,EACnD5I,OACF,CAAC;IACD0I,oBAAoB,GAAGzF,OAAO,GAAG,CAAC,GAAG,CAAC;IACtC0F,gBAAgB,GAAG/F,IAAI;EACzB;EAEA,MAAMiG,aAA6B,GACjCtF,8BAA8B,CAAC8B,oBAAoB,CAAC;EAEtD,IAAIG,iBAAiB,EAAE;IACrBG,cAAc,GAAGK,WAAW,CAACnH,6BAA6B,CAAC,WAAW,CAAC;IACvEgK,aAAa,CAAC/F,IAAI,CAAC6C,cAAc,CAAC;IAElC,MAAMmD,aAAa,GAAGrL,WAAC,CAACuG,cAAc,CAACvG,WAAC,CAACiB,SAAS,CAACiH,cAAc,CAAC,EAAE,CAClElI,WAAC,CAAC2C,cAAc,CAAC,CAAC,CACnB,CAAC;IAEF,IAAIkF,cAAc,EAAE;MAClB,MAAM/F,KAAK,GAAG+F,cAAc,CAAC9G,GAAG,CAAC,OAAO,CAAC;MACzC,MAAMU,IAAoB,GAAG,CAAC4J,aAAa,CAAC;MAE5C,IAAIvJ,KAAK,CAACpC,IAAI,EAAE;QACd+B,IAAI,CAAC4D,IAAI,CAACvD,KAAK,CAACpC,IAAI,CAAC;MACvB;MAEAoC,KAAK,CAACd,WAAW,CAAChB,WAAC,CAAC2B,kBAAkB,CAACF,IAAI,CAAC,CAAC;IAC/C,CAAC,MAAM,IAAIqG,eAAe,EAAE;MAC1B,IAAItI,IAAI,CAACE,IAAI,CAAC8B,UAAU,EAAE;QACxBhC,IAAI,CAACF,QAAQ,CAAC;UACZgM,cAAc,EAAE;YACdC,IAAIA,CAAC/L,IAAI,EAAE;cACT,IAAI,CAACA,IAAI,CAACuB,GAAG,CAAC,QAAQ,CAAC,CAACiI,OAAO,CAAC,CAAC,EAAE;cAEnCxJ,IAAI,CAACwB,WAAW,CACdhB,WAAC,CAACuG,cAAc,CAACvG,WAAC,CAACiB,SAAS,CAACiH,cAAc,CAAC,EAAE,CAAC1I,IAAI,CAACE,IAAI,CAAC,CAC3D,CAAC;cAEDF,IAAI,CAACgM,IAAI,CAAC,CAAC;YACb;UACF;QACF,CAAC,CAAC;MACJ,CAAC,MAAM;QACL1D,eAAe,CAACpI,IAAI,CAAC+B,IAAI,CAACA,IAAI,CAAC1C,OAAO,CACpCiB,WAAC,CAACiD,mBAAmB,CAACoI,aAAa,CACrC,CAAC;MACH;IACF,CAAC,MAAM;MACL,MAAM5J,IAAmB,GAAG,CAACzB,WAAC,CAACiD,mBAAmB,CAACoI,aAAa,CAAC,CAAC;MAElE,IAAI7L,IAAI,CAACE,IAAI,CAAC8B,UAAU,EAAE;QACxBC,IAAI,CAAC1C,OAAO,CACViB,WAAC,CAACiD,mBAAmB,CACnBjD,WAAC,CAACuG,cAAc,CAACvG,WAAC,CAACyL,KAAK,CAAC,CAAC,EAAE,CAC1BzL,WAAC,CAAC0L,aAAa,CAAC1L,WAAC,CAACE,UAAU,CAAC,MAAM,CAAC,CAAC,CACtC,CACH,CACF,CAAC;MACH;MAEAV,IAAI,CAACE,IAAI,CAAC+B,IAAI,CAACA,IAAI,CAAC1C,OAAO,CACzBiB,WAAC,CAACsD,WAAW,CACX,aAAa,EACbtD,WAAC,CAACE,UAAU,CAAC,aAAa,CAAC,EAC3B,CAACF,WAAC,CAAC2L,WAAW,CAAC3L,WAAC,CAACE,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EACrCF,WAAC,CAAC6C,cAAc,CAACpB,IAAI,CACvB,CACF,CAAC;IACH;EACF;EAEA,IAAIuG,kBAAkB,EAAE;IACtBG,eAAe,GAAGI,WAAW,CAACnH,6BAA6B,CAAC,YAAY,CAAC;IACzEgK,aAAa,CAAC/F,IAAI,CAAC8C,eAAe,CAAC;EACrC;EAEA,IAAIF,uBAAuB,CAAC2D,IAAI,GAAG,CAAC,EAAE;IACpCpM,IAAI,CAACF,QAAQ,CAAC;MACZC,WAAWA,CAACC,IAAI,EAAE;QAChB,IAAI,CAACyI,uBAAuB,CAAClI,GAAG,CAACP,IAAI,CAACE,IAAI,CAACd,EAAE,CAACe,IAAI,CAAC,EAAE;QAErD,MAAM0B,UAAU,GAAG7B,IAAI,CAAC6B,UAAU;QAClC,MAAMwK,gBAAgB,GAAGxK,UAAU,CAACA,UAAU;QAE9C,IAEGwK,gBAAgB,CAACnM,IAAI,CAACY,IAAI,KAAK,sBAAsB,IACpDuL,gBAAgB,CAACnM,IAAI,CAACoM,IAAI,KAAKzK,UAAU,CAAC3B,IAAI,IAEhDmM,gBAAgB,CAACnM,IAAI,CAACY,IAAI,KAAK,kBAAkB,IAEjDuL,gBAAgB,CAACnM,IAAI,CAACY,IAAI,KAAK,aAAa,IAE5CuL,gBAAgB,CAACnM,IAAI,CAACY,IAAI,KAAK,cAAc,IAE5CuL,gBAAgB,CAACnM,IAAI,CAACY,IAAI,KAAK,gBAAgB,IAC9CuL,gBAAgB,CAACnM,IAAI,CAACoC,KAAK,KAAKT,UAAU,CAAC3B,IAAI,IAC/CmM,gBAAgB,CAACxK,UAAU,CAACf,IAAI,KAAK,eAAgB,IAEtDuL,gBAAgB,CAACnM,IAAI,CAACY,IAAI,KAAK,gBAAgB,IAC9CuL,gBAAgB,CAACnM,IAAI,CAACoM,IAAI,KAAKzK,UAAU,CAAC3B,IAAK,EACjD;UACA,MAAMF,IAAI,CAACuM,mBAAmB,CAC3B,sDAAqDvM,IAAI,CAACE,IAAI,CAACd,EAAE,CAACe,IAAK,mCAC1E,CAAC;QACH;MACF;IACF,CAAC,CAAC;EACJ;EAEA,MAAMqM,WAA2B,GAAG,EAAE;EACtC,IAAIC,iBAAiB,GAAG,KAAK;EAC7B,MAAMC,aAAa,GACjB9D,cAAc,IAAIpI,WAAC,CAACuG,cAAc,CAACvG,WAAC,CAACiB,SAAS,CAACmH,cAAc,CAAC,EAAE,EAAE,CAAC;EAErE,MAAM+D,aAAa,GAAG3M,IAAI,CAACE,IAAI;EAE/B,IAAI2H,eAAe,EAAE;IACnB2E,WAAW,CAAC3G,IAAI,CAACgD,YAAY,EAAED,cAAc,CAAC;IAC9C,MAAMgE,OAIH,GAAG,EAAE;IACR,IAAIC,YAA6B,GAAG,EAAE;IACtC7M,IAAI,CAACuB,GAAG,CAAC,WAAW,CAAC,CAACuL,OAAO,CAAClK,OAAO,IAAI;MAGvC,IAAIA,OAAO,CAACmK,aAAa,CAAC,CAAC,EAAE;QAC3BF,YAAY,CAAChH,IAAI,CAACjD,OAAO,CAAC1C,IAAI,CAAC;QAC/B0C,OAAO,CAACwI,MAAM,CAAC,CAAC;QAChB;MACF;MAEA,MAAM4B,UAAU,GACdpK,OAAO,CAACqK,eAAe,CAAC,CAAC,IAAIrK,OAAO,CAACsH,sBAAsB,CAAC,CAAC;MAE/D,IACE,CAAC8C,UAAU,IAAIpK,OAAO,CAACsK,oBAAoB,CAAC,CAAC,KAC7CtK,OAAO,CAAC1C,IAAI,CAAC+C,MAAM,EACnB;QACA,IAAI+J,UAAU,IAAIH,YAAY,CAACvN,MAAM,GAAG,CAAC,EAAE;UACzC,MAAM6N,SAAyB,GAAGN,YAAY,CAAC1G,GAAG,CAACmB,iBAAiB,CAAC;UACrE,IAAI1E,OAAO,CAAC1C,IAAI,CAACoC,KAAK,EAAE6K,SAAS,CAACtH,IAAI,CAACjD,OAAO,CAAC1C,IAAI,CAACoC,KAAK,CAAC;UAC1DM,OAAO,CAAC1C,IAAI,CAACoC,KAAK,GAAGkF,uBAAuB,CAAC2F,SAAS,CAAC;UACvDN,YAAY,GAAG,EAAE;QACnB;QAEAjK,OAAO,CAAC1C,IAAI,CAAC+C,MAAM,GAAG,KAAK;QAC3B2J,OAAO,CAAC/G,IAAI,CAACjD,OAAO,CAAC1C,IAAI,CAAC;QAC1B0C,OAAO,CAACwI,MAAM,CAAC,CAAC;MAClB;IACF,CAAC,CAAC;IAEF,IAAIwB,OAAO,CAACtN,MAAM,GAAG,CAAC,IAAIuN,YAAY,CAACvN,MAAM,GAAG,CAAC,EAAE;MACjD,MAAM8N,YAAY,GAAGnJ,cAAQ,CAACC,UAAU,CAACC,GAAI;AACnD,wBAAwBwD,KAAK,CAAC0F,SAAS,CAAC,UAAU,CAAE;AACpD,OAA4B;MACtBD,YAAY,CAACnL,IAAI,CAACA,IAAI,GAAG,CACvBzB,WAAC,CAAC8M,WAAW,CAAC,CACZ9M,WAAC,CAAC+M,WAAW,CAACZ,aAAa,EAAE,IAAI,CAAC,IAGhCnM,WAAC,CAACiD,mBAAmB,CAACkJ,aAAkC,CAAC,CAC5D,CAAC,EACF,GAAGC,OAAO,CACX;MAED,MAAMY,eAA+B,GAAG,EAAE;MAE1C,MAAMC,OAAO,GAAGjN,WAAC,CAACkN,aAAa,CAACN,YAAY,EAAE,EAAE,CAAC;MAEjD,IAAIP,YAAY,CAACvN,MAAM,GAAG,CAAC,EAAE;QAC3BkO,eAAe,CAAC3H,IAAI,CAAC,GAAGgH,YAAY,CAAC1G,GAAG,CAACmB,iBAAiB,CAAC,CAAC;MAC9D;MACA,IAAIoF,aAAa,EAAE;QACjBD,iBAAiB,GAAG,IAAI;QACxBe,eAAe,CAAC3H,IAAI,CAAC6G,aAAa,CAAC;MACrC;MACA,IAAIc,eAAe,CAAClO,MAAM,GAAG,CAAC,EAAE;QAC9BkO,eAAe,CAACjO,OAAO,CACrBiB,WAAC,CAACuG,cAAc,CAACvG,WAAC,CAACyL,KAAK,CAAC,CAAC,EAAE,CAACzL,WAAC,CAACiB,SAAS,CAACoH,YAAY,CAAC,CAAC,CACzD,CAAC;QAEDuE,YAAY,CAACnL,IAAI,CAACA,IAAI,CAAC4D,IAAI,CACzBrF,WAAC,CAACsD,WAAW,CACX,aAAa,EACbtD,WAAC,CAACE,UAAU,CAAC,aAAa,CAAC,EAC3B,EAAE,EACFF,WAAC,CAAC6C,cAAc,CAAC,CACf7C,WAAC,CAACiD,mBAAmB,CAACjD,WAAC,CAAC2B,kBAAkB,CAACqL,eAAe,CAAC,CAAC,CAC7D,CACH,CACF,CAAC;MACH,CAAC,MAAM;QACLC,OAAO,CAACE,SAAS,CAAC9H,IAAI,CAACrF,WAAC,CAACiB,SAAS,CAACoH,YAAY,CAAC,CAAC;MACnD;MAEA7I,IAAI,CAACwB,WAAW,CAACiM,OAAO,CAAC;IAC3B;EACF;EACA,IAAI,CAAChB,iBAAiB,IAAIC,aAAa,EAAE;IACvC1M,IAAI,CAACE,IAAI,CAAC+B,IAAI,CAACA,IAAI,CAAC4D,IAAI,CACtBrF,WAAC,CAAC8M,WAAW,CAAC,CAAC9M,WAAC,CAACiD,mBAAmB,CAACiJ,aAAa,CAAC,CAAC,CACtD,CAAC;EACH;EAEA,IAAI;IAAE1K;EAAW,CAAC,GAAG2K,aAAa;EAClC,IAAI3K,UAAU,IAAqCe,OAAO,KAAK,SAAS,EAAG;IACzE,MAAM3D,EAAE,GAAGY,IAAI,CAACgB,KAAK,CAAC4M,qBAAqB,CAAC5L,UAAU,CAAC;IACvD,IAAI5C,EAAE,EAAE;MACNuN,aAAa,CAAC3K,UAAU,GAAGxB,WAAC,CAACkD,oBAAoB,CAAC,GAAG,EAAEtE,EAAE,EAAE4C,UAAU,CAAC;MACtEA,UAAU,GAAG5C,EAAE;IACjB;EACF;EAEAuN,aAAa,CAAC1K,IAAI,CAACA,IAAI,CAAC1C,OAAO,CAC7BiB,WAAC,CAAC8M,WAAW,CACX,CACE9M,WAAC,CAACiD,mBAAmB,CACnBoK,sBAAsB,CACpBjC,aAAa,EACbY,WAAW,EACXhB,kBAAkB,EAClBhL,WAAC,CAAC0F,eAAe,CAACwF,gBAAgB,CAAC,EACnClL,WAAC,CAACuF,cAAc,CAAC0F,oBAAoB,CAAC,EACtC3B,8BAA8B,GAAGD,uBAAuB,GAAG,IAAI,EAC/DrJ,WAAC,CAACiB,SAAS,CAACO,UAAU,CAAC,EACvB2F,KAAK,EACL5E,OACF,CACF,CAAC,EACDyF,kBAAkB,IAChBhI,WAAC,CAACiD,mBAAmB,CACnBjD,WAAC,CAACuG,cAAc,CAACvG,WAAC,CAACiB,SAAS,CAACkH,eAAe,CAAC,EAAE,CAC7CnI,WAAC,CAAC2C,cAAc,CAAC,CAAC,CACnB,CACH,CAAC,CACJ,CAAC+B,MAAM,CAACQ,OAAO,CAClB,CACF,CAAC;EAID1F,IAAI,CAACoB,YAAY,CAAC0H,WAAW,CAAC3C,GAAG,CAAC2H,IAAI,IAAItN,WAAC,CAACiD,mBAAmB,CAACqK,IAAI,CAAC,CAAC,CAAC;EAGvE9N,IAAI,CAACgB,KAAK,CAAC+M,KAAK,CAAC,CAAC;EAElB,OAAO/N,IAAI;AACb;AAEA,SAAS6N,sBAAsBA,CAC7BjC,aAA6B,EAC7BY,WAA2B,EAC3BhB,kBAAqC,EACrCE,gBAAmC,EACnCD,oBAAsC,EACtCuC,oBAA0C,EAC1ChM,UAA+B,EAC/B2F,KAAiB,EACjB5E,OAA6B,EAC7B;EACA,IAAIkL,GAAG,EAAEC,GAAG;EACZ,MAAMC,IAAoB,GAAG,CAC3B3N,WAAC,CAAC2C,cAAc,CAAC,CAAC,EAClBqI,kBAAkB,EAClBE,gBAAgB,CACjB;EAEkC;IACjC,IACE3I,OAAO,KAAK,SAAS,IACpBA,OAAO,KAAK,SAAS,IAAI,CAAC4E,KAAK,CAACyG,eAAe,CAAC,gBAAgB,CAAE,EACnE;MACA,MAAMH,GAAG,GAAGzN,WAAC,CAAC6N,YAAY,CAAC,CAAC,GAAGzC,aAAa,EAAE,GAAGY,WAAW,CAAC,CAAC;MAC9D,MAAM0B,GAAG,GAAG1N,WAAC,CAACuG,cAAc,CAC1BY,KAAK,CAAC0F,SAAS,CAACtK,OAAO,KAAK,SAAS,GAAG,WAAW,GAAG,eAAe,CAAC,EACtEoL,IACF,CAAC;MACD,OAAO3N,WAAC,CAACkD,oBAAoB,CAAC,GAAG,EAAEuK,GAAG,EAAEC,GAAG,CAAC;IAC9C;EACF;EAEA,IAAoCnL,OAAO,KAAK,SAAS,EAAE;IACzD,IACEiL,oBAAoB,IACpBhM,UAAU,IACVyJ,oBAAoB,CAACnJ,KAAK,KAAK,CAAC,EAChC;MACA6L,IAAI,CAACtI,IAAI,CAAC4F,oBAAoB,CAAC;IACjC;IACA,IAAIuC,oBAAoB,EAAE;MACxBG,IAAI,CAACtI,IAAI,CACP5B,cAAQ,CAACC,UAAU,CAACC,GAAI;AAChC,mBAAmB3D,WAAC,CAACiB,SAAS,CAACuM,oBAAoB,CAAE;AACrD,WACM,CAAC;IACH,CAAC,MAAM,IAAIhM,UAAU,EAAE;MACrBmM,IAAI,CAACtI,IAAI,CAACrF,WAAC,CAACsF,eAAe,CAAC,MAAM,EAAEtF,WAAC,CAACuF,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3D;IACA,IAAI/D,UAAU,EAAEmM,IAAI,CAACtI,IAAI,CAAC7D,UAAU,CAAC;IACrCkM,GAAG,GAAG1N,WAAC,CAACuG,cAAc,CAACY,KAAK,CAAC0F,SAAS,CAAC,eAAe,CAAC,EAAEc,IAAI,CAAC;EAChE,CAAC,MAAM,IAAIpL,OAAO,KAAK,SAAS,EAAE;IAChC,IAAIiL,oBAAoB,EAAE;MACxBG,IAAI,CAACtI,IAAI,CACP5B,cAAQ,CAACC,UAAU,CAACC,GAAI;AAChC,mBAAmB3D,WAAC,CAACiB,SAAS,CAACuM,oBAAoB,CAAE;AACrD,WACM,CAAC;IACH;IACAE,GAAG,GAAG1N,WAAC,CAACuG,cAAc,CAACY,KAAK,CAAC0F,SAAS,CAAC,eAAe,CAAC,EAAEc,IAAI,CAAC;EAChE,CAAC,MAAM;IACLD,GAAG,GAAG1N,WAAC,CAACuG,cAAc,CAACY,KAAK,CAAC0F,SAAS,CAAC,gBAAgB,CAAC,EAAEc,IAAI,CAAC;EACjE;EAGA,IAAIvC,aAAa,CAACtM,MAAM,GAAG,CAAC,EAAE;IAC5B,IAAIkN,WAAW,CAAClN,MAAM,GAAG,CAAC,EAAE;MAC1B2O,GAAG,GAAGzN,WAAC,CAAC8N,aAAa,CAAC,CACpB9N,WAAC,CAAC+N,cAAc,CAAC/N,WAAC,CAACE,UAAU,CAAC,GAAG,CAAC,EAAEF,WAAC,CAAC6N,YAAY,CAACzC,aAAa,CAAC,CAAC,EAClEpL,WAAC,CAAC+N,cAAc,CAAC/N,WAAC,CAACE,UAAU,CAAC,GAAG,CAAC,EAAEF,WAAC,CAAC6N,YAAY,CAAC7B,WAAW,CAAC,CAAC,CACjE,CAAC;IACJ,CAAC,MAAM;MACLyB,GAAG,GAAGzN,WAAC,CAAC6N,YAAY,CAACzC,aAAa,CAAC;MACnCsC,GAAG,GAAG1N,WAAC,CAAC+C,gBAAgB,CAAC2K,GAAG,EAAE1N,WAAC,CAACE,UAAU,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC;IAChE;EACF,CAAC,MAAM;IAELuN,GAAG,GAAGzN,WAAC,CAAC6N,YAAY,CAAC7B,WAAW,CAAC;IACjC0B,GAAG,GAAG1N,WAAC,CAAC+C,gBAAgB,CAAC2K,GAAG,EAAE1N,WAAC,CAACE,UAAU,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC;EAChE;EAEA,OAAOF,WAAC,CAACkD,oBAAoB,CAAC,GAAG,EAAEuK,GAAG,EAAEC,GAAG,CAAC;AAC9C;AAEe,SAAAM,SACb;EAAEC,aAAa;EAAEC;AAAsB,CAAC,EACxC;EAAEC;AAAe,CAAC,EAElB5L,OAAsD,EACxC;EAAA,IAAA6L,WAAA;EAGP;IACL,IAAI7L,OAAO,KAAK,SAAS,IAAIA,OAAO,KAAK,SAAS,EAAE;MAClD0L,aAAa,CAAC,SAAS,CAAC;IAC1B,CAAC,MAAM,IAAI1L,OAAO,KAAK,SAAS,EAAE;MAChC0L,aAAa,CAAC,SAAS,CAAC;IAC1B,CAAC,MAAM;MACLA,aAAa,CAAC,SAAS,CAAC;IAC1B;EACF;EAEA,MAAMI,OAAO,GAAG,IAAIC,OAAO,CAAW,CAAC;EACvC,MAAMlH,aAAa,IAAAgH,WAAA,GAAGF,UAAU,CAAC,eAAe,CAAC,YAAAE,WAAA,GAAID,KAAK;EAE1D,OAAO;IACLxO,IAAI,EAAE,qBAAqB;IAC3B4O,QAAQ,EAAEC,+BAAgB;IAE1BC,OAAO,EAAE;MACP,iDAAiDC,CAC/ClP,IAAqE,EACrE;QAAA,IAAAmP,qBAAA;QACA,MAAM;UAAEC;QAAY,CAAC,GAAGpP,IAAI,CAACE,IAAI;QACjC,IACE,CAAAkP,WAAW,oBAAXA,WAAW,CAAEtO,IAAI,MAAK,kBAAkB,IAGxC,EAAAqO,qBAAA,GAAAC,WAAW,CAAC/J,UAAU,qBAAtB8J,qBAAA,CAAwB7P,MAAM,IAAG,CAAC,EAClC;UACA,IAAA+P,qCAAsB,EAACrP,IAAI,CAAC;QAC9B;MACF,CAAC;MAEDsP,KAAKA,CAACtP,IAAI,EAAE2H,KAAK,EAAE;QACjB,IAAIkH,OAAO,CAACtO,GAAG,CAACP,IAAI,CAAC,EAAE;QAEvB,MAAMkC,OAAO,GAAGwF,cAAc,CAAC1H,IAAI,EAAE2H,KAAK,EAAEC,aAAa,EAAE7E,OAAO,CAAC;QACnE,IAAIb,OAAO,EAAE2M,OAAO,CAAC5O,GAAG,CAACiC,OAAO,CAAC;MACnC;IACF;EACF,CAAC;AACH"}