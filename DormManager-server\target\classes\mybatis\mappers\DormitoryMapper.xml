<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mapper.DormitoryMapper">
	<select id="findDormitoryList"  resultType="Dormitory">
		select * from dormitory 
	</select>
	
	<select id="query" parameterType="java.util.Map" resultType="Dormitory">
	    select a.*,b.dbname,ifnull(c.dorcount,0) as by1  
        from dormitory a  left join dormbuilding b on a.dbid=b.dbid  	
		left join (select doro,count(0) as dorcount from student group by doro) c on a.doro=c.doro
		<where>
      		<if test="doro != null and doro != ''">
		    and a.doro = #{doro}
		</if>
		<if test="dbid != null and dbid !=0 ">
		    and a.dbid = #{dbid}
		</if>
		<if test="dorcapacity != null and dorcapacity !=0 ">
		    and a.dorcapacity = #{dorcapacity}
		</if>
		<if test="dorgender != null and dorgender != ''">
		    and a.dorgender = #{dorgender}
		</if>
		<if test="condition != null and condition != ''">
		    ${condition} 
		</if>

    </where>

    order by ${sort} doro 

    <if test="page">
			limit #{offset} ,#{pageSize}
		</if>
	</select>	
	
	<select id="getCount" parameterType="java.util.Map" resultType="Int">
		select count(0) from dormitory a  left join dormbuilding b on a.dbid=b.dbid  
		<where>
      		<if test="doro != null and doro != ''">
		    and a.doro = #{doro}
		</if>
		<if test="dbid != null and dbid !=0 ">
		    and a.dbid = #{dbid}
		</if>
		<if test="dorcapacity != null and dorcapacity !=0 ">
		    and a.dorcapacity = #{dorcapacity}
		</if>
		<if test="dorgender != null and dorgender != ''">
		    and a.dorgender = #{dorgender}
		</if>
		<if test="condition != null and condition != ''">
		    ${condition} 
		</if>

    </where>
	</select>	
	
	<select id="queryDormitoryById" parameterType="String" resultType="Dormitory">
    select a.*,b.dbname,ifnull(c.dorcount,0) as by1  
        from dormitory a  left join dormbuilding b on a.dbid=b.dbid  	
		left join (select doro,count(0) as dorcount from student group by doro) c on a.doro=c.doro
		 	 where a.doro=#{value}
  </select>
 
	<insert id="insertDormitory" useGeneratedKeys="true" keyProperty="doro" parameterType="Dormitory">
    insert into dormitory
    (doro,dbid,dorcapacity,dorgender)
    values
    (#{doro},#{dbid},#{dorcapacity},#{dorgender});
  </insert>
	
	<update id="updateDormitory" parameterType="Dormitory" >
    update dormitory 
    <set>
		<if test="doro != null and doro != ''">
		    doro = #{doro},
		</if>
		<if test="dbid != null ">
		    dbid = #{dbid},
		</if>
		<if test="dorcapacity != null ">
		    dorcapacity = #{dorcapacity},
		</if>
		<if test="dorgender != null and dorgender != ''">
		    dorgender = #{dorgender},
		</if>

    </set>
   <where> 
    <if test="condition != null and condition != ''">
      ${condition}
    </if>
    <if test="doro != null or doro != ''">
      doro=#{doro}
    </if>
   </where>     
  </update>	
 
	
	<delete id="deleteDormitory" parameterType="String">
    delete from  dormitory where doro=#{value}
  </delete>

	
	
</mapper>

 
