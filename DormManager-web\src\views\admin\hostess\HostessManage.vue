<template>
    <div style="width: 100%;line-height: 30px;text-align: left;">
      <el-col  :span="24"  style="padding-bottom: 0px; margin-left: 10px">
<el-form :inline="true" :model="filters" >
<el-form-item>
<el-input v-model="filters.hno" placeholder="账号"  size="small"></el-input>
</el-form-item>
<el-form-item>
<el-input v-model="filters.honame" placeholder="姓名"  size="small"></el-input>
</el-form-item>
<el-form-item label="负责宿舍楼" prop="dbid">
<el-select v-model="filters.dbid" placeholder="请选择"  size="small">
<el-option label="全部" value=""></el-option>
<el-option v-for="item in dormbuildingList" :key="item.dbid" :label="item.dbname" :value="item.dbid"></el-option>
</el-select>
</el-form-item>
<el-form-item>
<el-button type="primary" size="small" @click="query" icon="el-icon-search">搜索</el-button>
</el-form-item>
 </el-form>
</el-col>

<el-table :data="datalist" border stripe style="width: 100%"  v-loading="listLoading"   highlight-current-row   max-height="600"     size="small">
<el-table-column prop="hno" label="账号"  align="center"></el-table-column>
<el-table-column prop="password" label="密码"  align="center"></el-table-column>
<el-table-column prop="honame" label="姓名"  align="center"></el-table-column>
<el-table-column prop="age" label="年龄"  align="center"></el-table-column>
<el-table-column prop="phone" label="手机号码"  align="center"></el-table-column>
<el-table-column prop="dbname" label="负责宿舍楼"  align="center"></el-table-column>
<el-table-column prop="addtime" label="添加时间"  align="center"></el-table-column>
<el-table-column label="操作" min-width="200" align="center">
<template #default="scope">
<el-button type="primary" size="mini" @click="handleShow(scope.$index, scope.row)" icon="el-icon-zoom-in" style=" padding: 3px 6px 3px 6px;">详情</el-button>
<el-button type="success" size="mini" @click="handleEdit(scope.$index, scope.row)" icon="el-icon-edit" style=" padding: 3px 6px 3px 6px;">编辑</el-button>
<el-button type="danger" size="mini" @click="handleDelete(scope.$index, scope.row)" icon="el-icon-delete" style=" padding: 3px 6px 3px 6px;">删除</el-button>
</template>
</el-table-column>
</el-table>
<el-pagination  @current-change="handleCurrentChange" :current-page="page.currentPage" :page-size="page.pageSize" 
 background layout="total, prev, pager, next, jumper" :total="page.totalCount" 
 style="float: right; margin: 10px 20px 0 0"></el-pagination>

    </div>
</template>

<script>
import request, { base } from "../../../../utils/http";
export default {
  name: 'hostess',
  components: {
    
  },  
    data() {
      return {
               filters: {
          //列表查询参数
          hno: '',
          honame: '',
          dbid: '',
        },

        page: {
          currentPage: 1, // 当前页
          pageSize: 10, // 每页显示条目个数
          totalCount: 0, // 总条目数
        },
        isClear: false,      
        dormbuildingList: [], //负责宿舍楼

        listLoading: false, //列表加载状态
        btnLoading: false, //保存按钮加载状态
        datalist: [], //表格数据  
    
      };
    },
    created() {
      this.getDatas();
      this.getdormbuildingList();
    },

 
    methods: {    

              
       // 删除宿管阿姨
        handleDelete(index, row) {
          this.$confirm("确认删除该记录吗?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          })
            .then(() => {
              this.listLoading = true;
              let url = base + "/hostess/del?id=" + row.hno;
              request.post(url).then((res) => {
                this.listLoading = false;
             
                this.$message({
                  message: "删除成功",
                  type: "success",
                  offset: 320,
                });
                this.getDatas();
              });
            })
            .catch(() => { });
        },
                
        // 分页
        handleCurrentChange(val) {
          this.page.currentPage = val;
          this.getDatas();
        },     
     
        //获取列表数据
        getDatas() {      
          let para = {
               hno:this.filters.hno,
   honame:this.filters.honame,
   dbid:this.filters.dbid,

          };
          this.listLoading = true;
          let url = base + "/hostess/list?currentPage=" + this.page.currentPage+ "&pageSize=" + this.page.pageSize;        
          request.post(url, para).then((res) => {   
            if (res.resdata.length > 0) {
              this.isPage = true;
            } else {
              this.isPage = false;
            }
            this.page.totalCount = res.count;
            this.datalist = res.resdata;
            this.listLoading = false;
          });
        },    
                 //查询
        query() {
          this.getDatas();
        },  
            
    getdormbuildingList() {
      let para = {};
      this.listLoading = true;
      let url = base + "/dormbuilding/list?currentPage=1&pageSize=1000";
      request.post(url, para).then((res) => {
        this.dormbuildingList = res.resdata;
      });
    },
   
        // 查看
        handleShow(index, row) {
          this.$router.push({
            path: "/HostessDetail",
             query: {
                id: row.hno,
              },
          });
        },
    
        // 编辑
        handleEdit(index, row) {
          this.$router.push({
            path: "/HostessEdit",
             query: {
                id: row.hno,
              },
          });
        },
      },
}

</script>
<style scoped>
</style>
 

