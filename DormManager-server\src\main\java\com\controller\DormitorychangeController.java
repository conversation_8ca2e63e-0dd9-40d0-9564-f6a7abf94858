package com.controller;

import com.model.*;
import com.response.Response;
import com.service.*;
import com.util.PageBean;
import com.util.removeHTML;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.text.SimpleDateFormat;
import java.util.Date;

@RestController
@RequestMapping("/api/dormitorychange")
public class DormitorychangeController{

	@Resource
	private DormitorychangeService dormitorychangeService;

	@Resource
	private StudentService studentService;
	
	//宿舍更换列表
	@RequestMapping(value="/list")
	@CrossOrigin
	public Response<List<Dormitorychange>> list(@RequestBody Dormitorychange dormitorychange, @RequestParam Integer currentPage, HttpServletRequest req) throws Exception {
		int pageSize=Integer.parseInt(req.getParameter("pageSize")); //每页显示记录数
		int offset = (currentPage - 1) * pageSize; //当前页开始记录
		int counts = 0;  //总记录数
		PageBean page = new PageBean(offset, pageSize); //分页对象
		//查询记录总数
		counts = dormitorychangeService.getCount(dormitorychange);
		//获取当前页记录
		List<Dormitorychange> dormitorychangeList = dormitorychangeService.queryDormitorychangeList(dormitorychange, page);
        
		int page_count = counts % PageBean.PAGE_IETM == 0 ? counts / PageBean.PAGE_IETM : counts / PageBean.PAGE_IETM + 1; //总页数
		return Response.success(dormitorychangeList, counts, page_count);
	}
        
	//添加宿舍更换
	@ResponseBody
	@PostMapping(value = "/add")
	@CrossOrigin
	public Response add(@RequestBody Dormitorychange dormitorychange, HttpServletRequest req) throws Exception {
		try {
			dormitorychangeService.insertDormitorychange(dormitorychange); //添加
   
			} catch (Exception e) {
			return Response.error();
		}
		return Response.success();
	}
    
	//删除宿舍更换
	@ResponseBody
	@PostMapping(value = "/del")
	@CrossOrigin
	public Response del(HttpServletRequest req) throws Exception {
		try {
			int id = Integer.parseInt(req.getParameter("id"));
			dormitorychangeService.deleteDormitorychange(id); //删除
			} catch (Exception e) {
			return Response.error();
		}
		return Response.success();
	}
	
	//修改宿舍更换
	@ResponseBody
	@PostMapping(value = "/update")
	@CrossOrigin
	public Response update(@RequestBody Dormitorychange dormitorychange, HttpServletRequest req) throws Exception {
		try {
			dormitorychangeService.updateDormitorychange(dormitorychange); //修改
			} catch (Exception e) {
			return Response.error();
		}
		return Response.success();
	}
	
	//返回宿舍更换详情
	@ResponseBody
	@PostMapping(value = "/get")
	@CrossOrigin
	public Response get(HttpServletRequest req) throws Exception {
		try {
			int id = Integer.parseInt(req.getParameter("id"));
			Dormitorychange dormitorychange=dormitorychangeService.queryDormitorychangeById(id); //根据ID查询
			return Response.success(dormitorychange);
			} catch (Exception e) {
			return Response.error();
		}

	}

	//学生申请宿舍更换
	@ResponseBody
	@PostMapping(value = "/apply")
	@CrossOrigin
	@SuppressWarnings("rawtypes")
	public Response apply(@RequestBody Dormitorychange dormitorychange, HttpServletRequest req) throws Exception {
		try {
			// 设置默认状态为待审核
			dormitorychange.setReviewstatus("待审核");
			dormitorychangeService.insertDormitorychange(dormitorychange); //添加申请
			return Response.success();
		} catch (Exception e) {
			e.printStackTrace();
			return Response.error(500, "申请提交失败");
		}
	}

	//审核宿舍更换申请
	@ResponseBody
	@PostMapping(value = "/review")
	@CrossOrigin
	@SuppressWarnings("rawtypes")
	public Response review(@RequestBody Dormitorychange dormitorychange, HttpServletRequest req) throws Exception {
		try {
			// 更新审核状态和回复
			dormitorychangeService.updateDormitorychange(dormitorychange);

			// 如果审核通过，需要更新学生的宿舍信息
			if ("审核通过".equals(dormitorychange.getReviewstatus())) {
				// 获取完整的宿舍更换信息
				Dormitorychange fullChange = dormitorychangeService.queryDormitorychangeById(dormitorychange.getId());

				// 更新学生的宿舍楼ID和宿舍编号
				Student student = studentService.queryStudentById(fullChange.getSno());
				if (student != null) {
					student.setDbid(fullChange.getDbid2()); // 新宿舍楼ID
					student.setDoro(fullChange.getDoro2()); // 新宿舍编号
					studentService.updateStudent(student);
				}
			}

			return Response.success();
		} catch (Exception e) {
			e.printStackTrace();
			return Response.error(500, "审核失败");
		}
	}

}

