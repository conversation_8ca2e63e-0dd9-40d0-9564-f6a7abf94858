{"remainingRequest": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\admin\\waterelectricityfee\\WaterelectricityfeeAdd.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\admin\\waterelectricityfee\\WaterelectricityfeeAdd.vue", "mtime": 1749046408899}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749043011899}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749043012919}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749043011899}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749043013130}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQppbXBvcnQgcmVxdWVzdCwgeyBiYXNlIH0gZnJvbSAiLi4vLi4vLi4vLi4vdXRpbHMvaHR0cCI7DQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogJ1dhdGVyZWxlY3RyaWNpdHlmZWVBZGQnLA0KICBjb21wb25lbnRzOiB7DQogICAgDQogIH0sICANCiAgICBkYXRhKCkgew0KICAgICAgcmV0dXJuIHsgICANCiAgICAgICAgdXBsb2FkVmlzaWJsZTogZmFsc2UsIA0KICAgICAgICBidG5Mb2FkaW5nOiBmYWxzZSwgLy/kv53lrZjmjInpkq7liqDovb3nirbmgIEgICAgIA0KICAgICAgICBmb3JtRGF0YTogew0KICAgICAgICAgIHN0YXR1czogJ+acque8tOe6sycNCiAgICAgICAgfSwgLy/ooajljZXmlbDmja4gICAgICAgICAgIA0KICAgICAgICBhZGRydWxlczogew0KICAgICAgICAgIGRiaWQ6IFt7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36YCJ5oup5a6/6IiN5qW8JywgdHJpZ2dlcjogJ29uY2hhbmdlJyB9XSwNCiAgICAgICAgICBkb3JvOiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+mAieaLqeWuv+iIjee8luWPtycsIHRyaWdnZXI6ICdvbmNoYW5nZScgfV0sDQogICAgICAgICAgZnR5cGU6IFt7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36YCJ5oup6LS555So57G75Z6LJywgdHJpZ2dlcjogJ29uY2hhbmdlJyB9XSwNCiAgICAgICAgICB0aXRsZTogW3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fovpPlhaXotLnnlKjmoIfpopgnLCB0cmlnZ2VyOiAnYmx1cicgfSwNCl0sICAgICAgICAgIGFtb3VudDogW3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fovpPlhaXotLnnlKjph5Hpop0nLCB0cmlnZ2VyOiAnYmx1cicgfSwNCl0sICAgICAgICAgc3RhdHVzOiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+i+k+WFpee8tOe6s+eKtuaAgScsIHRyaWdnZXI6ICdibHVyJyB9LA0KXSwgICAgICAgIH0sDQoNCiAgICAgIH07DQogICAgfSwNCiAgICBtb3VudGVkKCkgew0KICAgIA0KICAgICAgdGhpcy5nZXRkb3JtYnVpbGRpbmdMaXN0KCk7DQogICAgICB0aGlzLmdldGRvcm1pdG9yeUxpc3QoKTsNCiAgICB9LA0KDQogDQogICAgbWV0aG9kczogeyAgICANCiAgIC8vIOa3u+WKoA0KICAgIHNhdmUoKSB7ICAgICAgIA0KICAgICAgICAgdGhpcy4kcmVmc1siZm9ybURhdGFSZWYiXS52YWxpZGF0ZSgodmFsaWQpID0+IHsgLy/pqozor4HooajljZUNCiAgICAgICAgICAgaWYgKHZhbGlkKSB7DQogICAgICAgICAgICAgbGV0IHVybCA9IGJhc2UgKyAiL3dhdGVyZWxlY3RyaWNpdHlmZWUvYWRkIjsNCiAgICAgICAgICAgICB0aGlzLmJ0bkxvYWRpbmcgPSB0cnVlOw0KICAgICAgICAgICAgIHJlcXVlc3QucG9zdCh1cmwsIHRoaXMuZm9ybURhdGEpLnRoZW4oKHJlcykgPT4geyAvL+WPkemAgeivt+axgiAgICAgICAgIA0KICAgICAgICAgICAgICAgaWYgKHJlcy5jb2RlID09IDIwMCkgew0KICAgICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgICAgICAgICBtZXNzYWdlOiAi5pON5L2c5oiQ5YqfIiwNCiAgICAgICAgICAgICAgICAgICB0eXBlOiAic3VjY2VzcyIsDQogICAgICAgICAgICAgICAgICAgb2Zmc2V0OiAzMjAsDQogICAgICAgICAgICAgICAgIH0pOyAgICAgICAgICAgICAgDQogICAgICAgICAgICAgICAgdGhpcy4kcm91dGVyLnB1c2goew0KICAgICAgICAgICAgICAgIHBhdGg6ICIvV2F0ZXJlbGVjdHJpY2l0eWZlZU1hbmFnZSIsDQogICAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgICAgICAgICBtZXNzYWdlOiByZXMubXNnLA0KICAgICAgICAgICAgICAgICAgIHR5cGU6ICJlcnJvciIsDQogICAgICAgICAgICAgICAgICAgb2Zmc2V0OiAzMjAsDQogICAgICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgdGhpcy5idG5Mb2FkaW5nPWZhbHNlOw0KICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICB9ICAgICAgICANCiAgICAgICAgICAgDQogICAgICAgICB9KTsNCiAgICB9LA0KICAgIA0KICAgICAgIC8vIOi/lOWbng0KICAgICAgICBnb0JhY2soKSB7DQogICAgICAgICAgdGhpcy4kcm91dGVyLnB1c2goew0KICAgICAgICAgICAgcGF0aDogIi9XYXRlcmVsZWN0cmljaXR5ZmVlTWFuYWdlIiwNCiAgICAgICAgICB9KTsNCiAgICAgICAgfSwgICAgICAgDQogICAgICAgICAgICAgIA0KICAgICAgICAgICAgDQogICAgZ2V0ZG9ybWJ1aWxkaW5nTGlzdCgpIHsNCiAgICAgIGxldCBwYXJhID0ge307DQogICAgICB0aGlzLmxpc3RMb2FkaW5nID0gdHJ1ZTsNCiAgICAgIGxldCB1cmwgPSBiYXNlICsgIi9kb3JtYnVpbGRpbmcvbGlzdD9jdXJyZW50UGFnZT0xJnBhZ2VTaXplPTEwMDAiOw0KICAgICAgcmVxdWVzdC5wb3N0KHVybCwgcGFyYSkudGhlbigocmVzKSA9PiB7DQogICAgICAgIHRoaXMuZG9ybWJ1aWxkaW5nTGlzdCA9IHJlcy5yZXNkYXRhOw0KICAgICAgfSk7DQogICAgfSwNCiAgICANCiAgICAgIGdldGRvcm1pdG9yeUxpc3QoKSB7DQogdmFyIHVzZXIgPSBKU09OLnBhcnNlKHNlc3Npb25TdG9yYWdlLmdldEl0ZW0oJ3VzZXInKSk7IC8v6I635Y+W55So5oi35L+h5oGvDQogICAgICAgIGxldCBkYmlkID0gdXNlci5kYmlkOw0KICAgICAgICBsZXQgcGFyYSA9IHsNCiAgICAgICAgZGJpZDogZGJpZA0KICAgICAgfTsNCiAgICAgIHRoaXMubGlzdExvYWRpbmcgPSB0cnVlOw0KICAgICAgbGV0IHVybCA9IGJhc2UgKyAiL2Rvcm1pdG9yeS9saXN0P2N1cnJlbnRQYWdlPTEmcGFnZVNpemU9MTAwMCI7DQogICAgICByZXF1ZXN0LnBvc3QodXJsLCBwYXJhKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgdGhpcy5kb3JtaXRvcnlMaXN0ID0gcmVzLnJlc2RhdGE7DQogICAgICB9KTsNCiAgICB9LA0KICANCiAgICAgICAgICAgDQogICAgICAgICAgIA0KICAgICAgfSwNCn0NCg0K"}, {"version": 3, "sources": ["I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\admin\\waterelectricityfee\\WaterelectricityfeeAdd.vue"], "names": [], "mappings": ";AAyCA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAEtD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;EAEZ,CAAC;IACC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACR,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UAClE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACnE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACpE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AACzE,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AAC5E,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AAC3E,CAAC,SAAS,CAAC;;MAEL,CAAC;IACH,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;MAER,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC;;;IAGD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;GACV,CAAC,EAAE,CAAC;IACH,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;SACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;WACnD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;aACT,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;aACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;eACpD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;iBACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;mBACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;mBACf,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;mBACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;iBACb,CAAC,CAAC;gBACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAClB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAClC,CAAC,CAAC;eACH,EAAE,CAAC,CAAC,CAAC,EAAE;iBACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;mBACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;mBAChB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;mBACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;iBACb,CAAC,CAAC;eACJ;eACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aACvB,CAAC,CAAC;WACJ;;SAEF,CAAC,CAAC;IACP,CAAC;;OAEE,CAAC,EAAE,CAAC;QACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpC,CAAC,CAAC;QACJ,CAAC;;;IAGL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACpB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrC,CAAC,CAAC;IACJ,CAAC;;MAEC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;CACxB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE;QACX,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACX,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClC,CAAC,CAAC;IACJ,CAAC;;;;MAIC,CAAC;AACP", "file": "I:/product4/B7839DormManager/DormManager-web/src/views/admin/waterelectricityfee/WaterelectricityfeeAdd.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n    <div style=\"width: 100%;line-height: 30px;text-align: left;\">\r\n       <el-form :model=\"formData\" label-width=\"20%\" ref=\"formDataRef\" :rules=\"addrules\"  align=\"left\">\r\n\r\n<el-form-item label=\"宿舍编号\" prop=\"doro\">\r\n<el-select v-model=\"formData.doro\" placeholder=\"请选择\"  size=\"small\">\r\n<el-option v-for=\"item in dormitoryList\" :key=\"item.doro\" :label=\"item.doro\" :value=\"item.doro\"></el-option>\r\n</el-select>\r\n</el-form-item>\r\n<el-form-item label=\"费用类型\" prop=\"ftype\">\r\n<el-select v-model=\"formData.ftype\" placeholder=\"请选择\"  size=\"small\">\r\n<el-option label=\"水费\" value=\"水费\"></el-option>\r\n<el-option label=\"电费\" value=\"电费\"></el-option>\r\n</el-select>\r\n</el-form-item>\r\n<el-form-item label=\"费用标题\" prop=\"title\">\r\n<el-input v-model=\"formData.title\" placeholder=\"费用标题\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item label=\"费用金额\" prop=\"amount\">\r\n<el-input v-model=\"formData.amount\" placeholder=\"费用金额\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item label=\"备注说明\" prop=\"note\">\r\n<el-input type=\"textarea\" :rows=\"5\" v-model=\"formData.note\" placeholder=\"备注说明\"  size=\"small\"></el-input>\r\n</el-form-item>\r\n<el-form-item label=\"缴纳状态\" prop=\"status\">\r\n<el-radio-group v-model=\"formData.status\" placeholder=\"缴纳状态\"  style=\"width:50%;\" >\r\n<el-radio label=\"未缴纳\" value=\"未缴纳\"></el-radio>\r\n<el-radio label=\"已缴纳\" value=\"已缴纳\"></el-radio>\r\n</el-radio-group>\r\n</el-form-item>\r\n<el-form-item>\r\n<el-button type=\"primary\" size=\"small\" @click=\"save\" :loading=\"btnLoading\" icon=\"el-icon-upload\">提 交</el-button>\r\n<el-button type=\"info\" size=\"small\" @click=\"goBack\" icon=\"el-icon-back\">返 回</el-button>\r\n</el-form-item>\r\n</el-form>\r\n\r\n\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport request, { base } from \"../../../../utils/http\";\r\n\r\nexport default {\r\n  name: 'WaterelectricityfeeAdd',\r\n  components: {\r\n    \r\n  },  \r\n    data() {\r\n      return {   \r\n        uploadVisible: false, \r\n        btnLoading: false, //保存按钮加载状态     \r\n        formData: {\r\n          status: '未缴纳'\r\n        }, //表单数据           \r\n        addrules: {\r\n          dbid: [{ required: true, message: '请选择宿舍楼', trigger: 'onchange' }],\r\n          doro: [{ required: true, message: '请选择宿舍编号', trigger: 'onchange' }],\r\n          ftype: [{ required: true, message: '请选择费用类型', trigger: 'onchange' }],\r\n          title: [{ required: true, message: '请输入费用标题', trigger: 'blur' },\r\n],          amount: [{ required: true, message: '请输入费用金额', trigger: 'blur' },\r\n],         status: [{ required: true, message: '请输入缴纳状态', trigger: 'blur' },\r\n],        },\r\n\r\n      };\r\n    },\r\n    mounted() {\r\n    \r\n      this.getdormbuildingList();\r\n      this.getdormitoryList();\r\n    },\r\n\r\n \r\n    methods: {    \r\n   // 添加\r\n    save() {       \r\n         this.$refs[\"formDataRef\"].validate((valid) => { //验证表单\r\n           if (valid) {\r\n             let url = base + \"/waterelectricityfee/add\";\r\n             this.btnLoading = true;\r\n             request.post(url, this.formData).then((res) => { //发送请求         \r\n               if (res.code == 200) {\r\n                 this.$message({\r\n                   message: \"操作成功\",\r\n                   type: \"success\",\r\n                   offset: 320,\r\n                 });              \r\n                this.$router.push({\r\n                path: \"/WaterelectricityfeeManage\",\r\n                });\r\n               } else {\r\n                 this.$message({\r\n                   message: res.msg,\r\n                   type: \"error\",\r\n                   offset: 320,\r\n                 });\r\n               }\r\n               this.btnLoading=false;\r\n             });\r\n           }        \r\n           \r\n         });\r\n    },\r\n    \r\n       // 返回\r\n        goBack() {\r\n          this.$router.push({\r\n            path: \"/WaterelectricityfeeManage\",\r\n          });\r\n        },       \r\n              \r\n            \r\n    getdormbuildingList() {\r\n      let para = {};\r\n      this.listLoading = true;\r\n      let url = base + \"/dormbuilding/list?currentPage=1&pageSize=1000\";\r\n      request.post(url, para).then((res) => {\r\n        this.dormbuildingList = res.resdata;\r\n      });\r\n    },\r\n    \r\n      getdormitoryList() {\r\n var user = JSON.parse(sessionStorage.getItem('user')); //获取用户信息\r\n        let dbid = user.dbid;\r\n        let para = {\r\n        dbid: dbid\r\n      };\r\n      this.listLoading = true;\r\n      let url = base + \"/dormitory/list?currentPage=1&pageSize=1000\";\r\n      request.post(url, para).then((res) => {\r\n        this.dormitoryList = res.resdata;\r\n      });\r\n    },\r\n  \r\n           \r\n           \r\n      },\r\n}\r\n\r\n</script>\r\n<style scoped>\r\n</style>\r\n \r\n\r\n"]}]}