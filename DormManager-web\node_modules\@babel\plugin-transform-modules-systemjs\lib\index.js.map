{"version": 3, "names": ["_helper<PERSON>lugin<PERSON><PERSON>s", "require", "_helperHoistVariables", "_core", "_helperModuleTransforms", "_helperValidatorIdentifier", "buildTemplate", "template", "statement", "buildExportAll", "MISSING_PLUGIN_WARNING", "MISSING_PLUGIN_ERROR", "getExportSpecifierName", "node", "stringSpecifiers", "type", "name", "stringValue", "value", "isIdentifierName", "add", "Error", "constructExportCall", "path", "exportIdent", "exportNames", "exportValues", "exportStarTarget", "statements", "length", "push", "t", "expressionStatement", "callExpression", "stringLiteral", "objectProperties", "i", "exportName", "exportValue", "objectProperty", "has", "identifier", "objectExpression", "exportObj", "scope", "generateUid", "variableDeclaration", "variableDeclarator", "KEY", "generateUidIdentifier", "EXPORT_OBJ", "TARGET", "assignmentExpression", "memberExpression", "_default", "declare", "api", "options", "assertVersion", "systemGlobal", "allowTopLevelThis", "reassignmentVisited", "WeakSet", "reassignmentVisitor", "AssignmentExpression|UpdateExpression", "arg", "isAssignmentExpression", "get", "isObjectPattern", "isArrayPattern", "exprs", "Object", "keys", "getBindingIdentifiers", "getBinding", "exportedNames", "exports", "exportedName", "buildCall", "expression", "replaceWith", "sequenceExpression", "isIdentifier", "isPostUpdateExpression", "isUpdateExpression", "prefix", "binaryExpression", "operator", "unaryExpression", "cloneNode", "argument", "numericLiteral", "pre", "file", "set", "visitor", "types", "importExpression", "state", "isCallExpression", "isImport", "callee", "console", "warn", "buildDynamicImport", "specifier", "contextIdent", "MetaProperty", "meta", "property", "ReferencedIdentifier", "hasBinding", "Program", "enter", "Set", "rewriteThis", "exit", "exportMap", "create", "modules", "beforeBody", "setters", "sources", "variableIds", "removedPaths", "addExportName", "key", "val", "pushModule", "source", "specifiers", "module", "for<PERSON>ach", "m", "imports", "concat", "buildExportCall", "body", "isFunctionDeclaration", "isClassDeclaration", "id", "toExpression", "isVariableDeclaration", "kind", "isImportDeclaration", "removeBinding", "remove", "isExportAllDeclaration", "isExportDefaultDeclaration", "declar", "declaration", "buildUndefinedNode", "isExportNamedDeclaration", "isFunction", "isClass", "nodes", "local", "exported", "binding", "replaceWithMultiple", "setterBody", "target", "isImportNamespaceSpecifier", "isImportDefaultSpecifier", "importSpecifier", "isImportSpecifier", "imported", "hasExportStar", "isExportSpecifier", "isStringLiteral", "functionExpression", "blockStatement", "moduleName", "getModuleName", "opts", "hoistVariables", "hasInit", "unshift", "map", "traverse", "hasTLA", "AwaitExpression", "stop", "Function", "skip", "noScope", "SYSTEM_REGISTER", "BEFORE_BODY", "MODULE_NAME", "SETTERS", "arrayExpression", "EXECUTE", "SOURCES", "EXPORT_IDENTIFIER", "CONTEXT_IDENTIFIER", "default"], "sources": ["../src/index.ts"], "sourcesContent": ["import { declare } from \"@babel/helper-plugin-utils\";\nimport hoistVariables from \"@babel/helper-hoist-variables\";\nimport { template, types as t, type PluginPass } from \"@babel/core\";\nimport {\n  buildDynamicImport,\n  getModuleName,\n  rewriteThis,\n} from \"@babel/helper-module-transforms\";\nimport type { PluginOptions } from \"@babel/helper-module-transforms\";\nimport { isIdentifierName } from \"@babel/helper-validator-identifier\";\nimport type { Node<PERSON>ath, Scope, Visitor } from \"@babel/traverse\";\n\nconst buildTemplate = template.statement(`\n  SYSTEM_REGISTER(MODULE_NAME, SOURCES, function (EXPORT_IDENTIFIER, CONTEXT_IDENTIFIER) {\n    \"use strict\";\n    BEFORE_BODY;\n    return {\n      setters: SETTERS,\n      execute: EXECUTE,\n    };\n  });\n`);\n\nconst buildExportAll = template.statement(`\n  for (var KEY in TARGET) {\n    if (KEY !== \"default\" && KEY !== \"__esModule\") EXPORT_OBJ[KEY] = TARGET[KEY];\n  }\n`);\n\nconst MISSING_PLUGIN_WARNING = `\\\nWARNING: Dynamic import() transformation must be enabled using the\n         @babel/plugin-transform-dynamic-import plugin. Babel 8 will\n         no longer transform import() without using that plugin.\n`;\n\nconst MISSING_PLUGIN_ERROR = `\\\nERROR: Dynamic import() transformation must be enabled using the\n       @babel/plugin-transform-dynamic-import plugin. Babel 8\n       no longer transforms import() without using that plugin.\n`;\n\n//todo: use getExportSpecifierName in `helper-module-transforms` when this library is refactored to NodePath usage.\n\nexport function getExportSpecifierName(\n  node: t.Node,\n  stringSpecifiers: Set<string>,\n): string {\n  if (node.type === \"Identifier\") {\n    return node.name;\n  } else if (node.type === \"StringLiteral\") {\n    const stringValue = node.value;\n    // add specifier value to `stringSpecifiers` only when it can not be converted to an identifier name\n    // i.e In `import { \"foo\" as bar }`\n    // we do not consider `\"foo\"` to be a `stringSpecifier` because we can treat it as\n    // `import { foo as bar }`\n    // This helps minimize the size of `stringSpecifiers` and reduce overhead of checking valid identifier names\n    // when building transpiled code from metadata\n    if (!isIdentifierName(stringValue)) {\n      stringSpecifiers.add(stringValue);\n    }\n    return stringValue;\n  } else {\n    throw new Error(\n      `Expected export specifier to be either Identifier or StringLiteral, got ${node.type}`,\n    );\n  }\n}\n\ntype PluginState = {\n  contextIdent: string;\n  // List of names that should only be printed as string literals.\n  // i.e. `import { \"any unicode\" as foo } from \"some-module\"`\n  // `stringSpecifiers` is Set(1) [\"any unicode\"]\n  // In most cases `stringSpecifiers` is an empty Set\n  stringSpecifiers: Set<string>;\n};\n\ntype ModuleMetadata = {\n  key: string;\n  imports: any[];\n  exports: any[];\n};\n\nfunction constructExportCall(\n  path: NodePath<t.Program>,\n  exportIdent: t.Identifier,\n  exportNames: string[],\n  exportValues: t.Expression[],\n  exportStarTarget: t.Identifier | null,\n  stringSpecifiers: Set<string>,\n) {\n  const statements = [];\n  if (!exportStarTarget) {\n    if (exportNames.length === 1) {\n      statements.push(\n        t.expressionStatement(\n          t.callExpression(exportIdent, [\n            t.stringLiteral(exportNames[0]),\n            exportValues[0],\n          ]),\n        ),\n      );\n    } else {\n      const objectProperties = [];\n      for (let i = 0; i < exportNames.length; i++) {\n        const exportName = exportNames[i];\n        const exportValue = exportValues[i];\n        objectProperties.push(\n          t.objectProperty(\n            stringSpecifiers.has(exportName)\n              ? t.stringLiteral(exportName)\n              : t.identifier(exportName),\n            exportValue,\n          ),\n        );\n      }\n      statements.push(\n        t.expressionStatement(\n          t.callExpression(exportIdent, [t.objectExpression(objectProperties)]),\n        ),\n      );\n    }\n  } else {\n    const exportObj = path.scope.generateUid(\"exportObj\");\n\n    statements.push(\n      t.variableDeclaration(\"var\", [\n        t.variableDeclarator(t.identifier(exportObj), t.objectExpression([])),\n      ]),\n    );\n\n    statements.push(\n      buildExportAll({\n        KEY: path.scope.generateUidIdentifier(\"key\"),\n        EXPORT_OBJ: t.identifier(exportObj),\n        TARGET: exportStarTarget,\n      }),\n    );\n\n    for (let i = 0; i < exportNames.length; i++) {\n      const exportName = exportNames[i];\n      const exportValue = exportValues[i];\n\n      statements.push(\n        t.expressionStatement(\n          t.assignmentExpression(\n            \"=\",\n            t.memberExpression(\n              t.identifier(exportObj),\n              t.identifier(exportName),\n            ),\n            exportValue,\n          ),\n        ),\n      );\n    }\n\n    statements.push(\n      t.expressionStatement(\n        t.callExpression(exportIdent, [t.identifier(exportObj)]),\n      ),\n    );\n  }\n  return statements;\n}\n\nexport interface Options extends PluginOptions {\n  allowTopLevelThis?: boolean;\n  systemGlobal?: string;\n}\n\ntype ReassignmentVisitorState = {\n  scope: Scope;\n  exports: any;\n  buildCall: (name: string, value: t.Expression) => t.ExpressionStatement;\n};\n\nexport default declare<PluginState>((api, options: Options) => {\n  api.assertVersion(7);\n\n  const { systemGlobal = \"System\", allowTopLevelThis = false } = options;\n  const reassignmentVisited = new WeakSet();\n\n  const reassignmentVisitor: Visitor<ReassignmentVisitorState> = {\n    \"AssignmentExpression|UpdateExpression\"(\n      path: NodePath<t.AssignmentExpression | t.UpdateExpression>,\n    ) {\n      if (reassignmentVisited.has(path.node)) return;\n      reassignmentVisited.add(path.node);\n\n      const arg = path.isAssignmentExpression()\n        ? path.get(\"left\")\n        : path.get(\"argument\");\n\n      if (arg.isObjectPattern() || arg.isArrayPattern()) {\n        const exprs: t.SequenceExpression[\"expressions\"] = [path.node];\n        for (const name of Object.keys(arg.getBindingIdentifiers())) {\n          if (this.scope.getBinding(name) !== path.scope.getBinding(name)) {\n            return;\n          }\n          const exportedNames = this.exports[name];\n          if (!exportedNames) continue;\n          for (const exportedName of exportedNames) {\n            exprs.push(\n              this.buildCall(exportedName, t.identifier(name)).expression,\n            );\n          }\n        }\n        path.replaceWith(t.sequenceExpression(exprs));\n        return;\n      }\n\n      if (!arg.isIdentifier()) return;\n\n      const name = arg.node.name;\n\n      // redeclared in this scope\n      if (this.scope.getBinding(name) !== path.scope.getBinding(name)) return;\n\n      const exportedNames = this.exports[name];\n      if (!exportedNames) return;\n\n      let node: t.Expression = path.node;\n\n      // if it is a non-prefix update expression (x++ etc)\n      // then we must replace with the expression (_export('x', x + 1), x++)\n      // in order to ensure the same update expression value\n      const isPostUpdateExpression = t.isUpdateExpression(node, {\n        prefix: false,\n      });\n      if (isPostUpdateExpression) {\n        node = t.binaryExpression(\n          // @ts-expect-error The operator of a post-update expression must be \"++\" | \"--\"\n          node.operator[0],\n          t.unaryExpression(\n            \"+\",\n            t.cloneNode(\n              // @ts-expect-error node is UpdateExpression\n              node.argument,\n            ),\n          ),\n          t.numericLiteral(1),\n        );\n      }\n\n      for (const exportedName of exportedNames) {\n        node = this.buildCall(exportedName, node).expression;\n      }\n\n      if (isPostUpdateExpression) {\n        node = t.sequenceExpression([node, path.node]);\n      }\n\n      path.replaceWith(node);\n    },\n  };\n\n  return {\n    name: \"transform-modules-systemjs\",\n\n    pre() {\n      this.file.set(\"@babel/plugin-transform-modules-*\", \"systemjs\");\n    },\n\n    visitor: {\n      [\"CallExpression\" +\n        (api.types.importExpression ? \"|ImportExpression\" : \"\")](\n        this: PluginPass & PluginState,\n        path: NodePath<t.CallExpression | t.ImportExpression>,\n        state: PluginState,\n      ) {\n        if (path.isCallExpression() && !t.isImport(path.node.callee)) return;\n        if (path.isCallExpression()) {\n          if (!this.file.has(\"@babel/plugin-proposal-dynamic-import\")) {\n            if (process.env.BABEL_8_BREAKING) {\n              throw new Error(MISSING_PLUGIN_ERROR);\n            } else {\n              console.warn(MISSING_PLUGIN_WARNING);\n            }\n          }\n        } else {\n          // when createImportExpressions is true, we require the dynamic import transform\n          if (!this.file.has(\"@babel/plugin-proposal-dynamic-import\")) {\n            throw new Error(MISSING_PLUGIN_ERROR);\n          }\n        }\n        path.replaceWith(\n          buildDynamicImport(path.node, false, true, specifier =>\n            t.callExpression(\n              t.memberExpression(\n                t.identifier(state.contextIdent),\n                t.identifier(\"import\"),\n              ),\n              [specifier],\n            ),\n          ),\n        );\n      },\n\n      MetaProperty(path, state: PluginState) {\n        if (\n          path.node.meta.name === \"import\" &&\n          path.node.property.name === \"meta\"\n        ) {\n          path.replaceWith(\n            t.memberExpression(\n              t.identifier(state.contextIdent),\n              t.identifier(\"meta\"),\n            ),\n          );\n        }\n      },\n\n      ReferencedIdentifier(path, state) {\n        if (\n          path.node.name === \"__moduleName\" &&\n          !path.scope.hasBinding(\"__moduleName\")\n        ) {\n          path.replaceWith(\n            t.memberExpression(\n              t.identifier(state.contextIdent),\n              t.identifier(\"id\"),\n            ),\n          );\n        }\n      },\n\n      Program: {\n        enter(path, state) {\n          state.contextIdent = path.scope.generateUid(\"context\");\n          state.stringSpecifiers = new Set();\n          if (!allowTopLevelThis) {\n            rewriteThis(path);\n          }\n        },\n        exit(path, state) {\n          const scope = path.scope;\n          const exportIdent = scope.generateUid(\"export\");\n          const { contextIdent, stringSpecifiers } = state;\n\n          const exportMap: Record<string, string[]> = Object.create(null);\n          const modules: ModuleMetadata[] = [];\n\n          const beforeBody = [];\n          const setters: t.Expression[] = [];\n          const sources: t.StringLiteral[] = [];\n          const variableIds = [];\n          const removedPaths = [];\n\n          function addExportName(key: string, val: string) {\n            exportMap[key] = exportMap[key] || [];\n            exportMap[key].push(val);\n          }\n\n          function pushModule(\n            source: string,\n            key: \"imports\" | \"exports\",\n            specifiers: t.ModuleSpecifier[] | t.ExportAllDeclaration,\n          ) {\n            let module: ModuleMetadata;\n            modules.forEach(function (m) {\n              if (m.key === source) {\n                module = m;\n              }\n            });\n            if (!module) {\n              modules.push(\n                (module = { key: source, imports: [], exports: [] }),\n              );\n            }\n            module[key] = module[key].concat(specifiers);\n          }\n\n          function buildExportCall(name: string, val: t.Expression) {\n            return t.expressionStatement(\n              t.callExpression(t.identifier(exportIdent), [\n                t.stringLiteral(name),\n                val,\n              ]),\n            );\n          }\n\n          const exportNames = [];\n          const exportValues: t.Expression[] = [];\n\n          const body = path.get(\"body\");\n\n          for (const path of body) {\n            if (path.isFunctionDeclaration()) {\n              beforeBody.push(path.node);\n              removedPaths.push(path);\n            } else if (path.isClassDeclaration()) {\n              variableIds.push(t.cloneNode(path.node.id));\n              path.replaceWith(\n                t.expressionStatement(\n                  t.assignmentExpression(\n                    \"=\",\n                    t.cloneNode(path.node.id),\n                    t.toExpression(path.node),\n                  ),\n                ),\n              );\n            } else if (path.isVariableDeclaration()) {\n              // Convert top-level variable declarations to \"var\",\n              // because they must be hoisted\n              path.node.kind = \"var\";\n            } else if (path.isImportDeclaration()) {\n              const source = path.node.source.value;\n              pushModule(source, \"imports\", path.node.specifiers);\n              for (const name of Object.keys(path.getBindingIdentifiers())) {\n                scope.removeBinding(name);\n                variableIds.push(t.identifier(name));\n              }\n              path.remove();\n            } else if (path.isExportAllDeclaration()) {\n              pushModule(path.node.source.value, \"exports\", path.node);\n              path.remove();\n            } else if (path.isExportDefaultDeclaration()) {\n              const declar = path.node.declaration;\n              if (t.isClassDeclaration(declar)) {\n                const id = declar.id;\n                if (id) {\n                  exportNames.push(\"default\");\n                  exportValues.push(scope.buildUndefinedNode());\n                  variableIds.push(t.cloneNode(id));\n                  addExportName(id.name, \"default\");\n                  path.replaceWith(\n                    t.expressionStatement(\n                      t.assignmentExpression(\n                        \"=\",\n                        t.cloneNode(id),\n                        t.toExpression(declar),\n                      ),\n                    ),\n                  );\n                } else {\n                  exportNames.push(\"default\");\n                  exportValues.push(t.toExpression(declar));\n                  removedPaths.push(path);\n                }\n              } else if (t.isFunctionDeclaration(declar)) {\n                const id = declar.id;\n                if (id) {\n                  beforeBody.push(declar);\n                  exportNames.push(\"default\");\n                  exportValues.push(t.cloneNode(id));\n                  addExportName(id.name, \"default\");\n                } else {\n                  exportNames.push(\"default\");\n                  exportValues.push(t.toExpression(declar));\n                }\n                removedPaths.push(path);\n              } else {\n                // @ts-expect-error TSDeclareFunction is not expected here\n                path.replaceWith(buildExportCall(\"default\", declar));\n              }\n            } else if (path.isExportNamedDeclaration()) {\n              const declar = path.node.declaration;\n\n              if (declar) {\n                path.replaceWith(declar);\n\n                if (t.isFunction(declar)) {\n                  const name = declar.id.name;\n                  addExportName(name, name);\n                  beforeBody.push(declar);\n                  exportNames.push(name);\n                  exportValues.push(t.cloneNode(declar.id));\n                  removedPaths.push(path);\n                } else if (t.isClass(declar)) {\n                  const name = declar.id.name;\n                  exportNames.push(name);\n                  exportValues.push(scope.buildUndefinedNode());\n                  variableIds.push(t.cloneNode(declar.id));\n                  path.replaceWith(\n                    t.expressionStatement(\n                      t.assignmentExpression(\n                        \"=\",\n                        t.cloneNode(declar.id),\n                        t.toExpression(declar),\n                      ),\n                    ),\n                  );\n                  addExportName(name, name);\n                } else {\n                  if (t.isVariableDeclaration(declar)) {\n                    // Convert top-level variable declarations to \"var\",\n                    // because they must be hoisted\n                    declar.kind = \"var\";\n                  }\n                  for (const name of Object.keys(\n                    t.getBindingIdentifiers(declar),\n                  )) {\n                    addExportName(name, name);\n                  }\n                }\n              } else {\n                const specifiers = path.node.specifiers;\n                if (specifiers?.length) {\n                  if (path.node.source) {\n                    pushModule(path.node.source.value, \"exports\", specifiers);\n                    path.remove();\n                  } else {\n                    const nodes = [];\n\n                    for (const specifier of specifiers) {\n                      // @ts-expect-error This isn't an \"export ... from\" declaration\n                      // because path.node.source is falsy, so the local specifier exists.\n                      const { local, exported } = specifier;\n\n                      const binding = scope.getBinding(local.name);\n                      const exportedName = getExportSpecifierName(\n                        exported,\n                        stringSpecifiers,\n                      );\n                      // hoisted function export\n                      if (\n                        binding &&\n                        t.isFunctionDeclaration(binding.path.node)\n                      ) {\n                        exportNames.push(exportedName);\n                        exportValues.push(t.cloneNode(local));\n                      }\n                      // only globals also exported this way\n                      else if (!binding) {\n                        nodes.push(buildExportCall(exportedName, local));\n                      }\n                      addExportName(local.name, exportedName);\n                    }\n\n                    path.replaceWithMultiple(nodes);\n                  }\n                } else {\n                  path.remove();\n                }\n              }\n            }\n          }\n\n          modules.forEach(function (specifiers) {\n            const setterBody = [];\n            const target = scope.generateUid(specifiers.key);\n\n            for (let specifier of specifiers.imports) {\n              if (t.isImportNamespaceSpecifier(specifier)) {\n                setterBody.push(\n                  t.expressionStatement(\n                    t.assignmentExpression(\n                      \"=\",\n                      specifier.local,\n                      t.identifier(target),\n                    ),\n                  ),\n                );\n              } else if (t.isImportDefaultSpecifier(specifier)) {\n                specifier = t.importSpecifier(\n                  specifier.local,\n                  t.identifier(\"default\"),\n                );\n              }\n\n              if (t.isImportSpecifier(specifier)) {\n                const { imported } = specifier;\n                setterBody.push(\n                  t.expressionStatement(\n                    t.assignmentExpression(\n                      \"=\",\n                      specifier.local,\n                      t.memberExpression(\n                        t.identifier(target),\n                        specifier.imported,\n                        /* computed */ imported.type === \"StringLiteral\",\n                      ),\n                    ),\n                  ),\n                );\n              }\n            }\n\n            if (specifiers.exports.length) {\n              const exportNames = [];\n              const exportValues = [];\n              let hasExportStar = false;\n\n              for (const node of specifiers.exports) {\n                if (t.isExportAllDeclaration(node)) {\n                  hasExportStar = true;\n                } else if (t.isExportSpecifier(node)) {\n                  const exportedName = getExportSpecifierName(\n                    node.exported,\n                    stringSpecifiers,\n                  );\n                  exportNames.push(exportedName);\n                  exportValues.push(\n                    t.memberExpression(\n                      t.identifier(target),\n                      node.local,\n                      t.isStringLiteral(node.local),\n                    ),\n                  );\n                } else {\n                  // todo\n                }\n              }\n\n              setterBody.push(\n                ...constructExportCall(\n                  path,\n                  t.identifier(exportIdent),\n                  exportNames,\n                  exportValues,\n                  hasExportStar ? t.identifier(target) : null,\n                  stringSpecifiers,\n                ),\n              );\n            }\n\n            sources.push(t.stringLiteral(specifiers.key));\n            setters.push(\n              t.functionExpression(\n                null,\n                [t.identifier(target)],\n                t.blockStatement(setterBody),\n              ),\n            );\n          });\n\n          let moduleName = getModuleName(this.file.opts, options);\n          // @ts-expect-error todo(flow->ts): do not reuse variables\n          if (moduleName) moduleName = t.stringLiteral(moduleName);\n\n          hoistVariables(path, (id, name, hasInit) => {\n            variableIds.push(id);\n            if (!hasInit && name in exportMap) {\n              for (const exported of exportMap[name]) {\n                exportNames.push(exported);\n                exportValues.push(scope.buildUndefinedNode());\n              }\n            }\n          });\n\n          if (variableIds.length) {\n            beforeBody.unshift(\n              t.variableDeclaration(\n                \"var\",\n                variableIds.map(id => t.variableDeclarator(id)),\n              ),\n            );\n          }\n\n          if (exportNames.length) {\n            beforeBody.push(\n              ...constructExportCall(\n                path,\n                t.identifier(exportIdent),\n                exportNames,\n                exportValues,\n                null,\n                stringSpecifiers,\n              ),\n            );\n          }\n\n          path.traverse(reassignmentVisitor, {\n            exports: exportMap,\n            buildCall: buildExportCall,\n            scope,\n          });\n\n          for (const path of removedPaths) {\n            path.remove();\n          }\n\n          let hasTLA = false;\n          path.traverse({\n            AwaitExpression(path) {\n              hasTLA = true;\n              path.stop();\n            },\n            Function(path) {\n              path.skip();\n            },\n            // @ts-expect-error - todo: add noScope to type definitions\n            noScope: true,\n          });\n\n          path.node.body = [\n            buildTemplate({\n              SYSTEM_REGISTER: t.memberExpression(\n                t.identifier(systemGlobal),\n                t.identifier(\"register\"),\n              ),\n              BEFORE_BODY: beforeBody,\n              MODULE_NAME: moduleName,\n              SETTERS: t.arrayExpression(setters),\n              EXECUTE: t.functionExpression(\n                null,\n                [],\n                t.blockStatement(path.node.body),\n                false,\n                hasTLA,\n              ),\n              SOURCES: t.arrayExpression(sources),\n              EXPORT_IDENTIFIER: t.identifier(exportIdent),\n              CONTEXT_IDENTIFIER: t.identifier(contextIdent),\n            }),\n          ];\n        },\n      },\n    },\n  };\n});\n"], "mappings": ";;;;;;;AAAA,IAAAA,kBAAA,GAAAC,OAAA;AACA,IAAAC,qBAAA,GAAAD,OAAA;AACA,IAAAE,KAAA,GAAAF,OAAA;AACA,IAAAG,uBAAA,GAAAH,OAAA;AAMA,IAAAI,0BAAA,GAAAJ,OAAA;AAGA,MAAMK,aAAa,GAAGC,cAAQ,CAACC,SAAS,CAAE;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAAC;AAEF,MAAMC,cAAc,GAAGF,cAAQ,CAACC,SAAS,CAAE;AAC3C;AACA;AACA;AACA,CAAC,CAAC;AAEF,MAAME,sBAAsB,GAAI;AAChC;AACA;AACA;AACA,CAAC;AAED,MAAMC,oBAAoB,GAAI;AAC9B;AACA;AACA;AACA,CAAC;AAIM,SAASC,sBAAsBA,CACpCC,IAAY,EACZC,gBAA6B,EACrB;EACR,IAAID,IAAI,CAACE,IAAI,KAAK,YAAY,EAAE;IAC9B,OAAOF,IAAI,CAACG,IAAI;EAClB,CAAC,MAAM,IAAIH,IAAI,CAACE,IAAI,KAAK,eAAe,EAAE;IACxC,MAAME,WAAW,GAAGJ,IAAI,CAACK,KAAK;IAO9B,IAAI,CAAC,IAAAC,2CAAgB,EAACF,WAAW,CAAC,EAAE;MAClCH,gBAAgB,CAACM,GAAG,CAACH,WAAW,CAAC;IACnC;IACA,OAAOA,WAAW;EACpB,CAAC,MAAM;IACL,MAAM,IAAII,KAAK,CACZ,2EAA0ER,IAAI,CAACE,IAAK,EACvF,CAAC;EACH;AACF;AAiBA,SAASO,mBAAmBA,CAC1BC,IAAyB,EACzBC,WAAyB,EACzBC,WAAqB,EACrBC,YAA4B,EAC5BC,gBAAqC,EACrCb,gBAA6B,EAC7B;EACA,MAAMc,UAAU,GAAG,EAAE;EACrB,IAAI,CAACD,gBAAgB,EAAE;IACrB,IAAIF,WAAW,CAACI,MAAM,KAAK,CAAC,EAAE;MAC5BD,UAAU,CAACE,IAAI,CACbC,WAAC,CAACC,mBAAmB,CACnBD,WAAC,CAACE,cAAc,CAACT,WAAW,EAAE,CAC5BO,WAAC,CAACG,aAAa,CAACT,WAAW,CAAC,CAAC,CAAC,CAAC,EAC/BC,YAAY,CAAC,CAAC,CAAC,CAChB,CACH,CACF,CAAC;IACH,CAAC,MAAM;MACL,MAAMS,gBAAgB,GAAG,EAAE;MAC3B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGX,WAAW,CAACI,MAAM,EAAEO,CAAC,EAAE,EAAE;QAC3C,MAAMC,UAAU,GAAGZ,WAAW,CAACW,CAAC,CAAC;QACjC,MAAME,WAAW,GAAGZ,YAAY,CAACU,CAAC,CAAC;QACnCD,gBAAgB,CAACL,IAAI,CACnBC,WAAC,CAACQ,cAAc,CACdzB,gBAAgB,CAAC0B,GAAG,CAACH,UAAU,CAAC,GAC5BN,WAAC,CAACG,aAAa,CAACG,UAAU,CAAC,GAC3BN,WAAC,CAACU,UAAU,CAACJ,UAAU,CAAC,EAC5BC,WACF,CACF,CAAC;MACH;MACAV,UAAU,CAACE,IAAI,CACbC,WAAC,CAACC,mBAAmB,CACnBD,WAAC,CAACE,cAAc,CAACT,WAAW,EAAE,CAACO,WAAC,CAACW,gBAAgB,CAACP,gBAAgB,CAAC,CAAC,CACtE,CACF,CAAC;IACH;EACF,CAAC,MAAM;IACL,MAAMQ,SAAS,GAAGpB,IAAI,CAACqB,KAAK,CAACC,WAAW,CAAC,WAAW,CAAC;IAErDjB,UAAU,CAACE,IAAI,CACbC,WAAC,CAACe,mBAAmB,CAAC,KAAK,EAAE,CAC3Bf,WAAC,CAACgB,kBAAkB,CAAChB,WAAC,CAACU,UAAU,CAACE,SAAS,CAAC,EAAEZ,WAAC,CAACW,gBAAgB,CAAC,EAAE,CAAC,CAAC,CACtE,CACH,CAAC;IAEDd,UAAU,CAACE,IAAI,CACbrB,cAAc,CAAC;MACbuC,GAAG,EAAEzB,IAAI,CAACqB,KAAK,CAACK,qBAAqB,CAAC,KAAK,CAAC;MAC5CC,UAAU,EAAEnB,WAAC,CAACU,UAAU,CAACE,SAAS,CAAC;MACnCQ,MAAM,EAAExB;IACV,CAAC,CACH,CAAC;IAED,KAAK,IAAIS,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGX,WAAW,CAACI,MAAM,EAAEO,CAAC,EAAE,EAAE;MAC3C,MAAMC,UAAU,GAAGZ,WAAW,CAACW,CAAC,CAAC;MACjC,MAAME,WAAW,GAAGZ,YAAY,CAACU,CAAC,CAAC;MAEnCR,UAAU,CAACE,IAAI,CACbC,WAAC,CAACC,mBAAmB,CACnBD,WAAC,CAACqB,oBAAoB,CACpB,GAAG,EACHrB,WAAC,CAACsB,gBAAgB,CAChBtB,WAAC,CAACU,UAAU,CAACE,SAAS,CAAC,EACvBZ,WAAC,CAACU,UAAU,CAACJ,UAAU,CACzB,CAAC,EACDC,WACF,CACF,CACF,CAAC;IACH;IAEAV,UAAU,CAACE,IAAI,CACbC,WAAC,CAACC,mBAAmB,CACnBD,WAAC,CAACE,cAAc,CAACT,WAAW,EAAE,CAACO,WAAC,CAACU,UAAU,CAACE,SAAS,CAAC,CAAC,CACzD,CACF,CAAC;EACH;EACA,OAAOf,UAAU;AACnB;AAAC,IAAA0B,QAAA,GAac,IAAAC,0BAAO,EAAc,CAACC,GAAG,EAAEC,OAAgB,KAAK;EAC7DD,GAAG,CAACE,aAAa,CAAC,CAAC,CAAC;EAEpB,MAAM;IAAEC,YAAY,GAAG,QAAQ;IAAEC,iBAAiB,GAAG;EAAM,CAAC,GAAGH,OAAO;EACtE,MAAMI,mBAAmB,GAAG,IAAIC,OAAO,CAAC,CAAC;EAEzC,MAAMC,mBAAsD,GAAG;IAC7D,uCAAuCC,CACrCzC,IAA2D,EAC3D;MACA,IAAIsC,mBAAmB,CAACrB,GAAG,CAACjB,IAAI,CAACV,IAAI,CAAC,EAAE;MACxCgD,mBAAmB,CAACzC,GAAG,CAACG,IAAI,CAACV,IAAI,CAAC;MAElC,MAAMoD,GAAG,GAAG1C,IAAI,CAAC2C,sBAAsB,CAAC,CAAC,GACrC3C,IAAI,CAAC4C,GAAG,CAAC,MAAM,CAAC,GAChB5C,IAAI,CAAC4C,GAAG,CAAC,UAAU,CAAC;MAExB,IAAIF,GAAG,CAACG,eAAe,CAAC,CAAC,IAAIH,GAAG,CAACI,cAAc,CAAC,CAAC,EAAE;QACjD,MAAMC,KAA0C,GAAG,CAAC/C,IAAI,CAACV,IAAI,CAAC;QAC9D,KAAK,MAAMG,IAAI,IAAIuD,MAAM,CAACC,IAAI,CAACP,GAAG,CAACQ,qBAAqB,CAAC,CAAC,CAAC,EAAE;UAC3D,IAAI,IAAI,CAAC7B,KAAK,CAAC8B,UAAU,CAAC1D,IAAI,CAAC,KAAKO,IAAI,CAACqB,KAAK,CAAC8B,UAAU,CAAC1D,IAAI,CAAC,EAAE;YAC/D;UACF;UACA,MAAM2D,aAAa,GAAG,IAAI,CAACC,OAAO,CAAC5D,IAAI,CAAC;UACxC,IAAI,CAAC2D,aAAa,EAAE;UACpB,KAAK,MAAME,YAAY,IAAIF,aAAa,EAAE;YACxCL,KAAK,CAACxC,IAAI,CACR,IAAI,CAACgD,SAAS,CAACD,YAAY,EAAE9C,WAAC,CAACU,UAAU,CAACzB,IAAI,CAAC,CAAC,CAAC+D,UACnD,CAAC;UACH;QACF;QACAxD,IAAI,CAACyD,WAAW,CAACjD,WAAC,CAACkD,kBAAkB,CAACX,KAAK,CAAC,CAAC;QAC7C;MACF;MAEA,IAAI,CAACL,GAAG,CAACiB,YAAY,CAAC,CAAC,EAAE;MAEzB,MAAMlE,IAAI,GAAGiD,GAAG,CAACpD,IAAI,CAACG,IAAI;MAG1B,IAAI,IAAI,CAAC4B,KAAK,CAAC8B,UAAU,CAAC1D,IAAI,CAAC,KAAKO,IAAI,CAACqB,KAAK,CAAC8B,UAAU,CAAC1D,IAAI,CAAC,EAAE;MAEjE,MAAM2D,aAAa,GAAG,IAAI,CAACC,OAAO,CAAC5D,IAAI,CAAC;MACxC,IAAI,CAAC2D,aAAa,EAAE;MAEpB,IAAI9D,IAAkB,GAAGU,IAAI,CAACV,IAAI;MAKlC,MAAMsE,sBAAsB,GAAGpD,WAAC,CAACqD,kBAAkB,CAACvE,IAAI,EAAE;QACxDwE,MAAM,EAAE;MACV,CAAC,CAAC;MACF,IAAIF,sBAAsB,EAAE;QAC1BtE,IAAI,GAAGkB,WAAC,CAACuD,gBAAgB,CAEvBzE,IAAI,CAAC0E,QAAQ,CAAC,CAAC,CAAC,EAChBxD,WAAC,CAACyD,eAAe,CACf,GAAG,EACHzD,WAAC,CAAC0D,SAAS,CAET5E,IAAI,CAAC6E,QACP,CACF,CAAC,EACD3D,WAAC,CAAC4D,cAAc,CAAC,CAAC,CACpB,CAAC;MACH;MAEA,KAAK,MAAMd,YAAY,IAAIF,aAAa,EAAE;QACxC9D,IAAI,GAAG,IAAI,CAACiE,SAAS,CAACD,YAAY,EAAEhE,IAAI,CAAC,CAACkE,UAAU;MACtD;MAEA,IAAII,sBAAsB,EAAE;QAC1BtE,IAAI,GAAGkB,WAAC,CAACkD,kBAAkB,CAAC,CAACpE,IAAI,EAAEU,IAAI,CAACV,IAAI,CAAC,CAAC;MAChD;MAEAU,IAAI,CAACyD,WAAW,CAACnE,IAAI,CAAC;IACxB;EACF,CAAC;EAED,OAAO;IACLG,IAAI,EAAE,4BAA4B;IAElC4E,GAAGA,CAAA,EAAG;MACJ,IAAI,CAACC,IAAI,CAACC,GAAG,CAAC,mCAAmC,EAAE,UAAU,CAAC;IAChE,CAAC;IAEDC,OAAO,EAAE;MACP,CAAC,gBAAgB,IACdvC,GAAG,CAACwC,KAAK,CAACC,gBAAgB,GAAG,mBAAmB,GAAG,EAAE,CAAC,EAEvD1E,IAAqD,EACrD2E,KAAkB,EAClB;QACA,IAAI3E,IAAI,CAAC4E,gBAAgB,CAAC,CAAC,IAAI,CAACpE,WAAC,CAACqE,QAAQ,CAAC7E,IAAI,CAACV,IAAI,CAACwF,MAAM,CAAC,EAAE;QAC9D,IAAI9E,IAAI,CAAC4E,gBAAgB,CAAC,CAAC,EAAE;UAC3B,IAAI,CAAC,IAAI,CAACN,IAAI,CAACrD,GAAG,CAAC,uCAAuC,CAAC,EAAE;YAGpD;cACL8D,OAAO,CAACC,IAAI,CAAC7F,sBAAsB,CAAC;YACtC;UACF;QACF,CAAC,MAAM;UAEL,IAAI,CAAC,IAAI,CAACmF,IAAI,CAACrD,GAAG,CAAC,uCAAuC,CAAC,EAAE;YAC3D,MAAM,IAAInB,KAAK,CAACV,oBAAoB,CAAC;UACvC;QACF;QACAY,IAAI,CAACyD,WAAW,CACd,IAAAwB,0CAAkB,EAACjF,IAAI,CAACV,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE4F,SAAS,IAClD1E,WAAC,CAACE,cAAc,CACdF,WAAC,CAACsB,gBAAgB,CAChBtB,WAAC,CAACU,UAAU,CAACyD,KAAK,CAACQ,YAAY,CAAC,EAChC3E,WAAC,CAACU,UAAU,CAAC,QAAQ,CACvB,CAAC,EACD,CAACgE,SAAS,CACZ,CACF,CACF,CAAC;MACH,CAAC;MAEDE,YAAYA,CAACpF,IAAI,EAAE2E,KAAkB,EAAE;QACrC,IACE3E,IAAI,CAACV,IAAI,CAAC+F,IAAI,CAAC5F,IAAI,KAAK,QAAQ,IAChCO,IAAI,CAACV,IAAI,CAACgG,QAAQ,CAAC7F,IAAI,KAAK,MAAM,EAClC;UACAO,IAAI,CAACyD,WAAW,CACdjD,WAAC,CAACsB,gBAAgB,CAChBtB,WAAC,CAACU,UAAU,CAACyD,KAAK,CAACQ,YAAY,CAAC,EAChC3E,WAAC,CAACU,UAAU,CAAC,MAAM,CACrB,CACF,CAAC;QACH;MACF,CAAC;MAEDqE,oBAAoBA,CAACvF,IAAI,EAAE2E,KAAK,EAAE;QAChC,IACE3E,IAAI,CAACV,IAAI,CAACG,IAAI,KAAK,cAAc,IACjC,CAACO,IAAI,CAACqB,KAAK,CAACmE,UAAU,CAAC,cAAc,CAAC,EACtC;UACAxF,IAAI,CAACyD,WAAW,CACdjD,WAAC,CAACsB,gBAAgB,CAChBtB,WAAC,CAACU,UAAU,CAACyD,KAAK,CAACQ,YAAY,CAAC,EAChC3E,WAAC,CAACU,UAAU,CAAC,IAAI,CACnB,CACF,CAAC;QACH;MACF,CAAC;MAEDuE,OAAO,EAAE;QACPC,KAAKA,CAAC1F,IAAI,EAAE2E,KAAK,EAAE;UACjBA,KAAK,CAACQ,YAAY,GAAGnF,IAAI,CAACqB,KAAK,CAACC,WAAW,CAAC,SAAS,CAAC;UACtDqD,KAAK,CAACpF,gBAAgB,GAAG,IAAIoG,GAAG,CAAC,CAAC;UAClC,IAAI,CAACtD,iBAAiB,EAAE;YACtB,IAAAuD,mCAAW,EAAC5F,IAAI,CAAC;UACnB;QACF,CAAC;QACD6F,IAAIA,CAAC7F,IAAI,EAAE2E,KAAK,EAAE;UAChB,MAAMtD,KAAK,GAAGrB,IAAI,CAACqB,KAAK;UACxB,MAAMpB,WAAW,GAAGoB,KAAK,CAACC,WAAW,CAAC,QAAQ,CAAC;UAC/C,MAAM;YAAE6D,YAAY;YAAE5F;UAAiB,CAAC,GAAGoF,KAAK;UAEhD,MAAMmB,SAAmC,GAAG9C,MAAM,CAAC+C,MAAM,CAAC,IAAI,CAAC;UAC/D,MAAMC,OAAyB,GAAG,EAAE;UAEpC,MAAMC,UAAU,GAAG,EAAE;UACrB,MAAMC,OAAuB,GAAG,EAAE;UAClC,MAAMC,OAA0B,GAAG,EAAE;UACrC,MAAMC,WAAW,GAAG,EAAE;UACtB,MAAMC,YAAY,GAAG,EAAE;UAEvB,SAASC,aAAaA,CAACC,GAAW,EAAEC,GAAW,EAAE;YAC/CV,SAAS,CAACS,GAAG,CAAC,GAAGT,SAAS,CAACS,GAAG,CAAC,IAAI,EAAE;YACrCT,SAAS,CAACS,GAAG,CAAC,CAAChG,IAAI,CAACiG,GAAG,CAAC;UAC1B;UAEA,SAASC,UAAUA,CACjBC,MAAc,EACdH,GAA0B,EAC1BI,UAAwD,EACxD;YACA,IAAIC,MAAsB;YAC1BZ,OAAO,CAACa,OAAO,CAAC,UAAUC,CAAC,EAAE;cAC3B,IAAIA,CAAC,CAACP,GAAG,KAAKG,MAAM,EAAE;gBACpBE,MAAM,GAAGE,CAAC;cACZ;YACF,CAAC,CAAC;YACF,IAAI,CAACF,MAAM,EAAE;cACXZ,OAAO,CAACzF,IAAI,CACTqG,MAAM,GAAG;gBAAEL,GAAG,EAAEG,MAAM;gBAAEK,OAAO,EAAE,EAAE;gBAAE1D,OAAO,EAAE;cAAG,CACpD,CAAC;YACH;YACAuD,MAAM,CAACL,GAAG,CAAC,GAAGK,MAAM,CAACL,GAAG,CAAC,CAACS,MAAM,CAACL,UAAU,CAAC;UAC9C;UAEA,SAASM,eAAeA,CAACxH,IAAY,EAAE+G,GAAiB,EAAE;YACxD,OAAOhG,WAAC,CAACC,mBAAmB,CAC1BD,WAAC,CAACE,cAAc,CAACF,WAAC,CAACU,UAAU,CAACjB,WAAW,CAAC,EAAE,CAC1CO,WAAC,CAACG,aAAa,CAAClB,IAAI,CAAC,EACrB+G,GAAG,CACJ,CACH,CAAC;UACH;UAEA,MAAMtG,WAAW,GAAG,EAAE;UACtB,MAAMC,YAA4B,GAAG,EAAE;UAEvC,MAAM+G,IAAI,GAAGlH,IAAI,CAAC4C,GAAG,CAAC,MAAM,CAAC;UAE7B,KAAK,MAAM5C,IAAI,IAAIkH,IAAI,EAAE;YACvB,IAAIlH,IAAI,CAACmH,qBAAqB,CAAC,CAAC,EAAE;cAChClB,UAAU,CAAC1F,IAAI,CAACP,IAAI,CAACV,IAAI,CAAC;cAC1B+G,YAAY,CAAC9F,IAAI,CAACP,IAAI,CAAC;YACzB,CAAC,MAAM,IAAIA,IAAI,CAACoH,kBAAkB,CAAC,CAAC,EAAE;cACpChB,WAAW,CAAC7F,IAAI,CAACC,WAAC,CAAC0D,SAAS,CAAClE,IAAI,CAACV,IAAI,CAAC+H,EAAE,CAAC,CAAC;cAC3CrH,IAAI,CAACyD,WAAW,CACdjD,WAAC,CAACC,mBAAmB,CACnBD,WAAC,CAACqB,oBAAoB,CACpB,GAAG,EACHrB,WAAC,CAAC0D,SAAS,CAAClE,IAAI,CAACV,IAAI,CAAC+H,EAAE,CAAC,EACzB7G,WAAC,CAAC8G,YAAY,CAACtH,IAAI,CAACV,IAAI,CAC1B,CACF,CACF,CAAC;YACH,CAAC,MAAM,IAAIU,IAAI,CAACuH,qBAAqB,CAAC,CAAC,EAAE;cAGvCvH,IAAI,CAACV,IAAI,CAACkI,IAAI,GAAG,KAAK;YACxB,CAAC,MAAM,IAAIxH,IAAI,CAACyH,mBAAmB,CAAC,CAAC,EAAE;cACrC,MAAMf,MAAM,GAAG1G,IAAI,CAACV,IAAI,CAACoH,MAAM,CAAC/G,KAAK;cACrC8G,UAAU,CAACC,MAAM,EAAE,SAAS,EAAE1G,IAAI,CAACV,IAAI,CAACqH,UAAU,CAAC;cACnD,KAAK,MAAMlH,IAAI,IAAIuD,MAAM,CAACC,IAAI,CAACjD,IAAI,CAACkD,qBAAqB,CAAC,CAAC,CAAC,EAAE;gBAC5D7B,KAAK,CAACqG,aAAa,CAACjI,IAAI,CAAC;gBACzB2G,WAAW,CAAC7F,IAAI,CAACC,WAAC,CAACU,UAAU,CAACzB,IAAI,CAAC,CAAC;cACtC;cACAO,IAAI,CAAC2H,MAAM,CAAC,CAAC;YACf,CAAC,MAAM,IAAI3H,IAAI,CAAC4H,sBAAsB,CAAC,CAAC,EAAE;cACxCnB,UAAU,CAACzG,IAAI,CAACV,IAAI,CAACoH,MAAM,CAAC/G,KAAK,EAAE,SAAS,EAAEK,IAAI,CAACV,IAAI,CAAC;cACxDU,IAAI,CAAC2H,MAAM,CAAC,CAAC;YACf,CAAC,MAAM,IAAI3H,IAAI,CAAC6H,0BAA0B,CAAC,CAAC,EAAE;cAC5C,MAAMC,MAAM,GAAG9H,IAAI,CAACV,IAAI,CAACyI,WAAW;cACpC,IAAIvH,WAAC,CAAC4G,kBAAkB,CAACU,MAAM,CAAC,EAAE;gBAChC,MAAMT,EAAE,GAAGS,MAAM,CAACT,EAAE;gBACpB,IAAIA,EAAE,EAAE;kBACNnH,WAAW,CAACK,IAAI,CAAC,SAAS,CAAC;kBAC3BJ,YAAY,CAACI,IAAI,CAACc,KAAK,CAAC2G,kBAAkB,CAAC,CAAC,CAAC;kBAC7C5B,WAAW,CAAC7F,IAAI,CAACC,WAAC,CAAC0D,SAAS,CAACmD,EAAE,CAAC,CAAC;kBACjCf,aAAa,CAACe,EAAE,CAAC5H,IAAI,EAAE,SAAS,CAAC;kBACjCO,IAAI,CAACyD,WAAW,CACdjD,WAAC,CAACC,mBAAmB,CACnBD,WAAC,CAACqB,oBAAoB,CACpB,GAAG,EACHrB,WAAC,CAAC0D,SAAS,CAACmD,EAAE,CAAC,EACf7G,WAAC,CAAC8G,YAAY,CAACQ,MAAM,CACvB,CACF,CACF,CAAC;gBACH,CAAC,MAAM;kBACL5H,WAAW,CAACK,IAAI,CAAC,SAAS,CAAC;kBAC3BJ,YAAY,CAACI,IAAI,CAACC,WAAC,CAAC8G,YAAY,CAACQ,MAAM,CAAC,CAAC;kBACzCzB,YAAY,CAAC9F,IAAI,CAACP,IAAI,CAAC;gBACzB;cACF,CAAC,MAAM,IAAIQ,WAAC,CAAC2G,qBAAqB,CAACW,MAAM,CAAC,EAAE;gBAC1C,MAAMT,EAAE,GAAGS,MAAM,CAACT,EAAE;gBACpB,IAAIA,EAAE,EAAE;kBACNpB,UAAU,CAAC1F,IAAI,CAACuH,MAAM,CAAC;kBACvB5H,WAAW,CAACK,IAAI,CAAC,SAAS,CAAC;kBAC3BJ,YAAY,CAACI,IAAI,CAACC,WAAC,CAAC0D,SAAS,CAACmD,EAAE,CAAC,CAAC;kBAClCf,aAAa,CAACe,EAAE,CAAC5H,IAAI,EAAE,SAAS,CAAC;gBACnC,CAAC,MAAM;kBACLS,WAAW,CAACK,IAAI,CAAC,SAAS,CAAC;kBAC3BJ,YAAY,CAACI,IAAI,CAACC,WAAC,CAAC8G,YAAY,CAACQ,MAAM,CAAC,CAAC;gBAC3C;gBACAzB,YAAY,CAAC9F,IAAI,CAACP,IAAI,CAAC;cACzB,CAAC,MAAM;gBAELA,IAAI,CAACyD,WAAW,CAACwD,eAAe,CAAC,SAAS,EAAEa,MAAM,CAAC,CAAC;cACtD;YACF,CAAC,MAAM,IAAI9H,IAAI,CAACiI,wBAAwB,CAAC,CAAC,EAAE;cAC1C,MAAMH,MAAM,GAAG9H,IAAI,CAACV,IAAI,CAACyI,WAAW;cAEpC,IAAID,MAAM,EAAE;gBACV9H,IAAI,CAACyD,WAAW,CAACqE,MAAM,CAAC;gBAExB,IAAItH,WAAC,CAAC0H,UAAU,CAACJ,MAAM,CAAC,EAAE;kBACxB,MAAMrI,IAAI,GAAGqI,MAAM,CAACT,EAAE,CAAC5H,IAAI;kBAC3B6G,aAAa,CAAC7G,IAAI,EAAEA,IAAI,CAAC;kBACzBwG,UAAU,CAAC1F,IAAI,CAACuH,MAAM,CAAC;kBACvB5H,WAAW,CAACK,IAAI,CAACd,IAAI,CAAC;kBACtBU,YAAY,CAACI,IAAI,CAACC,WAAC,CAAC0D,SAAS,CAAC4D,MAAM,CAACT,EAAE,CAAC,CAAC;kBACzChB,YAAY,CAAC9F,IAAI,CAACP,IAAI,CAAC;gBACzB,CAAC,MAAM,IAAIQ,WAAC,CAAC2H,OAAO,CAACL,MAAM,CAAC,EAAE;kBAC5B,MAAMrI,IAAI,GAAGqI,MAAM,CAACT,EAAE,CAAC5H,IAAI;kBAC3BS,WAAW,CAACK,IAAI,CAACd,IAAI,CAAC;kBACtBU,YAAY,CAACI,IAAI,CAACc,KAAK,CAAC2G,kBAAkB,CAAC,CAAC,CAAC;kBAC7C5B,WAAW,CAAC7F,IAAI,CAACC,WAAC,CAAC0D,SAAS,CAAC4D,MAAM,CAACT,EAAE,CAAC,CAAC;kBACxCrH,IAAI,CAACyD,WAAW,CACdjD,WAAC,CAACC,mBAAmB,CACnBD,WAAC,CAACqB,oBAAoB,CACpB,GAAG,EACHrB,WAAC,CAAC0D,SAAS,CAAC4D,MAAM,CAACT,EAAE,CAAC,EACtB7G,WAAC,CAAC8G,YAAY,CAACQ,MAAM,CACvB,CACF,CACF,CAAC;kBACDxB,aAAa,CAAC7G,IAAI,EAAEA,IAAI,CAAC;gBAC3B,CAAC,MAAM;kBACL,IAAIe,WAAC,CAAC+G,qBAAqB,CAACO,MAAM,CAAC,EAAE;oBAGnCA,MAAM,CAACN,IAAI,GAAG,KAAK;kBACrB;kBACA,KAAK,MAAM/H,IAAI,IAAIuD,MAAM,CAACC,IAAI,CAC5BzC,WAAC,CAAC0C,qBAAqB,CAAC4E,MAAM,CAChC,CAAC,EAAE;oBACDxB,aAAa,CAAC7G,IAAI,EAAEA,IAAI,CAAC;kBAC3B;gBACF;cACF,CAAC,MAAM;gBACL,MAAMkH,UAAU,GAAG3G,IAAI,CAACV,IAAI,CAACqH,UAAU;gBACvC,IAAIA,UAAU,YAAVA,UAAU,CAAErG,MAAM,EAAE;kBACtB,IAAIN,IAAI,CAACV,IAAI,CAACoH,MAAM,EAAE;oBACpBD,UAAU,CAACzG,IAAI,CAACV,IAAI,CAACoH,MAAM,CAAC/G,KAAK,EAAE,SAAS,EAAEgH,UAAU,CAAC;oBACzD3G,IAAI,CAAC2H,MAAM,CAAC,CAAC;kBACf,CAAC,MAAM;oBACL,MAAMS,KAAK,GAAG,EAAE;oBAEhB,KAAK,MAAMlD,SAAS,IAAIyB,UAAU,EAAE;sBAGlC,MAAM;wBAAE0B,KAAK;wBAAEC;sBAAS,CAAC,GAAGpD,SAAS;sBAErC,MAAMqD,OAAO,GAAGlH,KAAK,CAAC8B,UAAU,CAACkF,KAAK,CAAC5I,IAAI,CAAC;sBAC5C,MAAM6D,YAAY,GAAGjE,sBAAsB,CACzCiJ,QAAQ,EACR/I,gBACF,CAAC;sBAED,IACEgJ,OAAO,IACP/H,WAAC,CAAC2G,qBAAqB,CAACoB,OAAO,CAACvI,IAAI,CAACV,IAAI,CAAC,EAC1C;wBACAY,WAAW,CAACK,IAAI,CAAC+C,YAAY,CAAC;wBAC9BnD,YAAY,CAACI,IAAI,CAACC,WAAC,CAAC0D,SAAS,CAACmE,KAAK,CAAC,CAAC;sBACvC,CAAC,MAEI,IAAI,CAACE,OAAO,EAAE;wBACjBH,KAAK,CAAC7H,IAAI,CAAC0G,eAAe,CAAC3D,YAAY,EAAE+E,KAAK,CAAC,CAAC;sBAClD;sBACA/B,aAAa,CAAC+B,KAAK,CAAC5I,IAAI,EAAE6D,YAAY,CAAC;oBACzC;oBAEAtD,IAAI,CAACwI,mBAAmB,CAACJ,KAAK,CAAC;kBACjC;gBACF,CAAC,MAAM;kBACLpI,IAAI,CAAC2H,MAAM,CAAC,CAAC;gBACf;cACF;YACF;UACF;UAEA3B,OAAO,CAACa,OAAO,CAAC,UAAUF,UAAU,EAAE;YACpC,MAAM8B,UAAU,GAAG,EAAE;YACrB,MAAMC,MAAM,GAAGrH,KAAK,CAACC,WAAW,CAACqF,UAAU,CAACJ,GAAG,CAAC;YAEhD,KAAK,IAAIrB,SAAS,IAAIyB,UAAU,CAACI,OAAO,EAAE;cACxC,IAAIvG,WAAC,CAACmI,0BAA0B,CAACzD,SAAS,CAAC,EAAE;gBAC3CuD,UAAU,CAAClI,IAAI,CACbC,WAAC,CAACC,mBAAmB,CACnBD,WAAC,CAACqB,oBAAoB,CACpB,GAAG,EACHqD,SAAS,CAACmD,KAAK,EACf7H,WAAC,CAACU,UAAU,CAACwH,MAAM,CACrB,CACF,CACF,CAAC;cACH,CAAC,MAAM,IAAIlI,WAAC,CAACoI,wBAAwB,CAAC1D,SAAS,CAAC,EAAE;gBAChDA,SAAS,GAAG1E,WAAC,CAACqI,eAAe,CAC3B3D,SAAS,CAACmD,KAAK,EACf7H,WAAC,CAACU,UAAU,CAAC,SAAS,CACxB,CAAC;cACH;cAEA,IAAIV,WAAC,CAACsI,iBAAiB,CAAC5D,SAAS,CAAC,EAAE;gBAClC,MAAM;kBAAE6D;gBAAS,CAAC,GAAG7D,SAAS;gBAC9BuD,UAAU,CAAClI,IAAI,CACbC,WAAC,CAACC,mBAAmB,CACnBD,WAAC,CAACqB,oBAAoB,CACpB,GAAG,EACHqD,SAAS,CAACmD,KAAK,EACf7H,WAAC,CAACsB,gBAAgB,CAChBtB,WAAC,CAACU,UAAU,CAACwH,MAAM,CAAC,EACpBxD,SAAS,CAAC6D,QAAQ,EACHA,QAAQ,CAACvJ,IAAI,KAAK,eACnC,CACF,CACF,CACF,CAAC;cACH;YACF;YAEA,IAAImH,UAAU,CAACtD,OAAO,CAAC/C,MAAM,EAAE;cAC7B,MAAMJ,WAAW,GAAG,EAAE;cACtB,MAAMC,YAAY,GAAG,EAAE;cACvB,IAAI6I,aAAa,GAAG,KAAK;cAEzB,KAAK,MAAM1J,IAAI,IAAIqH,UAAU,CAACtD,OAAO,EAAE;gBACrC,IAAI7C,WAAC,CAACoH,sBAAsB,CAACtI,IAAI,CAAC,EAAE;kBAClC0J,aAAa,GAAG,IAAI;gBACtB,CAAC,MAAM,IAAIxI,WAAC,CAACyI,iBAAiB,CAAC3J,IAAI,CAAC,EAAE;kBACpC,MAAMgE,YAAY,GAAGjE,sBAAsB,CACzCC,IAAI,CAACgJ,QAAQ,EACb/I,gBACF,CAAC;kBACDW,WAAW,CAACK,IAAI,CAAC+C,YAAY,CAAC;kBAC9BnD,YAAY,CAACI,IAAI,CACfC,WAAC,CAACsB,gBAAgB,CAChBtB,WAAC,CAACU,UAAU,CAACwH,MAAM,CAAC,EACpBpJ,IAAI,CAAC+I,KAAK,EACV7H,WAAC,CAAC0I,eAAe,CAAC5J,IAAI,CAAC+I,KAAK,CAC9B,CACF,CAAC;gBACH,CAAC,MAAM,CAEP;cACF;cAEAI,UAAU,CAAClI,IAAI,CACb,GAAGR,mBAAmB,CACpBC,IAAI,EACJQ,WAAC,CAACU,UAAU,CAACjB,WAAW,CAAC,EACzBC,WAAW,EACXC,YAAY,EACZ6I,aAAa,GAAGxI,WAAC,CAACU,UAAU,CAACwH,MAAM,CAAC,GAAG,IAAI,EAC3CnJ,gBACF,CACF,CAAC;YACH;YAEA4G,OAAO,CAAC5F,IAAI,CAACC,WAAC,CAACG,aAAa,CAACgG,UAAU,CAACJ,GAAG,CAAC,CAAC;YAC7CL,OAAO,CAAC3F,IAAI,CACVC,WAAC,CAAC2I,kBAAkB,CAClB,IAAI,EACJ,CAAC3I,WAAC,CAACU,UAAU,CAACwH,MAAM,CAAC,CAAC,EACtBlI,WAAC,CAAC4I,cAAc,CAACX,UAAU,CAC7B,CACF,CAAC;UACH,CAAC,CAAC;UAEF,IAAIY,UAAU,GAAG,IAAAC,qCAAa,EAAC,IAAI,CAAChF,IAAI,CAACiF,IAAI,EAAErH,OAAO,CAAC;UAEvD,IAAImH,UAAU,EAAEA,UAAU,GAAG7I,WAAC,CAACG,aAAa,CAAC0I,UAAU,CAAC;UAExD,IAAAG,6BAAc,EAACxJ,IAAI,EAAE,CAACqH,EAAE,EAAE5H,IAAI,EAAEgK,OAAO,KAAK;YAC1CrD,WAAW,CAAC7F,IAAI,CAAC8G,EAAE,CAAC;YACpB,IAAI,CAACoC,OAAO,IAAIhK,IAAI,IAAIqG,SAAS,EAAE;cACjC,KAAK,MAAMwC,QAAQ,IAAIxC,SAAS,CAACrG,IAAI,CAAC,EAAE;gBACtCS,WAAW,CAACK,IAAI,CAAC+H,QAAQ,CAAC;gBAC1BnI,YAAY,CAACI,IAAI,CAACc,KAAK,CAAC2G,kBAAkB,CAAC,CAAC,CAAC;cAC/C;YACF;UACF,CAAC,CAAC;UAEF,IAAI5B,WAAW,CAAC9F,MAAM,EAAE;YACtB2F,UAAU,CAACyD,OAAO,CAChBlJ,WAAC,CAACe,mBAAmB,CACnB,KAAK,EACL6E,WAAW,CAACuD,GAAG,CAACtC,EAAE,IAAI7G,WAAC,CAACgB,kBAAkB,CAAC6F,EAAE,CAAC,CAChD,CACF,CAAC;UACH;UAEA,IAAInH,WAAW,CAACI,MAAM,EAAE;YACtB2F,UAAU,CAAC1F,IAAI,CACb,GAAGR,mBAAmB,CACpBC,IAAI,EACJQ,WAAC,CAACU,UAAU,CAACjB,WAAW,CAAC,EACzBC,WAAW,EACXC,YAAY,EACZ,IAAI,EACJZ,gBACF,CACF,CAAC;UACH;UAEAS,IAAI,CAAC4J,QAAQ,CAACpH,mBAAmB,EAAE;YACjCa,OAAO,EAAEyC,SAAS;YAClBvC,SAAS,EAAE0D,eAAe;YAC1B5F;UACF,CAAC,CAAC;UAEF,KAAK,MAAMrB,IAAI,IAAIqG,YAAY,EAAE;YAC/BrG,IAAI,CAAC2H,MAAM,CAAC,CAAC;UACf;UAEA,IAAIkC,MAAM,GAAG,KAAK;UAClB7J,IAAI,CAAC4J,QAAQ,CAAC;YACZE,eAAeA,CAAC9J,IAAI,EAAE;cACpB6J,MAAM,GAAG,IAAI;cACb7J,IAAI,CAAC+J,IAAI,CAAC,CAAC;YACb,CAAC;YACDC,QAAQA,CAAChK,IAAI,EAAE;cACbA,IAAI,CAACiK,IAAI,CAAC,CAAC;YACb,CAAC;YAEDC,OAAO,EAAE;UACX,CAAC,CAAC;UAEFlK,IAAI,CAACV,IAAI,CAAC4H,IAAI,GAAG,CACfnI,aAAa,CAAC;YACZoL,eAAe,EAAE3J,WAAC,CAACsB,gBAAgB,CACjCtB,WAAC,CAACU,UAAU,CAACkB,YAAY,CAAC,EAC1B5B,WAAC,CAACU,UAAU,CAAC,UAAU,CACzB,CAAC;YACDkJ,WAAW,EAAEnE,UAAU;YACvBoE,WAAW,EAAEhB,UAAU;YACvBiB,OAAO,EAAE9J,WAAC,CAAC+J,eAAe,CAACrE,OAAO,CAAC;YACnCsE,OAAO,EAAEhK,WAAC,CAAC2I,kBAAkB,CAC3B,IAAI,EACJ,EAAE,EACF3I,WAAC,CAAC4I,cAAc,CAACpJ,IAAI,CAACV,IAAI,CAAC4H,IAAI,CAAC,EAChC,KAAK,EACL2C,MACF,CAAC;YACDY,OAAO,EAAEjK,WAAC,CAAC+J,eAAe,CAACpE,OAAO,CAAC;YACnCuE,iBAAiB,EAAElK,WAAC,CAACU,UAAU,CAACjB,WAAW,CAAC;YAC5C0K,kBAAkB,EAAEnK,WAAC,CAACU,UAAU,CAACiE,YAAY;UAC/C,CAAC,CAAC,CACH;QACH;MACF;IACF;EACF,CAAC;AACH,CAAC,CAAC;AAAA9B,OAAA,CAAAuH,OAAA,GAAA7I,QAAA"}