{"version": 3, "names": ["_setPrototypeOf", "require", "_inherits", "_wrapRegExp", "exports", "default", "re", "groups", "BabelRegExp", "undefined", "_super", "RegExp", "prototype", "_groups", "WeakMap", "flags", "_this", "set", "get", "setPrototypeOf", "inherits", "exec", "str", "result", "call", "buildGroups", "indices", "Symbol", "replace", "substitution", "_", "name", "group", "Array", "isArray", "join", "args", "arguments", "length", "slice", "push", "apply", "g", "Object", "keys", "reduce", "i", "k", "create"], "sources": ["../../src/helpers/wrapRegExp.js"], "sourcesContent": ["/* @minVersion 7.19.0 */\n\nimport setPrototypeOf from \"setPrototypeOf\";\nimport inherits from \"inherits\";\n\nexport default function _wrapRegExp() {\n  _wrapRegExp = function (re, groups) {\n    return new BabelRegExp(re, undefined, groups);\n  };\n\n  var _super = RegExp.prototype;\n  var _groups = new WeakMap();\n\n  function BabelRegExp(re, flags, groups) {\n    var _this = new RegExp(re, flags);\n    // if the regex is recreated with 'g' flag\n    _groups.set(_this, groups || _groups.get(re));\n    return setPrototypeOf(_this, BabelRegExp.prototype);\n  }\n  inherits(BabelRegExp, RegExp);\n\n  BabelRegExp.prototype.exec = function (str) {\n    var result = _super.exec.call(this, str);\n    if (result) {\n      result.groups = buildGroups(result, this);\n      var indices = result.indices;\n      if (indices) indices.groups = buildGroups(indices, this);\n    }\n    return result;\n  };\n  BabelRegExp.prototype[Symbol.replace] = function (str, substitution) {\n    if (typeof substitution === \"string\") {\n      var groups = _groups.get(this);\n      return _super[Symbol.replace].call(\n        this,\n        str,\n        substitution.replace(/\\$<([^>]+)>/g, function (_, name) {\n          var group = groups[name];\n          return \"$\" + (Array.isArray(group) ? group.join(\"$\") : group);\n        })\n      );\n    } else if (typeof substitution === \"function\") {\n      var _this = this;\n      return _super[Symbol.replace].call(this, str, function () {\n        var args = arguments;\n        // Modern engines already pass result.groups returned by exec() as the last arg.\n        if (typeof args[args.length - 1] !== \"object\") {\n          args = [].slice.call(args);\n          args.push(buildGroups(args, _this));\n        }\n        return substitution.apply(this, args);\n      });\n    } else {\n      return _super[Symbol.replace].call(this, str, substitution);\n    }\n  };\n\n  function buildGroups(result, re) {\n    // NOTE: This function should return undefined if there are no groups,\n    // but in that case Babel doesn't add the wrapper anyway.\n\n    var g = _groups.get(re);\n    return Object.keys(g).reduce(function (groups, name) {\n      var i = g[name];\n      if (typeof i === \"number\") groups[name] = result[i];\n      else {\n        // i is an array of indexes\n        var k = 0;\n        // if no group matched, we stop at k = i.length - 1 and then\n        // we store result[i[i.length - 1]] which is undefined.\n        while (result[i[k]] === undefined && k + 1 < i.length) k++;\n        groups[name] = result[i[k]];\n      }\n      return groups;\n    }, Object.create(null));\n  }\n\n  return _wrapRegExp.apply(this, arguments);\n}\n"], "mappings": ";;;;;;AAEA,IAAAA,eAAA,GAAAC,OAAA;AACA,IAAAC,SAAA,GAAAD,OAAA;AAEe,SAASE,WAAWA,CAAA,EAAG;EACpCC,OAAA,CAAAC,OAAA,GAAAF,WAAW,GAAG,SAAAA,CAAUG,EAAE,EAAEC,MAAM,EAAE;IAClC,OAAO,IAAIC,WAAW,CAACF,EAAE,EAAEG,SAAS,EAAEF,MAAM,CAAC;EAC/C,CAAC;EAED,IAAIG,MAAM,GAAGC,MAAM,CAACC,SAAS;EAC7B,IAAIC,OAAO,GAAG,IAAIC,OAAO,CAAC,CAAC;EAE3B,SAASN,WAAWA,CAACF,EAAE,EAAES,KAAK,EAAER,MAAM,EAAE;IACtC,IAAIS,KAAK,GAAG,IAAIL,MAAM,CAACL,EAAE,EAAES,KAAK,CAAC;IAEjCF,OAAO,CAACI,GAAG,CAACD,KAAK,EAAET,MAAM,IAAIM,OAAO,CAACK,GAAG,CAACZ,EAAE,CAAC,CAAC;IAC7C,OAAOa,eAAc,CAACH,KAAK,EAAER,WAAW,CAACI,SAAS,CAAC;EACrD;EACAQ,SAAQ,CAACZ,WAAW,EAAEG,MAAM,CAAC;EAE7BH,WAAW,CAACI,SAAS,CAACS,IAAI,GAAG,UAAUC,GAAG,EAAE;IAC1C,IAAIC,MAAM,GAAGb,MAAM,CAACW,IAAI,CAACG,IAAI,CAAC,IAAI,EAAEF,GAAG,CAAC;IACxC,IAAIC,MAAM,EAAE;MACVA,MAAM,CAAChB,MAAM,GAAGkB,WAAW,CAACF,MAAM,EAAE,IAAI,CAAC;MACzC,IAAIG,OAAO,GAAGH,MAAM,CAACG,OAAO;MAC5B,IAAIA,OAAO,EAAEA,OAAO,CAACnB,MAAM,GAAGkB,WAAW,CAACC,OAAO,EAAE,IAAI,CAAC;IAC1D;IACA,OAAOH,MAAM;EACf,CAAC;EACDf,WAAW,CAACI,SAAS,CAACe,MAAM,CAACC,OAAO,CAAC,GAAG,UAAUN,GAAG,EAAEO,YAAY,EAAE;IACnE,IAAI,OAAOA,YAAY,KAAK,QAAQ,EAAE;MACpC,IAAItB,MAAM,GAAGM,OAAO,CAACK,GAAG,CAAC,IAAI,CAAC;MAC9B,OAAOR,MAAM,CAACiB,MAAM,CAACC,OAAO,CAAC,CAACJ,IAAI,CAChC,IAAI,EACJF,GAAG,EACHO,YAAY,CAACD,OAAO,CAAC,cAAc,EAAE,UAAUE,CAAC,EAAEC,IAAI,EAAE;QACtD,IAAIC,KAAK,GAAGzB,MAAM,CAACwB,IAAI,CAAC;QACxB,OAAO,GAAG,IAAIE,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC,GAAGA,KAAK,CAACG,IAAI,CAAC,GAAG,CAAC,GAAGH,KAAK,CAAC;MAC/D,CAAC,CACH,CAAC;IACH,CAAC,MAAM,IAAI,OAAOH,YAAY,KAAK,UAAU,EAAE;MAC7C,IAAIb,KAAK,GAAG,IAAI;MAChB,OAAON,MAAM,CAACiB,MAAM,CAACC,OAAO,CAAC,CAACJ,IAAI,CAAC,IAAI,EAAEF,GAAG,EAAE,YAAY;QACxD,IAAIc,IAAI,GAAGC,SAAS;QAEpB,IAAI,OAAOD,IAAI,CAACA,IAAI,CAACE,MAAM,GAAG,CAAC,CAAC,KAAK,QAAQ,EAAE;UAC7CF,IAAI,GAAG,EAAE,CAACG,KAAK,CAACf,IAAI,CAACY,IAAI,CAAC;UAC1BA,IAAI,CAACI,IAAI,CAACf,WAAW,CAACW,IAAI,EAAEpB,KAAK,CAAC,CAAC;QACrC;QACA,OAAOa,YAAY,CAACY,KAAK,CAAC,IAAI,EAAEL,IAAI,CAAC;MACvC,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,OAAO1B,MAAM,CAACiB,MAAM,CAACC,OAAO,CAAC,CAACJ,IAAI,CAAC,IAAI,EAAEF,GAAG,EAAEO,YAAY,CAAC;IAC7D;EACF,CAAC;EAED,SAASJ,WAAWA,CAACF,MAAM,EAAEjB,EAAE,EAAE;IAI/B,IAAIoC,CAAC,GAAG7B,OAAO,CAACK,GAAG,CAACZ,EAAE,CAAC;IACvB,OAAOqC,MAAM,CAACC,IAAI,CAACF,CAAC,CAAC,CAACG,MAAM,CAAC,UAAUtC,MAAM,EAAEwB,IAAI,EAAE;MACnD,IAAIe,CAAC,GAAGJ,CAAC,CAACX,IAAI,CAAC;MACf,IAAI,OAAOe,CAAC,KAAK,QAAQ,EAAEvC,MAAM,CAACwB,IAAI,CAAC,GAAGR,MAAM,CAACuB,CAAC,CAAC,CAAC,KAC/C;QAEH,IAAIC,CAAC,GAAG,CAAC;QAGT,OAAOxB,MAAM,CAACuB,CAAC,CAACC,CAAC,CAAC,CAAC,KAAKtC,SAAS,IAAIsC,CAAC,GAAG,CAAC,GAAGD,CAAC,CAACR,MAAM,EAAES,CAAC,EAAE;QAC1DxC,MAAM,CAACwB,IAAI,CAAC,GAAGR,MAAM,CAACuB,CAAC,CAACC,CAAC,CAAC,CAAC;MAC7B;MACA,OAAOxC,MAAM;IACf,CAAC,EAAEoC,MAAM,CAACK,MAAM,CAAC,IAAI,CAAC,CAAC;EACzB;EAEA,OAAO7C,WAAW,CAACsC,KAAK,CAAC,IAAI,EAAEJ,SAAS,CAAC;AAC3C"}