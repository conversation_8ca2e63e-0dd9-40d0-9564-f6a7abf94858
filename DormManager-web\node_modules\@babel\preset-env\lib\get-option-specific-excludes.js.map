{"version": 3, "names": ["defaultExcludesForLooseMode", "_default", "loose"], "sources": ["../src/get-option-specific-excludes.ts"], "sourcesContent": ["const defaultExcludesForLooseMode = [\"transform-typeof-symbol\"];\n\nexport default function ({ loose }: { loose: boolean }): null | string[] {\n  return loose ? defaultExcludesForLooseMode : null;\n}\n"], "mappings": ";;;;;;AAAA,MAAMA,2BAA2B,GAAG,CAAC,yBAAyB,CAAC;AAEhD,SAAAC,SAAU;EAAEC;AAA0B,CAAC,EAAmB;EACvE,OAAOA,KAAK,GAAGF,2BAA2B,GAAG,IAAI;AACnD"}