{"remainingRequest": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\B7839DormManager\\DormManager-web\\src\\components\\LeftMenu.vue?vue&type=template&id=edc10994&scoped=true", "dependencies": [{"path": "I:\\product4\\B7839DormManager\\DormManager-web\\src\\components\\LeftMenu.vue", "mtime": 1749046536848}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\babel.config.js", "mtime": 1749034452000}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749043011899}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749043012919}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749043013784}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749043011899}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749043013130}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["class", "_createElementVNode", "href", "_createElementBlock", "_hoisted_1", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_hoisted_5", "_hoisted_6", "_hoisted_7", "_hoisted_8", "_createVNode", "_component_router_link", "to", "_hoisted_13", "_hoisted_14", "_hoisted_15", "_hoisted_18", "_hoisted_19", "_hoisted_20", "_hoisted_23", "_hoisted_24", "_hoisted_25", "_hoisted_28", "_hoisted_29", "_hoisted_30", "_hoisted_33", "_hoisted_34", "_hoisted_35", "_hoisted_38", "_hoisted_39", "_hoisted_40", "_hoisted_45", "_hoisted_46", "_hoisted_47", "_hoisted_51", "_hoisted_52", "_hoisted_53", "_hoisted_57", "_hoisted_58", "_hoisted_59", "_hoisted_63", "_hoisted_64", "_hoisted_65", "_hoisted_69", "_hoisted_70", "_hoisted_71", "_hoisted_75", "_hoisted_76", "_hoisted_77", "$data", "role", "_hoisted_79", "_hoisted_80", "_hoisted_81", "_hoisted_82", "_hoisted_83", "_hoisted_85", "_hoisted_86", "_hoisted_87", "_hoisted_89", "_hoisted_90", "_hoisted_91", "_hoisted_94", "_hoisted_95", "_hoisted_96", "_hoisted_99", "_hoisted_100", "_hoisted_101", "_hoisted_104", "_hoisted_105", "_hoisted_106", "_hoisted_108", "_hoisted_109", "_hoisted_110", "_hoisted_112", "_hoisted_113", "_hoisted_114", "_hoisted_117", "_hoisted_118", "_hoisted_119", "_hoisted_120", "_hoisted_121", "_hoisted_125", "_hoisted_126", "_hoisted_127", "_hoisted_131", "_hoisted_132", "_hoisted_133", "_hoisted_137", "_hoisted_138", "_hoisted_139", "_hoisted_143", "_hoisted_144", "_hoisted_145", "_hoisted_149", "_hoisted_150", "_hoisted_151", "_hoisted_155", "_hoisted_156", "_hoisted_157", "_hoisted_160", "_hoisted_161", "_hoisted_162", "_hoisted_165", "_hoisted_166", "_hoisted_167", "_hoisted_171", "_hoisted_172", "_hoisted_173", "_hoisted_177", "_hoisted_178", "_hoisted_179", "_hoisted_183", "_hoisted_184", "_hoisted_185", "_hoisted_189", "_hoisted_190", "_hoisted_191", "_hoisted_194", "_hoisted_195", "_hoisted_196", "_hoisted_201", "_hoisted_202", "_hoisted_203", "_hoisted_204", "_hoisted_205", "_hoisted_207", "_hoisted_208", "_hoisted_209", "_hoisted_211", "_hoisted_212", "_hoisted_213", "_hoisted_216", "_hoisted_217", "_hoisted_218", "_hoisted_222", "_hoisted_223", "_hoisted_224", "_hoisted_227", "_hoisted_228", "_hoisted_229", "_hoisted_232", "_hoisted_233", "_hoisted_234"], "sources": ["I:\\product4\\B7839DormManager\\DormManager-web\\src\\components\\LeftMenu.vue"], "sourcesContent": ["<template>\r\n <nav class=\"pcoded-navbar\">\r\n        <div class=\"navbar-wrapper\">\r\n            <div class=\"navbar-brand header-logo\">\r\n                <a href=\"/main\" class=\"b-brand\">\r\n                    <div class=\"b-bg\">\r\n                        <i class=\"feather icon-trending-up\"></i>\r\n                    </div>\r\n                    <span class=\"b-title\">宿舍管理系统</span>\r\n                </a>\r\n\r\n            </div>\r\n            <div class=\"navbar-content scroll-div\">\r\n                <ul class=\"nav pcoded-inner-navbar\" v-show=\"role == '管理员'\">\r\n                 \r\n                   \r\n                      <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿舍管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                                 <li> <router-link to=\"/dormbuildingAdd\">添加宿舍楼</router-link></li>\r\n  <li> <router-link to=\"/dormbuildingManage\">管理宿舍楼</router-link></li>\r\n                             <li> <router-link to=\"/dormitoryAdd\">添加宿舍</router-link></li>\r\n  <li> <router-link to=\"/dormitoryManage\">管理宿舍</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                         <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">学生管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/studentAdd\">添加学生</router-link></li>\r\n  <li> <router-link to=\"/studentManage\">管理学生</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                           <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿管阿姨管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/hostessAdd\">添加宿管阿姨</router-link></li>\r\n  <li> <router-link to=\"/hostessManage\">管理宿管阿姨</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                         <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">维修员管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/repairmenAdd\">添加维修员</router-link></li>\r\n  <li> <router-link to=\"/repairmenManage\">管理维修员</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n\r\n                         <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">公告管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/systemnoticesAdd\">添加公告</router-link></li>\r\n  <li> <router-link to=\"/systemnoticesManage\">管理公告</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                          <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿舍更换管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/dormitorychangeAdd\">添加宿舍更换</router-link></li>\r\n  <li> <router-link to=\"/dormitorychangeManage\">管理宿舍更换</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                     \r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">报修管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                         <li> <router-link to=\"/repairtypeAdd\">添加报修类型</router-link></li>\r\n  <li> <router-link to=\"/repairtypeManage\">管理报修类型</router-link></li>\r\n  <li> <router-link to=\"/repairordersManage\">管理报修</router-link></li>\r\n  <li> <router-link to=\"/repairordersManage2\">报修列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                    \r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">水电费管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/waterelectricityfeeAdd\">添加水电费</router-link></li>\r\n  <li> <router-link to=\"/waterelectricityfeeManage\">管理水电费</router-link></li>\r\n  <li> <router-link to=\"/waterelectricityfeeManage2\">水电费列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n    \r\n\r\n  \r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">离校登记管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/leaveschoolAdd\">添加离校登记</router-link></li>\r\n  <li> <router-link to=\"/leaveschoolManage\">管理离校登记</router-link></li>\r\n  <li> <router-link to=\"/leaveschoolManage2\">离校登记列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                     \r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">返校登记管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/registerinfoAdd\">添加返校登记</router-link></li>\r\n  <li> <router-link to=\"/registerinfoManage\">管理返校登记</router-link></li>\r\n  <li> <router-link to=\"/registerinfoManage2\">返校登记列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿舍评分管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/dormitoryscoreAdd\">添加宿舍评分</router-link></li>\r\n  <li> <router-link to=\"/dormitoryscoreManage\">管理宿舍评分</router-link></li>\r\n  <li> <router-link to=\"/dormitoryscoreManage2\">宿舍评分列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n \r\n    \r\n\r\n     \r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">统计报表</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/total1\">报修类型统计</router-link></li>\r\n  <li> <router-link to=\"/total2\">维修员维修统计</router-link></li>\r\n  <li> <router-link to=\"/total3\">宿舍评分统计</router-link></li>\r\n\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                      <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">系统管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                           \r\n  <li> <router-link to=\"/password\">修改密码</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n          \r\n                </ul>\r\n\r\n                      <ul class=\"nav pcoded-inner-navbar\" v-show=\"role == '学生'\">\r\n                    <li class=\"nav-item pcoded-menu-caption\">\r\n                        <label>功能菜单</label>\r\n                    </li>\r\n                         <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">公告管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                         \r\n  <li> <router-link to=\"/systemnoticesManage2\">公告列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                           <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿舍更换管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                       <li> <router-link to=\"/mydormitory\">我的宿舍</router-link></li> \r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">离校登记管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/leaveschoolAdd\">添加离校登记</router-link></li>\r\n  <li> <router-link to=\"/leaveschoolManage\">我的离校登记</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                        <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">返校登记管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/registerinfoAdd\">添加返校登记</router-link></li>\r\n  <li> <router-link to=\"/registerinfoManage\">我的返校登记</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">报修管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/repairordersAdd\">在线报修</router-link></li>\r\n  <li> <router-link to=\"/repairordersManage\">我的报修</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                        <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">水电费管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n          \r\n  <li> <router-link to=\"/waterelectricityfeeManage2\">水电费</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n     \r\n     \r\n\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿舍评分管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                    \r\n  <li> <router-link to=\"/dormitoryscoreManage2\">宿舍评分</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n   \r\n \r\n    \r\n   \r\n\r\n                          <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">个人中心</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/studentInfo\">修改个人信息</router-link></li>\r\n  <li> <router-link to=\"/password\">修改密码</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n          \r\n                </ul>\r\n\r\n\r\n                      <ul class=\"nav pcoded-inner-navbar\" v-show=\"role == '维修员'\">\r\n                    <li class=\"nav-item pcoded-menu-caption\">\r\n                        <label>功能菜单</label>\r\n                    </li>\r\n                         <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">公告管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/systemnoticesAdd\">添加公告</router-link></li>\r\n  <li> <router-link to=\"/systemnoticesManage\">管理公告</router-link></li>\r\n  <li> <router-link to=\"/systemnoticesManage2\">公告列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">维修员管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/repairmenAdd\">添加维修员</router-link></li>\r\n  <li> <router-link to=\"/repairmenManage\">管理维修员</router-link></li>\r\n  <li> <router-link to=\"/repairmenInfo\">修改个人信息</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">离校登记管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/leaveschoolAdd\">添加离校登记</router-link></li>\r\n  <li> <router-link to=\"/leaveschoolManage\">管理离校登记</router-link></li>\r\n  <li> <router-link to=\"/leaveschoolManage2\">离校登记列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿舍评分管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/dormitoryscoreAdd\">添加宿舍评分</router-link></li>\r\n  <li> <router-link to=\"/dormitoryscoreManage\">管理宿舍评分</router-link></li>\r\n  <li> <router-link to=\"/dormitoryscoreManage2\">宿舍评分列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿舍更换管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/dormitorychangeAdd\">添加宿舍更换</router-link></li>\r\n  <li> <router-link to=\"/dormitorychangeManage\">管理宿舍更换</router-link></li>\r\n  <li> <router-link to=\"/dormitorychangeManage2\">宿舍更换列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">水电费管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/waterelectricityfeeAdd\">添加水电费</router-link></li>\r\n  <li> <router-link to=\"/waterelectricityfeeManage\">管理水电费</router-link></li>\r\n  <li> <router-link to=\"/waterelectricityfeeManage2\">水电费列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">报修类型管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/repairtypeAdd\">添加报修类型</router-link></li>\r\n  <li> <router-link to=\"/repairtypeManage\">管理报修类型</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿舍楼管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/dormbuildingAdd\">添加宿舍楼</router-link></li>\r\n  <li> <router-link to=\"/dormbuildingManage\">管理宿舍楼</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">学生管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/studentAdd\">添加学生</router-link></li>\r\n  <li> <router-link to=\"/studentManage\">管理学生</router-link></li>\r\n  <li> <router-link to=\"/studentInfo\">修改个人信息</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">报修管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/repairordersAdd\">添加报修</router-link></li>\r\n  <li> <router-link to=\"/repairordersManage\">管理报修</router-link></li>\r\n  <li> <router-link to=\"/repairordersManage2\">报修列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">返校登记管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/registerinfoAdd\">添加返校登记</router-link></li>\r\n  <li> <router-link to=\"/registerinfoManage\">管理返校登记</router-link></li>\r\n  <li> <router-link to=\"/registerinfoManage2\">返校登记列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿管阿姨管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/hostessAdd\">添加宿管阿姨</router-link></li>\r\n  <li> <router-link to=\"/hostessManage\">管理宿管阿姨</router-link></li>\r\n  <li> <router-link to=\"/hostessInfo\">修改个人信息</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿舍管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/dormitoryAdd\">添加宿舍</router-link></li>\r\n  <li> <router-link to=\"/dormitoryManage\">管理宿舍</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">图表管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/total1\">图表1</router-link></li>\r\n  <li> <router-link to=\"/total2\">图表2</router-link></li>\r\n  <li> <router-link to=\"/total3\">图表3</router-link></li>\r\n  <li> <router-link to=\"/total4\">图表4</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n          \r\n                </ul>\r\n\r\n                      <ul class=\"nav pcoded-inner-navbar\" v-show=\"role == '宿管阿姨'\">\r\n                    <li class=\"nav-item pcoded-menu-caption\">\r\n                        <label>功能菜单</label>\r\n                    </li>\r\n                         <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">公告管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                        \r\n  <li> <router-link to=\"/systemnoticesManage2\">公告列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                           <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿舍更换管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                 \r\n  <li> <router-link to=\"/dormitorychangeManage\">管理宿舍更换</router-link></li>\r\n\r\n\r\n                        </ul>\r\n                    </li>\r\n   \r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">离校登记管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                         \r\n  <li> <router-link to=\"/leaveschoolManage\">管理离校登记</router-link></li>\r\n  <li> <router-link to=\"/leaveschoolManage2\">离校登记列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                          <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">返校登记管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/registerinfoAdd\">添加返校登记</router-link></li>\r\n  <li> <router-link to=\"/registerinfoManage\">管理返校登记</router-link></li>\r\n  <li> <router-link to=\"/registerinfoManage2\">返校登记列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                          <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">水电费管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/waterelectricityfeeAdd\">添加水电费</router-link></li>\r\n  <li> <router-link to=\"/waterelectricityfeeManage\">管理水电费</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿舍评分管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/dormitoryscoreAdd\">添加宿舍评分</router-link></li>\r\n  <li> <router-link to=\"/dormitoryscoreManage\">管理宿舍评分</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n \r\n \r\n     \r\n                                              <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">个人中心</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                   <li> <router-link to=\"/hostessInfo\">修改个人信息</router-link></li>\r\n  <li> <router-link to=\"/password\">修改密码</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n\r\n          \r\n                </ul>\r\n\r\n\r\n            </div>\r\n        </div>\r\n    </nav>\r\n    \r\n\r\n</template>\r\n\r\n\r\n<script>\r\nimport $ from 'jquery';\r\n\r\nexport default {\r\n  name: \"LeftMenu\",\r\n  data() {\r\n    return {\r\n      userLname: \"\",\r\n      role: \"\",\r\n      activeMenu: null, // 用于跟踪当前激活的菜单\r\n    };\r\n  },\r\n  watch: {\r\n    $route(to, from) {\r\n      this.activeMenu = to.name;\r\n      this.$nextTick(() => {\r\n        this.initializeMenu();\r\n      });\r\n    }\r\n  },\r\n  mounted() {\r\n    this.userLname = sessionStorage.getItem(\"userLname\");\r\n    this.role = sessionStorage.getItem(\"role\");\r\n    this.$nextTick(() => {\r\n      this.initializeMenu();\r\n    });\r\n  },\r\n  methods: {\r\n    initializeMenu() {\r\n      $('.nav-item.pcoded-hasmenu > a').off('click').on('click', function(e) {\r\n        e.preventDefault();\r\n        const $parent = $(this).parent();\r\n\r\n        if ($parent.hasClass('pcoded-trigger')) {\r\n          $parent.removeClass('pcoded-trigger');\r\n          $parent.children('ul').slideUp();\r\n        } else {\r\n          $('.nav-item.pcoded-hasmenu').removeClass('pcoded-trigger');\r\n          $('.nav-item.pcoded-hasmenu > ul').slideUp();\r\n          $parent.addClass('pcoded-trigger');\r\n          $parent.children('ul').slideDown();\r\n        }\r\n      });\r\n\r\n      // 初始化：根据当前路由展开对应的菜单\r\n      const currentPath = this.$route.path;\r\n      $('.pcoded-submenu a').each(function() {\r\n        if ($(this).attr('href') === currentPath) {\r\n          $(this).parents('.nav-item.pcoded-hasmenu').addClass('pcoded-trigger');\r\n          $(this).parents('ul.pcoded-submenu').show();\r\n        }\r\n      });\r\n    },\r\n\r\n    exit: function () {\r\n      var _this = this;\r\n      this.$confirm(\"确认退出吗?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n          .then(() => {\r\n            sessionStorage.removeItem(\"userLname\");\r\n            sessionStorage.removeItem(\"role\");\r\n            _this.$router.push(\"/\");\r\n          })\r\n          .catch(() => { });\r\n    },\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.example-showcase .el-dropdown-link {\r\n  cursor: pointer;\r\n  color:green;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.pcoded-submenu a{\r\n  text-decoration: none;\r\n  font-size: 14px;\r\n}\r\n\r\n/*加点击效果*/\r\n.pcoded-submenu a:hover{\r\n  color: #fff;\r\n  color: #ff6600;\r\n}\r\n</style>\r\n\r\n"], "mappings": ";;;EACMA,KAAK,EAAC;AAAe;;EACdA,KAAK,EAAC;AAAgB;;;EAUlBA,KAAK,EAAC;AAA2B;;EAC9BA,KAAK,EAAC;AAAyB;;EAGxBA,KAAK,EAAC;AAAyB;gEAClCC,mBAAA,CAA6I;EAA1IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAsC;EAAhCD,KAAK,EAAC;AAAc,GAAC,MAAI,E;;EAC9HA,KAAK,EAAC;AAAgB;iDACuB,OAAK;kDACjC,OAAK;kDACgB,MAAI;kDAC5B,MAAI;;EAKhBA,KAAK,EAAC;AAAyB;iEACrCC,mBAAA,CAA6I;EAA1IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAsC;EAAhCD,KAAK,EAAC;AAAc,GAAC,MAAI,E;;EAC9HA,KAAK,EAAC;AAAgB;kDACc,MAAI;kDAC5B,MAAI;;EAKZA,KAAK,EAAC;AAAyB;iEACvCC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;kDACc,QAAM;kDAC9B,QAAM;;EAKhBA,KAAK,EAAC;AAAyB;iEACrCC,mBAAA,CAA8I;EAA3IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAuC;EAAjCD,KAAK,EAAC;AAAc,GAAC,OAAK,E;;EAC/HA,KAAK,EAAC;AAAgB;kDACgB,OAAK;kDAC7B,OAAK;;EAMjBA,KAAK,EAAC;AAAyB;iEACrCC,mBAAA,CAA6I;EAA1IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAsC;EAAhCD,KAAK,EAAC;AAAc,GAAC,MAAI,E;;EAC9HA,KAAK,EAAC;AAAgB;kDACoB,MAAI;kDAC5B,MAAI;;EAKnBA,KAAK,EAAC;AAAyB;iEACtCC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;kDACsB,QAAM;kDAC9B,QAAM;;EAM1CA,KAAK,EAAC;AAAyB;iEACnBC,mBAAA,CAA6I;EAA1IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAsC;EAAhCD,KAAK,EAAC;AAAc,GAAC,MAAI,E;;EAC9HA,KAAK,EAAC;AAAgB;kDACa,QAAM;kDAC1B,QAAM;kDACJ,MAAI;kDACH,MAAI;;EAMtCA,KAAK,EAAC;AAAyB;iEACnBC,mBAAA,CAA8I;EAA3IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAuC;EAAjCD,KAAK,EAAC;AAAc,GAAC,OAAK,E;;EAC/HA,KAAK,EAAC;AAAgB;kDAC0B,OAAK;kDAC7B,OAAK;kDACJ,OAAK;;EAO9CA,KAAK,EAAC;AAAyB;iEACnBC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;kDACkB,QAAM;kDAC9B,QAAM;kDACL,QAAM;;EAMvCA,KAAK,EAAC;AAAyB;iEACnBC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;kDACmB,QAAM;kDAC9B,QAAM;kDACL,QAAM;;EAMxCA,KAAK,EAAC;AAAyB;iEACnBC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;kDACqB,QAAM;kDAC9B,QAAM;kDACL,QAAM;;EAQ1CA,KAAK,EAAC;AAAyB;iEACnBC,mBAAA,CAA6I;EAA1IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAsC;EAAhCD,KAAK,EAAC;AAAc,GAAC,MAAI,E;;EAC9HA,KAAK,EAAC;AAAgB;kDACU,QAAM;kDACjC,SAAO;kDACP,QAAM;;EAMZA,KAAK,EAAC;AAAyB;iEAClCC,mBAAA,CAA6I;EAA1IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAsC;EAAhCD,KAAK,EAAC;AAAc,GAAC,MAAI,E;;EAC9HA,KAAK,EAAC;AAAgB;kDAEf,MAAI;;EAQbA,KAAK,EAAC;AAAyB;iEACrCC,mBAAA,CAEK;EAFDD,KAAK,EAAC;AAA8B,I,aACpCC,mBAAA,CAAmB,eAAZ,MAAI,E;;EAELD,KAAK,EAAC;AAAyB;iEACrCC,mBAAA,CAA6I;EAA1IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAsC;EAAhCD,KAAK,EAAC;AAAc,GAAC,MAAI,E;;EAC9HA,KAAK,EAAC;AAAgB;kDAEH,MAAI;;EAKnBA,KAAK,EAAC;AAAyB;iEACvCC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;kDACS,MAAI;;EAMnDA,KAAK,EAAC;AAAyB;iEACnBC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;kDACkB,QAAM;kDAC9B,QAAM;;EAKrBA,KAAK,EAAC;AAAyB;iEACpCC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;kDACmB,QAAM;kDAC9B,QAAM;;EAKvBA,KAAK,EAAC;AAAyB;kEACnCC,mBAAA,CAA6I;EAA1IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAsC;EAAhCD,KAAK,EAAC;AAAc,GAAC,MAAI,E;;EAC9HA,KAAK,EAAC;AAAgB;mDACmB,MAAI;mDAC5B,MAAI;;EAKpBA,KAAK,EAAC;AAAyB;kEACpCC,mBAAA,CAA8I;EAA3IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAuC;EAAjCD,KAAK,EAAC;AAAc,GAAC,OAAK,E;;EAC/HA,KAAK,EAAC;AAAgB;mDAEG,KAAG;;EAO5CA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;mDAEF,MAAI;;EAUrBA,KAAK,EAAC;AAAyB;kEACtCC,mBAAA,CAA6I;EAA1IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAsC;EAAhCD,KAAK,EAAC;AAAc,GAAC,MAAI,E;;EAC9HA,KAAK,EAAC;AAAgB;mDACe,QAAM;mDACpC,MAAI;;EASbA,KAAK,EAAC;AAAyB;kEACrCC,mBAAA,CAEK;EAFDD,KAAK,EAAC;AAA8B,I,aACpCC,mBAAA,CAAmB,eAAZ,MAAI,E;;EAELD,KAAK,EAAC;AAAyB;kEACrCC,mBAAA,CAA6I;EAA1IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAsC;EAAhCD,KAAK,EAAC;AAAc,GAAC,MAAI,E;;EAC9HA,KAAK,EAAC;AAAgB;mDACoB,MAAI;mDAC5B,MAAI;mDACH,MAAI;;EAIvCA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA8I;EAA3IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAuC;EAAjCD,KAAK,EAAC;AAAc,GAAC,OAAK,E;;EAC/HA,KAAK,EAAC;AAAgB;mDACgB,OAAK;mDAC7B,OAAK;mDACP,QAAM;;EAIlCA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;mDACkB,QAAM;mDAC9B,QAAM;mDACL,QAAM;;EAIvCA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;mDACqB,QAAM;mDAC9B,QAAM;mDACL,QAAM;;EAI1CA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;mDACsB,QAAM;mDAC9B,QAAM;mDACL,QAAM;;EAI3CA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA8I;EAA3IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAuC;EAAjCD,KAAK,EAAC;AAAc,GAAC,OAAK,E;;EAC/HA,KAAK,EAAC;AAAgB;mDAC0B,OAAK;mDAC7B,OAAK;mDACJ,OAAK;;EAI9CA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;mDACiB,QAAM;mDAC9B,QAAM;;EAIrCA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA8I;EAA3IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAuC;EAAjCD,KAAK,EAAC;AAAc,GAAC,OAAK,E;;EAC/HA,KAAK,EAAC;AAAgB;mDACmB,OAAK;mDAC7B,OAAK;;EAItCA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA6I;EAA1IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAsC;EAAhCD,KAAK,EAAC;AAAc,GAAC,MAAI,E;;EAC9HA,KAAK,EAAC;AAAgB;mDACc,MAAI;mDAC5B,MAAI;mDACN,QAAM;;EAIhCA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA6I;EAA1IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAsC;EAAhCD,KAAK,EAAC;AAAc,GAAC,MAAI,E;;EAC9HA,KAAK,EAAC;AAAgB;mDACmB,MAAI;mDAC5B,MAAI;mDACH,MAAI;;EAItCA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;mDACmB,QAAM;mDAC9B,QAAM;mDACL,QAAM;;EAIxCA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;mDACc,QAAM;mDAC9B,QAAM;mDACR,QAAM;;EAIhCA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA6I;EAA1IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAsC;EAAhCD,KAAK,EAAC;AAAc,GAAC,MAAI,E;;EAC9HA,KAAK,EAAC;AAAgB;mDACgB,MAAI;mDAC5B,MAAI;;EAIlCA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA6I;EAA1IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAsC;EAAhCD,KAAK,EAAC;AAAc,GAAC,MAAI,E;;EAC9HA,KAAK,EAAC;AAAgB;mDACU,KAAG;mDAC9B,KAAG;mDACH,KAAG;mDACH,KAAG;;EAQVA,KAAK,EAAC;AAAyB;kEACrCC,mBAAA,CAEK;EAFDD,KAAK,EAAC;AAA8B,I,aACpCC,mBAAA,CAAmB,eAAZ,MAAI,E;;EAELD,KAAK,EAAC;AAAyB;kEACrCC,mBAAA,CAA6I;EAA1IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAsC;EAAhCD,KAAK,EAAC;AAAc,GAAC,MAAI,E;;EAC9HA,KAAK,EAAC;AAAgB;mDAEH,MAAI;;EAKnBA,KAAK,EAAC;AAAyB;kEACvCC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;mDAEF,QAAM;;EAM1CA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;mDAEN,QAAM;mDACL,QAAM;;EAKpBA,KAAK,EAAC;AAAyB;kEACtCC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;mDACmB,QAAM;mDAC9B,QAAM;mDACL,QAAM;;EAKrBA,KAAK,EAAC;AAAyB;kEACtCC,mBAAA,CAA8I;EAA3IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAuC;EAAjCD,KAAK,EAAC;AAAc,GAAC,OAAK,E;;EAC/HA,KAAK,EAAC;AAAgB;mDAC0B,OAAK;mDAC7B,OAAK;;EAK7CA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;mDACqB,QAAM;mDAC9B,QAAM;;EAQFA,KAAK,EAAC;AAAyB;kEAC1DC,mBAAA,CAA6I;EAA1IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAsC;EAAhCD,KAAK,EAAC;AAAc,GAAC,MAAI,E;;EAC9HA,KAAK,EAAC;AAAgB;mDACK,QAAM;mDAC1B,MAAI;;;uBA1btCG,mBAAA,CAscS,OAtcTC,UAscS,GArcFH,mBAAA,CAocM,OApcNI,UAocM,GAncFC,UAQM,EACNL,mBAAA,CAybM,OAzbNM,UAybM,G,gBAxbFN,mBAAA,CA6IK,MA7ILO,UA6IK,GA1ICP,mBAAA,CASG,MATHQ,UASG,GARDC,UAA6I,EAC7IT,mBAAA,CAMK,MANLU,UAMK,GALIV,mBAAA,CAAgE,aAA3DW,YAAA,CAAsDC,sBAAA;IAAzCC,EAAE,EAAC;EAAkB;sBAAC,MAAK,C;;QAC5Eb,mBAAA,CAAmE,aAA9DW,YAAA,CAAyDC,sBAAA;IAA5CC,EAAE,EAAC;EAAqB;sBAAC,MAAK,C;;QACrBb,mBAAA,CAA4D,aAAvDW,YAAA,CAAkDC,sBAAA;IAArCC,EAAE,EAAC;EAAe;sBAAC,MAAI,C;;QACpEb,mBAAA,CAA+D,aAA1DW,YAAA,CAAqDC,sBAAA;IAAxCC,EAAE,EAAC;EAAkB;sBAAC,MAAI,C;;YAKrBb,mBAAA,CAOA,MAPAc,WAOA,GANDC,WAA6I,EAC7If,mBAAA,CAIK,MAJLgB,WAIK,GAHAhB,mBAAA,CAA0D,aAArDW,YAAA,CAAgDC,sBAAA;IAAnCC,EAAE,EAAC;EAAa;sBAAC,MAAI,C;;QAClEb,mBAAA,CAA6D,aAAxDW,YAAA,CAAmDC,sBAAA;IAAtCC,EAAE,EAAC;EAAgB;sBAAC,MAAI,C;;YAKjBb,mBAAA,CAOF,MAPEiB,WAOF,GANDC,WAA+I,EAC/IlB,mBAAA,CAIK,MAJLmB,WAIK,GAHAnB,mBAAA,CAA4D,aAAvDW,YAAA,CAAkDC,sBAAA;IAArCC,EAAE,EAAC;EAAa;sBAAC,MAAM,C;;QACpEb,mBAAA,CAA+D,aAA1DW,YAAA,CAAqDC,sBAAA;IAAxCC,EAAE,EAAC;EAAgB;sBAAC,MAAM,C;;YAKrBb,mBAAA,CAOA,MAPAoB,WAOA,GANDC,WAA8I,EAC9IrB,mBAAA,CAIK,MAJLsB,WAIK,GAHAtB,mBAAA,CAA6D,aAAxDW,YAAA,CAAmDC,sBAAA;IAAtCC,EAAE,EAAC;EAAe;sBAAC,MAAK,C;;QACrEb,mBAAA,CAAgE,aAA3DW,YAAA,CAAsDC,sBAAA;IAAzCC,EAAE,EAAC;EAAkB;sBAAC,MAAK,C;;YAMtBb,mBAAA,CAOA,MAPAuB,WAOA,GANDC,WAA6I,EAC7IxB,mBAAA,CAIK,MAJLyB,WAIK,GAHAzB,mBAAA,CAAgE,aAA3DW,YAAA,CAAsDC,sBAAA;IAAzCC,EAAE,EAAC;EAAmB;sBAAC,MAAI,C;;QACxEb,mBAAA,CAAmE,aAA9DW,YAAA,CAAyDC,sBAAA;IAA5CC,EAAE,EAAC;EAAsB;sBAAC,MAAI,C;;YAKxBb,mBAAA,CAOD,MAPC0B,WAOD,GANDC,WAA+I,EAC/I3B,mBAAA,CAIK,MAJL4B,WAIK,GAHA5B,mBAAA,CAAoE,aAA/DW,YAAA,CAA0DC,sBAAA;IAA7CC,EAAE,EAAC;EAAqB;sBAAC,MAAM,C;;QAC5Eb,mBAAA,CAAuE,aAAlEW,YAAA,CAA6DC,sBAAA;IAAhDC,EAAE,EAAC;EAAwB;sBAAC,MAAM,C;;YAM/Cb,mBAAA,CASkB,MATlB6B,WASkB,GARDC,WAA6I,EAC7I9B,mBAAA,CAMK,MANL+B,WAMK,GALJ/B,mBAAA,CAA+D,aAA1DW,YAAA,CAAqDC,sBAAA;IAAxCC,EAAE,EAAC;EAAgB;sBAAC,MAAM,C;;QACnEb,mBAAA,CAAkE,aAA7DW,YAAA,CAAwDC,sBAAA;IAA3CC,EAAE,EAAC;EAAmB;sBAAC,MAAM,C;;QAC/Cb,mBAAA,CAAkE,aAA7DW,YAAA,CAAwDC,sBAAA;IAA3CC,EAAE,EAAC;EAAqB;sBAAC,MAAI,C;;QAC/Cb,mBAAA,CAAmE,aAA9DW,YAAA,CAAyDC,sBAAA;IAA5CC,EAAE,EAAC;EAAsB;sBAAC,MAAI,C;;YAM3Cb,mBAAA,CAQkB,MARlBgC,WAQkB,GAPDC,WAA8I,EAC9IjC,mBAAA,CAKK,MALLkC,WAKK,GAJAlC,mBAAA,CAAuE,aAAlEW,YAAA,CAA6DC,sBAAA;IAAhDC,EAAE,EAAC;EAAyB;sBAAC,MAAK,C;;QAC/Eb,mBAAA,CAA0E,aAArEW,YAAA,CAAgEC,sBAAA;IAAnDC,EAAE,EAAC;EAA4B;sBAAC,MAAK,C;;QACvDb,mBAAA,CAA2E,aAAtEW,YAAA,CAAiEC,sBAAA;IAApDC,EAAE,EAAC;EAA6B;sBAAC,MAAK,C;;YAOnDb,mBAAA,CAQkB,MARlBmC,WAQkB,GAPDC,WAA+I,EAC/IpC,mBAAA,CAKK,MALLqC,WAKK,GAJArC,mBAAA,CAAgE,aAA3DW,YAAA,CAAsDC,sBAAA;IAAzCC,EAAE,EAAC;EAAiB;sBAAC,MAAM,C;;QACxEb,mBAAA,CAAmE,aAA9DW,YAAA,CAAyDC,sBAAA;IAA5CC,EAAE,EAAC;EAAoB;sBAAC,MAAM,C;;QAChDb,mBAAA,CAAoE,aAA/DW,YAAA,CAA0DC,sBAAA;IAA7CC,EAAE,EAAC;EAAqB;sBAAC,MAAM,C;;YAM5Cb,mBAAA,CAQkB,MARlBsC,WAQkB,GAPDC,WAA+I,EAC/IvC,mBAAA,CAKK,MALLwC,WAKK,GAJAxC,mBAAA,CAAiE,aAA5DW,YAAA,CAAuDC,sBAAA;IAA1CC,EAAE,EAAC;EAAkB;sBAAC,MAAM,C;;QACzEb,mBAAA,CAAoE,aAA/DW,YAAA,CAA0DC,sBAAA;IAA7CC,EAAE,EAAC;EAAqB;sBAAC,MAAM,C;;QACjDb,mBAAA,CAAqE,aAAhEW,YAAA,CAA2DC,sBAAA;IAA9CC,EAAE,EAAC;EAAsB;sBAAC,MAAM,C;;YAM7Cb,mBAAA,CAQkB,MARlByC,WAQkB,GAPDC,WAA+I,EAC/I1C,mBAAA,CAKK,MALL2C,WAKK,GAJA3C,mBAAA,CAAmE,aAA9DW,YAAA,CAAyDC,sBAAA;IAA5CC,EAAE,EAAC;EAAoB;sBAAC,MAAM,C;;QAC3Eb,mBAAA,CAAsE,aAAjEW,YAAA,CAA4DC,sBAAA;IAA/CC,EAAE,EAAC;EAAuB;sBAAC,MAAM,C;;QACnDb,mBAAA,CAAuE,aAAlEW,YAAA,CAA6DC,sBAAA;IAAhDC,EAAE,EAAC;EAAwB;sBAAC,MAAM,C;;YAQ/Cb,mBAAA,CASkB,MATlB4C,WASkB,GARDC,WAA6I,EAC7I7C,mBAAA,CAMK,MANL8C,WAMK,GALA9C,mBAAA,CAAwD,aAAnDW,YAAA,CAA8CC,sBAAA;IAAjCC,EAAE,EAAC;EAAS;sBAAC,MAAM,C;;QAChEb,mBAAA,CAAyD,aAApDW,YAAA,CAA+CC,sBAAA;IAAlCC,EAAE,EAAC;EAAS;sBAAC,MAAO,C;;QACtCb,mBAAA,CAAwD,aAAnDW,YAAA,CAA8CC,sBAAA;IAAjCC,EAAE,EAAC;EAAS;sBAAC,MAAM,C;;YAMjBb,mBAAA,CAOG,MAPH+C,WAOG,GANDC,WAA6I,EAC7IhD,mBAAA,CAIK,MAJLiD,WAIK,GAF3BjD,mBAAA,CAAwD,aAAnDW,YAAA,CAA8CC,sBAAA;IAAjCC,EAAE,EAAC;EAAW;sBAAC,MAAI,C;;8CAvIqBqC,KAAA,CAAAC,IAAI,W,mBA+I1CnD,mBAAA,CAoFD,MApFCoD,WAoFD,GAnFDC,WAEK,EACArD,mBAAA,CAOA,MAPAsD,WAOA,GANDC,WAA6I,EAC7IvD,mBAAA,CAIK,MAJLwD,WAIK,GAF3BxD,mBAAA,CAAoE,aAA/DW,YAAA,CAA0DC,sBAAA;IAA7CC,EAAE,EAAC;EAAuB;sBAAC,MAAI,C;;YAKxBb,mBAAA,CAMF,MANEyD,WAMF,GALDC,WAA+I,EAC/I1D,mBAAA,CAGK,MAHL2D,WAGK,GAFN3D,mBAAA,CAA2D,aAAtDW,YAAA,CAAiDC,sBAAA;IAApCC,EAAE,EAAC;EAAc;sBAAC,MAAI,C;;YAMxDb,mBAAA,CAOkB,MAPlB4D,WAOkB,GANDC,WAA+I,EAC/I7D,mBAAA,CAIK,MAJL8D,WAIK,GAHA9D,mBAAA,CAAgE,aAA3DW,YAAA,CAAsDC,sBAAA;IAAzCC,EAAE,EAAC;EAAiB;sBAAC,MAAM,C;;QACxEb,mBAAA,CAAmE,aAA9DW,YAAA,CAAyDC,sBAAA;IAA5CC,EAAE,EAAC;EAAoB;sBAAC,MAAM,C;;YAK1Bb,mBAAA,CAOC,MAPD+D,WAOC,GANDC,WAA+I,EAC/IhE,mBAAA,CAIK,MAJLiE,WAIK,GAHAjE,mBAAA,CAAiE,aAA5DW,YAAA,CAAuDC,sBAAA;IAA1CC,EAAE,EAAC;EAAkB;sBAAC,MAAM,C;;QACzEb,mBAAA,CAAoE,aAA/DW,YAAA,CAA0DC,sBAAA;IAA7CC,EAAE,EAAC;EAAqB;sBAAC,MAAM,C;;YAK5Bb,mBAAA,CAOE,MAPFkE,WAOE,GANDC,YAA6I,EAC7InE,mBAAA,CAIK,MAJLoE,YAIK,GAHApE,mBAAA,CAA+D,aAA1DW,YAAA,CAAqDC,sBAAA;IAAxCC,EAAE,EAAC;EAAkB;sBAAC,MAAI,C;;QACvEb,mBAAA,CAAkE,aAA7DW,YAAA,CAAwDC,sBAAA;IAA3CC,EAAE,EAAC;EAAqB;sBAAC,MAAI,C;;YAKzBb,mBAAA,CAOC,MAPDqE,YAOC,GANDC,YAA8I,EAC9ItE,mBAAA,CAIK,MAJLuE,YAIK,GAF3BvE,mBAAA,CAAyE,aAApEW,YAAA,CAA+DC,sBAAA;IAAlDC,EAAE,EAAC;EAA6B;sBAAC,MAAG,C;;YAOjDb,mBAAA,CAOkB,MAPlBwE,YAOkB,GANDC,YAA+I,EAC/IzE,mBAAA,CAIK,MAJL0E,YAIK,GAF3B1E,mBAAA,CAAqE,aAAhEW,YAAA,CAA2DC,sBAAA;IAA9CC,EAAE,EAAC;EAAwB;sBAAC,MAAI,C;;YAU1Bb,mBAAA,CAOD,MAPC2E,YAOD,GANDC,YAA6I,EAC7I5E,mBAAA,CAIK,MAJL6E,YAIK,GAHA7E,mBAAA,CAA6D,aAAxDW,YAAA,CAAmDC,sBAAA;IAAtCC,EAAE,EAAC;EAAc;sBAAC,MAAM,C;;QACrEb,mBAAA,CAAwD,aAAnDW,YAAA,CAA8CC,sBAAA;IAAjCC,EAAE,EAAC;EAAW;sBAAC,MAAI,C;;8CA9E2BqC,KAAA,CAAAC,IAAI,U,mBAuFhDnD,mBAAA,CAkID,MAlIC8E,YAkID,GAjIDC,YAEK,EACA/E,mBAAA,CAQA,MARAgF,YAQA,GAPDC,YAA6I,EAC7IjF,mBAAA,CAKK,MALLkF,YAKK,GAJAlF,mBAAA,CAAgE,aAA3DW,YAAA,CAAsDC,sBAAA;IAAzCC,EAAE,EAAC;EAAmB;sBAAC,MAAI,C;;QACxEb,mBAAA,CAAmE,aAA9DW,YAAA,CAAyDC,sBAAA;IAA5CC,EAAE,EAAC;EAAsB;sBAAC,MAAI,C;;QAChDb,mBAAA,CAAoE,aAA/DW,YAAA,CAA0DC,sBAAA;IAA7CC,EAAE,EAAC;EAAuB;sBAAC,MAAI,C;;YAI5Cb,mBAAA,CAQkB,MARlBmF,YAQkB,GAPDC,YAA8I,EAC9IpF,mBAAA,CAKK,MALLqF,YAKK,GAJArF,mBAAA,CAA6D,aAAxDW,YAAA,CAAmDC,sBAAA;IAAtCC,EAAE,EAAC;EAAe;sBAAC,MAAK,C;;QACrEb,mBAAA,CAAgE,aAA3DW,YAAA,CAAsDC,sBAAA;IAAzCC,EAAE,EAAC;EAAkB;sBAAC,MAAK,C;;QAC7Cb,mBAAA,CAA+D,aAA1DW,YAAA,CAAqDC,sBAAA;IAAxCC,EAAE,EAAC;EAAgB;sBAAC,MAAM,C;;YAIvCb,mBAAA,CAQkB,MARlBsF,YAQkB,GAPDC,YAA+I,EAC/IvF,mBAAA,CAKK,MALLwF,YAKK,GAJAxF,mBAAA,CAAgE,aAA3DW,YAAA,CAAsDC,sBAAA;IAAzCC,EAAE,EAAC;EAAiB;sBAAC,MAAM,C;;QACxEb,mBAAA,CAAmE,aAA9DW,YAAA,CAAyDC,sBAAA;IAA5CC,EAAE,EAAC;EAAoB;sBAAC,MAAM,C;;QAChDb,mBAAA,CAAoE,aAA/DW,YAAA,CAA0DC,sBAAA;IAA7CC,EAAE,EAAC;EAAqB;sBAAC,MAAM,C;;YAI5Cb,mBAAA,CAQkB,MARlByF,YAQkB,GAPDC,YAA+I,EAC/I1F,mBAAA,CAKK,MALL2F,YAKK,GAJA3F,mBAAA,CAAmE,aAA9DW,YAAA,CAAyDC,sBAAA;IAA5CC,EAAE,EAAC;EAAoB;sBAAC,MAAM,C;;QAC3Eb,mBAAA,CAAsE,aAAjEW,YAAA,CAA4DC,sBAAA;IAA/CC,EAAE,EAAC;EAAuB;sBAAC,MAAM,C;;QACnDb,mBAAA,CAAuE,aAAlEW,YAAA,CAA6DC,sBAAA;IAAhDC,EAAE,EAAC;EAAwB;sBAAC,MAAM,C;;YAI/Cb,mBAAA,CAQkB,MARlB4F,YAQkB,GAPDC,YAA+I,EAC/I7F,mBAAA,CAKK,MALL8F,YAKK,GAJA9F,mBAAA,CAAoE,aAA/DW,YAAA,CAA0DC,sBAAA;IAA7CC,EAAE,EAAC;EAAqB;sBAAC,MAAM,C;;QAC5Eb,mBAAA,CAAuE,aAAlEW,YAAA,CAA6DC,sBAAA;IAAhDC,EAAE,EAAC;EAAwB;sBAAC,MAAM,C;;QACpDb,mBAAA,CAAwE,aAAnEW,YAAA,CAA8DC,sBAAA;IAAjDC,EAAE,EAAC;EAAyB;sBAAC,MAAM,C;;YAIhDb,mBAAA,CAQkB,MARlB+F,YAQkB,GAPDC,YAA8I,EAC9IhG,mBAAA,CAKK,MALLiG,YAKK,GAJAjG,mBAAA,CAAuE,aAAlEW,YAAA,CAA6DC,sBAAA;IAAhDC,EAAE,EAAC;EAAyB;sBAAC,MAAK,C;;QAC/Eb,mBAAA,CAA0E,aAArEW,YAAA,CAAgEC,sBAAA;IAAnDC,EAAE,EAAC;EAA4B;sBAAC,MAAK,C;;QACvDb,mBAAA,CAA2E,aAAtEW,YAAA,CAAiEC,sBAAA;IAApDC,EAAE,EAAC;EAA6B;sBAAC,MAAK,C;;YAInDb,mBAAA,CAOkB,MAPlBkG,YAOkB,GANDC,YAA+I,EAC/InG,mBAAA,CAIK,MAJLoG,YAIK,GAHApG,mBAAA,CAA+D,aAA1DW,YAAA,CAAqDC,sBAAA;IAAxCC,EAAE,EAAC;EAAgB;sBAAC,MAAM,C;;QACvEb,mBAAA,CAAkE,aAA7DW,YAAA,CAAwDC,sBAAA;IAA3CC,EAAE,EAAC;EAAmB;sBAAC,MAAM,C;;YAI1Cb,mBAAA,CAOkB,MAPlBqG,YAOkB,GANDC,YAA8I,EAC9ItG,mBAAA,CAIK,MAJLuG,YAIK,GAHAvG,mBAAA,CAAgE,aAA3DW,YAAA,CAAsDC,sBAAA;IAAzCC,EAAE,EAAC;EAAkB;sBAAC,MAAK,C;;QACxEb,mBAAA,CAAmE,aAA9DW,YAAA,CAAyDC,sBAAA;IAA5CC,EAAE,EAAC;EAAqB;sBAAC,MAAK,C;;YAI3Cb,mBAAA,CAQkB,MARlBwG,YAQkB,GAPDC,YAA6I,EAC7IzG,mBAAA,CAKK,MALL0G,YAKK,GAJA1G,mBAAA,CAA0D,aAArDW,YAAA,CAAgDC,sBAAA;IAAnCC,EAAE,EAAC;EAAa;sBAAC,MAAI,C;;QAClEb,mBAAA,CAA6D,aAAxDW,YAAA,CAAmDC,sBAAA;IAAtCC,EAAE,EAAC;EAAgB;sBAAC,MAAI,C;;QAC1Cb,mBAAA,CAA6D,aAAxDW,YAAA,CAAmDC,sBAAA;IAAtCC,EAAE,EAAC;EAAc;sBAAC,MAAM,C;;YAIrCb,mBAAA,CAQkB,MARlB2G,YAQkB,GAPDC,YAA6I,EAC7I5G,mBAAA,CAKK,MALL6G,YAKK,GAJA7G,mBAAA,CAA+D,aAA1DW,YAAA,CAAqDC,sBAAA;IAAxCC,EAAE,EAAC;EAAkB;sBAAC,MAAI,C;;QACvEb,mBAAA,CAAkE,aAA7DW,YAAA,CAAwDC,sBAAA;IAA3CC,EAAE,EAAC;EAAqB;sBAAC,MAAI,C;;QAC/Cb,mBAAA,CAAmE,aAA9DW,YAAA,CAAyDC,sBAAA;IAA5CC,EAAE,EAAC;EAAsB;sBAAC,MAAI,C;;YAI3Cb,mBAAA,CAQkB,MARlB8G,YAQkB,GAPDC,YAA+I,EAC/I/G,mBAAA,CAKK,MALLgH,YAKK,GAJAhH,mBAAA,CAAiE,aAA5DW,YAAA,CAAuDC,sBAAA;IAA1CC,EAAE,EAAC;EAAkB;sBAAC,MAAM,C;;QACzEb,mBAAA,CAAoE,aAA/DW,YAAA,CAA0DC,sBAAA;IAA7CC,EAAE,EAAC;EAAqB;sBAAC,MAAM,C;;QACjDb,mBAAA,CAAqE,aAAhEW,YAAA,CAA2DC,sBAAA;IAA9CC,EAAE,EAAC;EAAsB;sBAAC,MAAM,C;;YAI7Cb,mBAAA,CAQkB,MARlBiH,YAQkB,GAPDC,YAA+I,EAC/IlH,mBAAA,CAKK,MALLmH,YAKK,GAJAnH,mBAAA,CAA4D,aAAvDW,YAAA,CAAkDC,sBAAA;IAArCC,EAAE,EAAC;EAAa;sBAAC,MAAM,C;;QACpEb,mBAAA,CAA+D,aAA1DW,YAAA,CAAqDC,sBAAA;IAAxCC,EAAE,EAAC;EAAgB;sBAAC,MAAM,C;;QAC5Cb,mBAAA,CAA6D,aAAxDW,YAAA,CAAmDC,sBAAA;IAAtCC,EAAE,EAAC;EAAc;sBAAC,MAAM,C;;YAIrCb,mBAAA,CAOkB,MAPlBoH,YAOkB,GANDC,YAA6I,EAC7IrH,mBAAA,CAIK,MAJLsH,YAIK,GAHAtH,mBAAA,CAA4D,aAAvDW,YAAA,CAAkDC,sBAAA;IAArCC,EAAE,EAAC;EAAe;sBAAC,MAAI,C;;QACpEb,mBAAA,CAA+D,aAA1DW,YAAA,CAAqDC,sBAAA;IAAxCC,EAAE,EAAC;EAAkB;sBAAC,MAAI,C;;YAIvCb,mBAAA,CASkB,MATlBuH,YASkB,GARDC,YAA6I,EAC7IxH,mBAAA,CAMK,MANLyH,YAMK,GALAzH,mBAAA,CAAqD,aAAhDW,YAAA,CAA2CC,sBAAA;IAA9BC,EAAE,EAAC;EAAS;sBAAC,MAAG,C;;QAC7Db,mBAAA,CAAqD,aAAhDW,YAAA,CAA2CC,sBAAA;IAA9BC,EAAE,EAAC;EAAS;sBAAC,MAAG,C;;QAClCb,mBAAA,CAAqD,aAAhDW,YAAA,CAA2CC,sBAAA;IAA9BC,EAAE,EAAC;EAAS;sBAAC,MAAG,C;;QAClCb,mBAAA,CAAqD,aAAhDW,YAAA,CAA2CC,sBAAA;IAA9BC,EAAE,EAAC;EAAS;sBAAC,MAAG,C;;8CA5H8BqC,KAAA,CAAAC,IAAI,W,mBAoIhDnD,mBAAA,CA2ED,MA3EC0H,YA2ED,GA1EDC,YAEK,EACA3H,mBAAA,CAOA,MAPA4H,YAOA,GANDC,YAA6I,EAC7I7H,mBAAA,CAIK,MAJL8H,YAIK,GAF3B9H,mBAAA,CAAoE,aAA/DW,YAAA,CAA0DC,sBAAA;IAA7CC,EAAE,EAAC;EAAuB;sBAAC,MAAI,C;;YAKxBb,mBAAA,CAQF,MARE+H,YAQF,GAPDC,YAA+I,EAC/IhI,mBAAA,CAKK,MALLiI,YAKK,GAH3BjI,mBAAA,CAAuE,aAAlEW,YAAA,CAA6DC,sBAAA;IAAhDC,EAAE,EAAC;EAAwB;sBAAC,MAAM,C;;YAM/Cb,mBAAA,CAQkB,MARlBkI,YAQkB,GAPDC,YAA+I,EAC/InI,mBAAA,CAKK,MALLoI,YAKK,GAH3BpI,mBAAA,CAAmE,aAA9DW,YAAA,CAAyDC,sBAAA;IAA5CC,EAAE,EAAC;EAAoB;sBAAC,MAAM,C;;QAChDb,mBAAA,CAAoE,aAA/DW,YAAA,CAA0DC,sBAAA;IAA7CC,EAAE,EAAC;EAAqB;sBAAC,MAAM,C;;YAKzBb,mBAAA,CAQD,MARCqI,YAQD,GAPDC,YAA+I,EAC/ItI,mBAAA,CAKK,MALLuI,YAKK,GAJAvI,mBAAA,CAAiE,aAA5DW,YAAA,CAAuDC,sBAAA;IAA1CC,EAAE,EAAC;EAAkB;sBAAC,MAAM,C;;QACzEb,mBAAA,CAAoE,aAA/DW,YAAA,CAA0DC,sBAAA;IAA7CC,EAAE,EAAC;EAAqB;sBAAC,MAAM,C;;QACjDb,mBAAA,CAAqE,aAAhEW,YAAA,CAA2DC,sBAAA;IAA9CC,EAAE,EAAC;EAAsB;sBAAC,MAAM,C;;YAK1Bb,mBAAA,CAOD,MAPCwI,YAOD,GANDC,YAA8I,EAC9IzI,mBAAA,CAIK,MAJL0I,YAIK,GAHA1I,mBAAA,CAAuE,aAAlEW,YAAA,CAA6DC,sBAAA;IAAhDC,EAAE,EAAC;EAAyB;sBAAC,MAAK,C;;QAC/Eb,mBAAA,CAA0E,aAArEW,YAAA,CAAgEC,sBAAA;IAAnDC,EAAE,EAAC;EAA4B;sBAAC,MAAK,C;;YAKlDb,mBAAA,CAOkB,MAPlB2I,YAOkB,GANDC,YAA+I,EAC/I5I,mBAAA,CAIK,MAJL6I,YAIK,GAHA7I,mBAAA,CAAmE,aAA9DW,YAAA,CAAyDC,sBAAA;IAA5CC,EAAE,EAAC;EAAoB;sBAAC,MAAM,C;;QAC3Eb,mBAAA,CAAsE,aAAjEW,YAAA,CAA4DC,sBAAA;IAA/CC,EAAE,EAAC;EAAuB;sBAAC,MAAM,C;;YAQPb,mBAAA,CAOrB,MAPqB8I,YAOrB,GANDC,YAA6I,EAC7I/I,mBAAA,CAIK,MAJLgJ,YAIK,GAHVhJ,mBAAA,CAA6D,aAAxDW,YAAA,CAAmDC,sBAAA;IAAtCC,EAAE,EAAC;EAAc;sBAAC,MAAM,C;;QAC3Db,mBAAA,CAAwD,aAAnDW,YAAA,CAA8CC,sBAAA;IAAjCC,EAAE,EAAC;EAAW;sBAAC,MAAI,C;;8CApE2BqC,KAAA,CAAAC,IAAI,Y"}]}