{"remainingRequest": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\B7839DormManager\\DormManager-web\\src\\components\\LeftMenu.vue?vue&type=template&id=edc10994&scoped=true", "dependencies": [{"path": "I:\\product4\\B7839DormManager\\DormManager-web\\src\\components\\LeftMenu.vue", "mtime": 1749046057766}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\babel.config.js", "mtime": 1749034452000}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749043011899}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749043012919}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749043013784}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749043011899}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749043013130}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["class", "_createElementVNode", "href", "_createElementBlock", "_hoisted_1", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_hoisted_5", "_hoisted_6", "_hoisted_7", "_hoisted_8", "_createVNode", "_component_router_link", "to", "_hoisted_13", "_hoisted_14", "_hoisted_15", "_hoisted_18", "_hoisted_19", "_hoisted_20", "_hoisted_23", "_hoisted_24", "_hoisted_25", "_hoisted_28", "_hoisted_29", "_hoisted_30", "_hoisted_33", "_hoisted_34", "_hoisted_35", "_hoisted_38", "_hoisted_39", "_hoisted_40", "_hoisted_45", "_hoisted_46", "_hoisted_47", "_hoisted_51", "_hoisted_52", "_hoisted_53", "_hoisted_57", "_hoisted_58", "_hoisted_59", "_hoisted_63", "_hoisted_64", "_hoisted_65", "_hoisted_69", "_hoisted_70", "_hoisted_71", "_hoisted_75", "_hoisted_76", "_hoisted_77", "$data", "role", "_hoisted_79", "_hoisted_80", "_hoisted_81", "_hoisted_82", "_hoisted_83", "_hoisted_85", "_hoisted_86", "_hoisted_87", "_hoisted_90", "_hoisted_91", "_hoisted_92", "_hoisted_95", "_hoisted_96", "_hoisted_97", "_hoisted_100", "_hoisted_101", "_hoisted_102", "_hoisted_105", "_hoisted_106", "_hoisted_107", "_hoisted_109", "_hoisted_110", "_hoisted_111", "_hoisted_113", "_hoisted_114", "_hoisted_115", "_hoisted_118", "_hoisted_119", "_hoisted_120", "_hoisted_121", "_hoisted_122", "_hoisted_126", "_hoisted_127", "_hoisted_128", "_hoisted_132", "_hoisted_133", "_hoisted_134", "_hoisted_138", "_hoisted_139", "_hoisted_140", "_hoisted_144", "_hoisted_145", "_hoisted_146", "_hoisted_150", "_hoisted_151", "_hoisted_152", "_hoisted_156", "_hoisted_157", "_hoisted_158", "_hoisted_161", "_hoisted_162", "_hoisted_163", "_hoisted_166", "_hoisted_167", "_hoisted_168", "_hoisted_172", "_hoisted_173", "_hoisted_174", "_hoisted_178", "_hoisted_179", "_hoisted_180", "_hoisted_184", "_hoisted_185", "_hoisted_186", "_hoisted_190", "_hoisted_191", "_hoisted_192", "_hoisted_195", "_hoisted_196", "_hoisted_197", "_hoisted_202", "_hoisted_203", "_hoisted_204", "_hoisted_205", "_hoisted_206", "_hoisted_208", "_hoisted_209", "_hoisted_210", "_hoisted_213", "_hoisted_214", "_hoisted_215", "_hoisted_218", "_hoisted_219", "_hoisted_220", "_hoisted_224", "_hoisted_225", "_hoisted_226", "_hoisted_229", "_hoisted_230", "_hoisted_231", "_hoisted_234", "_hoisted_235", "_hoisted_236"], "sources": ["I:\\product4\\B7839DormManager\\DormManager-web\\src\\components\\LeftMenu.vue"], "sourcesContent": ["<template>\r\n <nav class=\"pcoded-navbar\">\r\n        <div class=\"navbar-wrapper\">\r\n            <div class=\"navbar-brand header-logo\">\r\n                <a href=\"/main\" class=\"b-brand\">\r\n                    <div class=\"b-bg\">\r\n                        <i class=\"feather icon-trending-up\"></i>\r\n                    </div>\r\n                    <span class=\"b-title\">宿舍管理系统</span>\r\n                </a>\r\n\r\n            </div>\r\n            <div class=\"navbar-content scroll-div\">\r\n                <ul class=\"nav pcoded-inner-navbar\" v-show=\"role == '管理员'\">\r\n                 \r\n                   \r\n                      <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿舍管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                                 <li> <router-link to=\"/dormbuildingAdd\">添加宿舍楼</router-link></li>\r\n  <li> <router-link to=\"/dormbuildingManage\">管理宿舍楼</router-link></li>\r\n                             <li> <router-link to=\"/dormitoryAdd\">添加宿舍</router-link></li>\r\n  <li> <router-link to=\"/dormitoryManage\">管理宿舍</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                         <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">学生管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/studentAdd\">添加学生</router-link></li>\r\n  <li> <router-link to=\"/studentManage\">管理学生</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                           <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿管阿姨管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/hostessAdd\">添加宿管阿姨</router-link></li>\r\n  <li> <router-link to=\"/hostessManage\">管理宿管阿姨</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                         <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">维修员管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/repairmenAdd\">添加维修员</router-link></li>\r\n  <li> <router-link to=\"/repairmenManage\">管理维修员</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n\r\n                         <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">公告管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/systemnoticesAdd\">添加公告</router-link></li>\r\n  <li> <router-link to=\"/systemnoticesManage\">管理公告</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                          <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿舍更换管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/dormitorychangeAdd\">添加宿舍更换</router-link></li>\r\n  <li> <router-link to=\"/dormitorychangeManage\">管理宿舍更换</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                     \r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">报修管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                         <li> <router-link to=\"/repairtypeAdd\">添加报修类型</router-link></li>\r\n  <li> <router-link to=\"/repairtypeManage\">管理报修类型</router-link></li>\r\n  <li> <router-link to=\"/repairordersManage\">管理报修</router-link></li>\r\n  <li> <router-link to=\"/repairordersManage2\">报修列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                    \r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">水电费管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/waterelectricityfeeAdd\">添加水电费</router-link></li>\r\n  <li> <router-link to=\"/waterelectricityfeeManage\">管理水电费</router-link></li>\r\n  <li> <router-link to=\"/waterelectricityfeeManage2\">水电费列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n    \r\n\r\n  \r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">离校登记管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/leaveschoolAdd\">添加离校登记</router-link></li>\r\n  <li> <router-link to=\"/leaveschoolManage\">管理离校登记</router-link></li>\r\n  <li> <router-link to=\"/leaveschoolManage2\">离校登记列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                     \r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">返校登记管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/registerinfoAdd\">添加返校登记</router-link></li>\r\n  <li> <router-link to=\"/registerinfoManage\">管理返校登记</router-link></li>\r\n  <li> <router-link to=\"/registerinfoManage2\">返校登记列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿舍评分管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/dormitoryscoreAdd\">添加宿舍评分</router-link></li>\r\n  <li> <router-link to=\"/dormitoryscoreManage\">管理宿舍评分</router-link></li>\r\n  <li> <router-link to=\"/dormitoryscoreManage2\">宿舍评分列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n \r\n    \r\n\r\n     \r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">统计报表</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/total1\">报修类型统计</router-link></li>\r\n  <li> <router-link to=\"/total2\">维修员维修统计</router-link></li>\r\n  <li> <router-link to=\"/total3\">宿舍评分统计</router-link></li>\r\n\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                      <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">系统管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                           \r\n  <li> <router-link to=\"/password\">修改密码</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n          \r\n                </ul>\r\n\r\n                      <ul class=\"nav pcoded-inner-navbar\" v-show=\"role == '学生'\">\r\n                    <li class=\"nav-item pcoded-menu-caption\">\r\n                        <label>功能菜单</label>\r\n                    </li>\r\n                         <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">公告管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                         \r\n  <li> <router-link to=\"/systemnoticesManage2\">公告列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                           <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿舍更换管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                       <li> <router-link to=\"/mydormitory\">我的宿舍</router-link></li> \r\n  <li> <router-link to=\"/dormitorychangeManage2\">我的宿舍更换</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">离校登记管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/leaveschoolAdd\">添加离校登记</router-link></li>\r\n  <li> <router-link to=\"/leaveschoolManage\">我的离校登记</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                        <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">返校登记管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/registerinfoAdd\">添加返校登记</router-link></li>\r\n  <li> <router-link to=\"/registerinfoManage\">我的返校登记</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">报修管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/repairordersAdd\">在线报修</router-link></li>\r\n  <li> <router-link to=\"/repairordersManage\">我的报修</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                        <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">水电费管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n          \r\n  <li> <router-link to=\"/waterelectricityfeeManage2\">水电费</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n     \r\n     \r\n\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿舍评分管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                    \r\n  <li> <router-link to=\"/dormitoryscoreManage2\">宿舍评分</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n   \r\n \r\n    \r\n   \r\n\r\n                          <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">个人中心</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/studentInfo\">修改个人信息</router-link></li>\r\n  <li> <router-link to=\"/password\">修改密码</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n          \r\n                </ul>\r\n\r\n\r\n                      <ul class=\"nav pcoded-inner-navbar\" v-show=\"role == '维修员'\">\r\n                    <li class=\"nav-item pcoded-menu-caption\">\r\n                        <label>功能菜单</label>\r\n                    </li>\r\n                         <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">公告管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/systemnoticesAdd\">添加公告</router-link></li>\r\n  <li> <router-link to=\"/systemnoticesManage\">管理公告</router-link></li>\r\n  <li> <router-link to=\"/systemnoticesManage2\">公告列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">维修员管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/repairmenAdd\">添加维修员</router-link></li>\r\n  <li> <router-link to=\"/repairmenManage\">管理维修员</router-link></li>\r\n  <li> <router-link to=\"/repairmenInfo\">修改个人信息</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">离校登记管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/leaveschoolAdd\">添加离校登记</router-link></li>\r\n  <li> <router-link to=\"/leaveschoolManage\">管理离校登记</router-link></li>\r\n  <li> <router-link to=\"/leaveschoolManage2\">离校登记列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿舍评分管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/dormitoryscoreAdd\">添加宿舍评分</router-link></li>\r\n  <li> <router-link to=\"/dormitoryscoreManage\">管理宿舍评分</router-link></li>\r\n  <li> <router-link to=\"/dormitoryscoreManage2\">宿舍评分列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿舍更换管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/dormitorychangeAdd\">添加宿舍更换</router-link></li>\r\n  <li> <router-link to=\"/dormitorychangeManage\">管理宿舍更换</router-link></li>\r\n  <li> <router-link to=\"/dormitorychangeManage2\">宿舍更换列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">水电费管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/waterelectricityfeeAdd\">添加水电费</router-link></li>\r\n  <li> <router-link to=\"/waterelectricityfeeManage\">管理水电费</router-link></li>\r\n  <li> <router-link to=\"/waterelectricityfeeManage2\">水电费列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">报修类型管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/repairtypeAdd\">添加报修类型</router-link></li>\r\n  <li> <router-link to=\"/repairtypeManage\">管理报修类型</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿舍楼管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/dormbuildingAdd\">添加宿舍楼</router-link></li>\r\n  <li> <router-link to=\"/dormbuildingManage\">管理宿舍楼</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">学生管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/studentAdd\">添加学生</router-link></li>\r\n  <li> <router-link to=\"/studentManage\">管理学生</router-link></li>\r\n  <li> <router-link to=\"/studentInfo\">修改个人信息</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">报修管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/repairordersAdd\">添加报修</router-link></li>\r\n  <li> <router-link to=\"/repairordersManage\">管理报修</router-link></li>\r\n  <li> <router-link to=\"/repairordersManage2\">报修列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">返校登记管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/registerinfoAdd\">添加返校登记</router-link></li>\r\n  <li> <router-link to=\"/registerinfoManage\">管理返校登记</router-link></li>\r\n  <li> <router-link to=\"/registerinfoManage2\">返校登记列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿管阿姨管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/hostessAdd\">添加宿管阿姨</router-link></li>\r\n  <li> <router-link to=\"/hostessManage\">管理宿管阿姨</router-link></li>\r\n  <li> <router-link to=\"/hostessInfo\">修改个人信息</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿舍管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/dormitoryAdd\">添加宿舍</router-link></li>\r\n  <li> <router-link to=\"/dormitoryManage\">管理宿舍</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">图表管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/total1\">图表1</router-link></li>\r\n  <li> <router-link to=\"/total2\">图表2</router-link></li>\r\n  <li> <router-link to=\"/total3\">图表3</router-link></li>\r\n  <li> <router-link to=\"/total4\">图表4</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n          \r\n                </ul>\r\n\r\n                      <ul class=\"nav pcoded-inner-navbar\" v-show=\"role == '宿管阿姨'\">\r\n                    <li class=\"nav-item pcoded-menu-caption\">\r\n                        <label>功能菜单</label>\r\n                    </li>\r\n                         <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">公告管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                        \r\n  <li> <router-link to=\"/systemnoticesManage2\">公告列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                           <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿舍更换管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                 \r\n  <li> <router-link to=\"/dormitorychangeManage\">管理宿舍更换</router-link></li>\r\n  <li> <router-link to=\"/dormitorychangeManage2\">宿舍更换列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n   \r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">离校登记管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                         \r\n  <li> <router-link to=\"/leaveschoolManage\">管理离校登记</router-link></li>\r\n  <li> <router-link to=\"/leaveschoolManage2\">离校登记列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                          <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">返校登记管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/registerinfoAdd\">添加返校登记</router-link></li>\r\n  <li> <router-link to=\"/registerinfoManage\">管理返校登记</router-link></li>\r\n  <li> <router-link to=\"/registerinfoManage2\">返校登记列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                          <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">水电费管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/waterelectricityfeeAdd\">添加水电费</router-link></li>\r\n  <li> <router-link to=\"/waterelectricityfeeManage\">管理水电费</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿舍评分管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/dormitoryscoreAdd\">添加宿舍评分</router-link></li>\r\n  <li> <router-link to=\"/dormitoryscoreManage\">管理宿舍评分</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n \r\n \r\n     \r\n                                              <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">个人中心</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                   <li> <router-link to=\"/hostessInfo\">修改个人信息</router-link></li>\r\n  <li> <router-link to=\"/password\">修改密码</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n\r\n          \r\n                </ul>\r\n\r\n\r\n            </div>\r\n        </div>\r\n    </nav>\r\n    \r\n\r\n</template>\r\n\r\n\r\n<script>\r\nimport $ from 'jquery';\r\n\r\nexport default {\r\n  name: \"LeftMenu\",\r\n  data() {\r\n    return {\r\n      userLname: \"\",\r\n      role: \"\",\r\n      activeMenu: null, // 用于跟踪当前激活的菜单\r\n    };\r\n  },\r\n  watch: {\r\n    $route(to, from) {\r\n      this.activeMenu = to.name;\r\n      this.$nextTick(() => {\r\n        this.initializeMenu();\r\n      });\r\n    }\r\n  },\r\n  mounted() {\r\n    this.userLname = sessionStorage.getItem(\"userLname\");\r\n    this.role = sessionStorage.getItem(\"role\");\r\n    this.$nextTick(() => {\r\n      this.initializeMenu();\r\n    });\r\n  },\r\n  methods: {\r\n    initializeMenu() {\r\n      $('.nav-item.pcoded-hasmenu > a').off('click').on('click', function(e) {\r\n        e.preventDefault();\r\n        const $parent = $(this).parent();\r\n\r\n        if ($parent.hasClass('pcoded-trigger')) {\r\n          $parent.removeClass('pcoded-trigger');\r\n          $parent.children('ul').slideUp();\r\n        } else {\r\n          $('.nav-item.pcoded-hasmenu').removeClass('pcoded-trigger');\r\n          $('.nav-item.pcoded-hasmenu > ul').slideUp();\r\n          $parent.addClass('pcoded-trigger');\r\n          $parent.children('ul').slideDown();\r\n        }\r\n      });\r\n\r\n      // 初始化：根据当前路由展开对应的菜单\r\n      const currentPath = this.$route.path;\r\n      $('.pcoded-submenu a').each(function() {\r\n        if ($(this).attr('href') === currentPath) {\r\n          $(this).parents('.nav-item.pcoded-hasmenu').addClass('pcoded-trigger');\r\n          $(this).parents('ul.pcoded-submenu').show();\r\n        }\r\n      });\r\n    },\r\n\r\n    exit: function () {\r\n      var _this = this;\r\n      this.$confirm(\"确认退出吗?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n          .then(() => {\r\n            sessionStorage.removeItem(\"userLname\");\r\n            sessionStorage.removeItem(\"role\");\r\n            _this.$router.push(\"/\");\r\n          })\r\n          .catch(() => { });\r\n    },\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.example-showcase .el-dropdown-link {\r\n  cursor: pointer;\r\n  color:green;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.pcoded-submenu a{\r\n  text-decoration: none;\r\n  font-size: 14px;\r\n}\r\n\r\n/*加点击效果*/\r\n.pcoded-submenu a:hover{\r\n  color: #fff;\r\n  color: #ff6600;\r\n}\r\n</style>\r\n\r\n"], "mappings": ";;;EACMA,KAAK,EAAC;AAAe;;EACdA,KAAK,EAAC;AAAgB;;;EAUlBA,KAAK,EAAC;AAA2B;;EAC9BA,KAAK,EAAC;AAAyB;;EAGxBA,KAAK,EAAC;AAAyB;gEAClCC,mBAAA,CAA6I;EAA1IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAsC;EAAhCD,KAAK,EAAC;AAAc,GAAC,MAAI,E;;EAC9HA,KAAK,EAAC;AAAgB;iDACuB,OAAK;kDACjC,OAAK;kDACgB,MAAI;kDAC5B,MAAI;;EAKhBA,KAAK,EAAC;AAAyB;iEACrCC,mBAAA,CAA6I;EAA1IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAsC;EAAhCD,KAAK,EAAC;AAAc,GAAC,MAAI,E;;EAC9HA,KAAK,EAAC;AAAgB;kDACc,MAAI;kDAC5B,MAAI;;EAKZA,KAAK,EAAC;AAAyB;iEACvCC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;kDACc,QAAM;kDAC9B,QAAM;;EAKhBA,KAAK,EAAC;AAAyB;iEACrCC,mBAAA,CAA8I;EAA3IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAuC;EAAjCD,KAAK,EAAC;AAAc,GAAC,OAAK,E;;EAC/HA,KAAK,EAAC;AAAgB;kDACgB,OAAK;kDAC7B,OAAK;;EAMjBA,KAAK,EAAC;AAAyB;iEACrCC,mBAAA,CAA6I;EAA1IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAsC;EAAhCD,KAAK,EAAC;AAAc,GAAC,MAAI,E;;EAC9HA,KAAK,EAAC;AAAgB;kDACoB,MAAI;kDAC5B,MAAI;;EAKnBA,KAAK,EAAC;AAAyB;iEACtCC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;kDACsB,QAAM;kDAC9B,QAAM;;EAM1CA,KAAK,EAAC;AAAyB;iEACnBC,mBAAA,CAA6I;EAA1IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAsC;EAAhCD,KAAK,EAAC;AAAc,GAAC,MAAI,E;;EAC9HA,KAAK,EAAC;AAAgB;kDACa,QAAM;kDAC1B,QAAM;kDACJ,MAAI;kDACH,MAAI;;EAMtCA,KAAK,EAAC;AAAyB;iEACnBC,mBAAA,CAA8I;EAA3IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAuC;EAAjCD,KAAK,EAAC;AAAc,GAAC,OAAK,E;;EAC/HA,KAAK,EAAC;AAAgB;kDAC0B,OAAK;kDAC7B,OAAK;kDACJ,OAAK;;EAO9CA,KAAK,EAAC;AAAyB;iEACnBC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;kDACkB,QAAM;kDAC9B,QAAM;kDACL,QAAM;;EAMvCA,KAAK,EAAC;AAAyB;iEACnBC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;kDACmB,QAAM;kDAC9B,QAAM;kDACL,QAAM;;EAMxCA,KAAK,EAAC;AAAyB;iEACnBC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;kDACqB,QAAM;kDAC9B,QAAM;kDACL,QAAM;;EAQ1CA,KAAK,EAAC;AAAyB;iEACnBC,mBAAA,CAA6I;EAA1IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAsC;EAAhCD,KAAK,EAAC;AAAc,GAAC,MAAI,E;;EAC9HA,KAAK,EAAC;AAAgB;kDACU,QAAM;kDACjC,SAAO;kDACP,QAAM;;EAMZA,KAAK,EAAC;AAAyB;iEAClCC,mBAAA,CAA6I;EAA1IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAsC;EAAhCD,KAAK,EAAC;AAAc,GAAC,MAAI,E;;EAC9HA,KAAK,EAAC;AAAgB;kDAEf,MAAI;;EAQbA,KAAK,EAAC;AAAyB;iEACrCC,mBAAA,CAEK;EAFDD,KAAK,EAAC;AAA8B,I,aACpCC,mBAAA,CAAmB,eAAZ,MAAI,E;;EAELD,KAAK,EAAC;AAAyB;iEACrCC,mBAAA,CAA6I;EAA1IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAsC;EAAhCD,KAAK,EAAC;AAAc,GAAC,MAAI,E;;EAC9HA,KAAK,EAAC;AAAgB;kDAEH,MAAI;;EAKnBA,KAAK,EAAC;AAAyB;iEACvCC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;kDACS,MAAI;kDACd,QAAM;;EAM3CA,KAAK,EAAC;AAAyB;iEACnBC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;kDACkB,QAAM;kDAC9B,QAAM;;EAKrBA,KAAK,EAAC;AAAyB;iEACpCC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;kDACmB,QAAM;kDAC9B,QAAM;;EAKvBA,KAAK,EAAC;AAAyB;kEACnCC,mBAAA,CAA6I;EAA1IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAsC;EAAhCD,KAAK,EAAC;AAAc,GAAC,MAAI,E;;EAC9HA,KAAK,EAAC;AAAgB;mDACmB,MAAI;mDAC5B,MAAI;;EAKpBA,KAAK,EAAC;AAAyB;kEACpCC,mBAAA,CAA8I;EAA3IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAuC;EAAjCD,KAAK,EAAC;AAAc,GAAC,OAAK,E;;EAC/HA,KAAK,EAAC;AAAgB;mDAEG,KAAG;;EAO5CA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;mDAEF,MAAI;;EAUrBA,KAAK,EAAC;AAAyB;kEACtCC,mBAAA,CAA6I;EAA1IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAsC;EAAhCD,KAAK,EAAC;AAAc,GAAC,MAAI,E;;EAC9HA,KAAK,EAAC;AAAgB;mDACe,QAAM;mDACpC,MAAI;;EASbA,KAAK,EAAC;AAAyB;kEACrCC,mBAAA,CAEK;EAFDD,KAAK,EAAC;AAA8B,I,aACpCC,mBAAA,CAAmB,eAAZ,MAAI,E;;EAELD,KAAK,EAAC;AAAyB;kEACrCC,mBAAA,CAA6I;EAA1IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAsC;EAAhCD,KAAK,EAAC;AAAc,GAAC,MAAI,E;;EAC9HA,KAAK,EAAC;AAAgB;mDACoB,MAAI;mDAC5B,MAAI;mDACH,MAAI;;EAIvCA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA8I;EAA3IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAuC;EAAjCD,KAAK,EAAC;AAAc,GAAC,OAAK,E;;EAC/HA,KAAK,EAAC;AAAgB;mDACgB,OAAK;mDAC7B,OAAK;mDACP,QAAM;;EAIlCA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;mDACkB,QAAM;mDAC9B,QAAM;mDACL,QAAM;;EAIvCA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;mDACqB,QAAM;mDAC9B,QAAM;mDACL,QAAM;;EAI1CA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;mDACsB,QAAM;mDAC9B,QAAM;mDACL,QAAM;;EAI3CA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA8I;EAA3IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAuC;EAAjCD,KAAK,EAAC;AAAc,GAAC,OAAK,E;;EAC/HA,KAAK,EAAC;AAAgB;mDAC0B,OAAK;mDAC7B,OAAK;mDACJ,OAAK;;EAI9CA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;mDACiB,QAAM;mDAC9B,QAAM;;EAIrCA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA8I;EAA3IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAuC;EAAjCD,KAAK,EAAC;AAAc,GAAC,OAAK,E;;EAC/HA,KAAK,EAAC;AAAgB;mDACmB,OAAK;mDAC7B,OAAK;;EAItCA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA6I;EAA1IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAsC;EAAhCD,KAAK,EAAC;AAAc,GAAC,MAAI,E;;EAC9HA,KAAK,EAAC;AAAgB;mDACc,MAAI;mDAC5B,MAAI;mDACN,QAAM;;EAIhCA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA6I;EAA1IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAsC;EAAhCD,KAAK,EAAC;AAAc,GAAC,MAAI,E;;EAC9HA,KAAK,EAAC;AAAgB;mDACmB,MAAI;mDAC5B,MAAI;mDACH,MAAI;;EAItCA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;mDACmB,QAAM;mDAC9B,QAAM;mDACL,QAAM;;EAIxCA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;mDACc,QAAM;mDAC9B,QAAM;mDACR,QAAM;;EAIhCA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA6I;EAA1IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAsC;EAAhCD,KAAK,EAAC;AAAc,GAAC,MAAI,E;;EAC9HA,KAAK,EAAC;AAAgB;mDACgB,MAAI;mDAC5B,MAAI;;EAIlCA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA6I;EAA1IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAsC;EAAhCD,KAAK,EAAC;AAAc,GAAC,MAAI,E;;EAC9HA,KAAK,EAAC;AAAgB;mDACU,KAAG;mDAC9B,KAAG;mDACH,KAAG;mDACH,KAAG;;EAQVA,KAAK,EAAC;AAAyB;kEACrCC,mBAAA,CAEK;EAFDD,KAAK,EAAC;AAA8B,I,aACpCC,mBAAA,CAAmB,eAAZ,MAAI,E;;EAELD,KAAK,EAAC;AAAyB;kEACrCC,mBAAA,CAA6I;EAA1IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAsC;EAAhCD,KAAK,EAAC;AAAc,GAAC,MAAI,E;;EAC9HA,KAAK,EAAC;AAAgB;mDAEH,MAAI;;EAKnBA,KAAK,EAAC;AAAyB;kEACvCC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;mDAEF,QAAM;mDACL,QAAM;;EAK3CA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;mDAEN,QAAM;mDACL,QAAM;;EAKpBA,KAAK,EAAC;AAAyB;kEACtCC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;mDACmB,QAAM;mDAC9B,QAAM;mDACL,QAAM;;EAKrBA,KAAK,EAAC;AAAyB;kEACtCC,mBAAA,CAA8I;EAA3IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAuC;EAAjCD,KAAK,EAAC;AAAc,GAAC,OAAK,E;;EAC/HA,KAAK,EAAC;AAAgB;mDAC0B,OAAK;mDAC7B,OAAK;;EAK7CA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;mDACqB,QAAM;mDAC9B,QAAM;;EAQFA,KAAK,EAAC;AAAyB;kEAC1DC,mBAAA,CAA6I;EAA1IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAsC;EAAhCD,KAAK,EAAC;AAAc,GAAC,MAAI,E;;EAC9HA,KAAK,EAAC;AAAgB;mDACK,QAAM;mDAC1B,MAAI;;;uBA3btCG,mBAAA,CAucS,OAvcTC,UAucS,GAtcFH,mBAAA,CAqcM,OArcNI,UAqcM,GApcFC,UAQM,EACNL,mBAAA,CA0bM,OA1bNM,UA0bM,G,gBAzbFN,mBAAA,CA6IK,MA7ILO,UA6IK,GA1ICP,mBAAA,CASG,MATHQ,UASG,GARDC,UAA6I,EAC7IT,mBAAA,CAMK,MANLU,UAMK,GALIV,mBAAA,CAAgE,aAA3DW,YAAA,CAAsDC,sBAAA;IAAzCC,EAAE,EAAC;EAAkB;sBAAC,MAAK,C;;QAC5Eb,mBAAA,CAAmE,aAA9DW,YAAA,CAAyDC,sBAAA;IAA5CC,EAAE,EAAC;EAAqB;sBAAC,MAAK,C;;QACrBb,mBAAA,CAA4D,aAAvDW,YAAA,CAAkDC,sBAAA;IAArCC,EAAE,EAAC;EAAe;sBAAC,MAAI,C;;QACpEb,mBAAA,CAA+D,aAA1DW,YAAA,CAAqDC,sBAAA;IAAxCC,EAAE,EAAC;EAAkB;sBAAC,MAAI,C;;YAKrBb,mBAAA,CAOA,MAPAc,WAOA,GANDC,WAA6I,EAC7If,mBAAA,CAIK,MAJLgB,WAIK,GAHAhB,mBAAA,CAA0D,aAArDW,YAAA,CAAgDC,sBAAA;IAAnCC,EAAE,EAAC;EAAa;sBAAC,MAAI,C;;QAClEb,mBAAA,CAA6D,aAAxDW,YAAA,CAAmDC,sBAAA;IAAtCC,EAAE,EAAC;EAAgB;sBAAC,MAAI,C;;YAKjBb,mBAAA,CAOF,MAPEiB,WAOF,GANDC,WAA+I,EAC/IlB,mBAAA,CAIK,MAJLmB,WAIK,GAHAnB,mBAAA,CAA4D,aAAvDW,YAAA,CAAkDC,sBAAA;IAArCC,EAAE,EAAC;EAAa;sBAAC,MAAM,C;;QACpEb,mBAAA,CAA+D,aAA1DW,YAAA,CAAqDC,sBAAA;IAAxCC,EAAE,EAAC;EAAgB;sBAAC,MAAM,C;;YAKrBb,mBAAA,CAOA,MAPAoB,WAOA,GANDC,WAA8I,EAC9IrB,mBAAA,CAIK,MAJLsB,WAIK,GAHAtB,mBAAA,CAA6D,aAAxDW,YAAA,CAAmDC,sBAAA;IAAtCC,EAAE,EAAC;EAAe;sBAAC,MAAK,C;;QACrEb,mBAAA,CAAgE,aAA3DW,YAAA,CAAsDC,sBAAA;IAAzCC,EAAE,EAAC;EAAkB;sBAAC,MAAK,C;;YAMtBb,mBAAA,CAOA,MAPAuB,WAOA,GANDC,WAA6I,EAC7IxB,mBAAA,CAIK,MAJLyB,WAIK,GAHAzB,mBAAA,CAAgE,aAA3DW,YAAA,CAAsDC,sBAAA;IAAzCC,EAAE,EAAC;EAAmB;sBAAC,MAAI,C;;QACxEb,mBAAA,CAAmE,aAA9DW,YAAA,CAAyDC,sBAAA;IAA5CC,EAAE,EAAC;EAAsB;sBAAC,MAAI,C;;YAKxBb,mBAAA,CAOD,MAPC0B,WAOD,GANDC,WAA+I,EAC/I3B,mBAAA,CAIK,MAJL4B,WAIK,GAHA5B,mBAAA,CAAoE,aAA/DW,YAAA,CAA0DC,sBAAA;IAA7CC,EAAE,EAAC;EAAqB;sBAAC,MAAM,C;;QAC5Eb,mBAAA,CAAuE,aAAlEW,YAAA,CAA6DC,sBAAA;IAAhDC,EAAE,EAAC;EAAwB;sBAAC,MAAM,C;;YAM/Cb,mBAAA,CASkB,MATlB6B,WASkB,GARDC,WAA6I,EAC7I9B,mBAAA,CAMK,MANL+B,WAMK,GALJ/B,mBAAA,CAA+D,aAA1DW,YAAA,CAAqDC,sBAAA;IAAxCC,EAAE,EAAC;EAAgB;sBAAC,MAAM,C;;QACnEb,mBAAA,CAAkE,aAA7DW,YAAA,CAAwDC,sBAAA;IAA3CC,EAAE,EAAC;EAAmB;sBAAC,MAAM,C;;QAC/Cb,mBAAA,CAAkE,aAA7DW,YAAA,CAAwDC,sBAAA;IAA3CC,EAAE,EAAC;EAAqB;sBAAC,MAAI,C;;QAC/Cb,mBAAA,CAAmE,aAA9DW,YAAA,CAAyDC,sBAAA;IAA5CC,EAAE,EAAC;EAAsB;sBAAC,MAAI,C;;YAM3Cb,mBAAA,CAQkB,MARlBgC,WAQkB,GAPDC,WAA8I,EAC9IjC,mBAAA,CAKK,MALLkC,WAKK,GAJAlC,mBAAA,CAAuE,aAAlEW,YAAA,CAA6DC,sBAAA;IAAhDC,EAAE,EAAC;EAAyB;sBAAC,MAAK,C;;QAC/Eb,mBAAA,CAA0E,aAArEW,YAAA,CAAgEC,sBAAA;IAAnDC,EAAE,EAAC;EAA4B;sBAAC,MAAK,C;;QACvDb,mBAAA,CAA2E,aAAtEW,YAAA,CAAiEC,sBAAA;IAApDC,EAAE,EAAC;EAA6B;sBAAC,MAAK,C;;YAOnDb,mBAAA,CAQkB,MARlBmC,WAQkB,GAPDC,WAA+I,EAC/IpC,mBAAA,CAKK,MALLqC,WAKK,GAJArC,mBAAA,CAAgE,aAA3DW,YAAA,CAAsDC,sBAAA;IAAzCC,EAAE,EAAC;EAAiB;sBAAC,MAAM,C;;QACxEb,mBAAA,CAAmE,aAA9DW,YAAA,CAAyDC,sBAAA;IAA5CC,EAAE,EAAC;EAAoB;sBAAC,MAAM,C;;QAChDb,mBAAA,CAAoE,aAA/DW,YAAA,CAA0DC,sBAAA;IAA7CC,EAAE,EAAC;EAAqB;sBAAC,MAAM,C;;YAM5Cb,mBAAA,CAQkB,MARlBsC,WAQkB,GAPDC,WAA+I,EAC/IvC,mBAAA,CAKK,MALLwC,WAKK,GAJAxC,mBAAA,CAAiE,aAA5DW,YAAA,CAAuDC,sBAAA;IAA1CC,EAAE,EAAC;EAAkB;sBAAC,MAAM,C;;QACzEb,mBAAA,CAAoE,aAA/DW,YAAA,CAA0DC,sBAAA;IAA7CC,EAAE,EAAC;EAAqB;sBAAC,MAAM,C;;QACjDb,mBAAA,CAAqE,aAAhEW,YAAA,CAA2DC,sBAAA;IAA9CC,EAAE,EAAC;EAAsB;sBAAC,MAAM,C;;YAM7Cb,mBAAA,CAQkB,MARlByC,WAQkB,GAPDC,WAA+I,EAC/I1C,mBAAA,CAKK,MALL2C,WAKK,GAJA3C,mBAAA,CAAmE,aAA9DW,YAAA,CAAyDC,sBAAA;IAA5CC,EAAE,EAAC;EAAoB;sBAAC,MAAM,C;;QAC3Eb,mBAAA,CAAsE,aAAjEW,YAAA,CAA4DC,sBAAA;IAA/CC,EAAE,EAAC;EAAuB;sBAAC,MAAM,C;;QACnDb,mBAAA,CAAuE,aAAlEW,YAAA,CAA6DC,sBAAA;IAAhDC,EAAE,EAAC;EAAwB;sBAAC,MAAM,C;;YAQ/Cb,mBAAA,CASkB,MATlB4C,WASkB,GARDC,WAA6I,EAC7I7C,mBAAA,CAMK,MANL8C,WAMK,GALA9C,mBAAA,CAAwD,aAAnDW,YAAA,CAA8CC,sBAAA;IAAjCC,EAAE,EAAC;EAAS;sBAAC,MAAM,C;;QAChEb,mBAAA,CAAyD,aAApDW,YAAA,CAA+CC,sBAAA;IAAlCC,EAAE,EAAC;EAAS;sBAAC,MAAO,C;;QACtCb,mBAAA,CAAwD,aAAnDW,YAAA,CAA8CC,sBAAA;IAAjCC,EAAE,EAAC;EAAS;sBAAC,MAAM,C;;YAMjBb,mBAAA,CAOG,MAPH+C,WAOG,GANDC,WAA6I,EAC7IhD,mBAAA,CAIK,MAJLiD,WAIK,GAF3BjD,mBAAA,CAAwD,aAAnDW,YAAA,CAA8CC,sBAAA;IAAjCC,EAAE,EAAC;EAAW;sBAAC,MAAI,C;;8CAvIqBqC,KAAA,CAAAC,IAAI,W,mBA+I1CnD,mBAAA,CAqFD,MArFCoD,WAqFD,GApFDC,WAEK,EACArD,mBAAA,CAOA,MAPAsD,WAOA,GANDC,WAA6I,EAC7IvD,mBAAA,CAIK,MAJLwD,WAIK,GAF3BxD,mBAAA,CAAoE,aAA/DW,YAAA,CAA0DC,sBAAA;IAA7CC,EAAE,EAAC;EAAuB;sBAAC,MAAI,C;;YAKxBb,mBAAA,CAOF,MAPEyD,WAOF,GANDC,WAA+I,EAC/I1D,mBAAA,CAIK,MAJL2D,WAIK,GAHN3D,mBAAA,CAA2D,aAAtDW,YAAA,CAAiDC,sBAAA;IAApCC,EAAE,EAAC;EAAc;sBAAC,MAAI,C;;QAC7Db,mBAAA,CAAwE,aAAnEW,YAAA,CAA8DC,sBAAA;IAAjDC,EAAE,EAAC;EAAyB;sBAAC,MAAM,C;;YAMhDb,mBAAA,CAOkB,MAPlB4D,WAOkB,GANDC,WAA+I,EAC/I7D,mBAAA,CAIK,MAJL8D,WAIK,GAHA9D,mBAAA,CAAgE,aAA3DW,YAAA,CAAsDC,sBAAA;IAAzCC,EAAE,EAAC;EAAiB;sBAAC,MAAM,C;;QACxEb,mBAAA,CAAmE,aAA9DW,YAAA,CAAyDC,sBAAA;IAA5CC,EAAE,EAAC;EAAoB;sBAAC,MAAM,C;;YAK1Bb,mBAAA,CAOC,MAPD+D,WAOC,GANDC,WAA+I,EAC/IhE,mBAAA,CAIK,MAJLiE,WAIK,GAHAjE,mBAAA,CAAiE,aAA5DW,YAAA,CAAuDC,sBAAA;IAA1CC,EAAE,EAAC;EAAkB;sBAAC,MAAM,C;;QACzEb,mBAAA,CAAoE,aAA/DW,YAAA,CAA0DC,sBAAA;IAA7CC,EAAE,EAAC;EAAqB;sBAAC,MAAM,C;;YAK5Bb,mBAAA,CAOE,MAPFkE,YAOE,GANDC,YAA6I,EAC7InE,mBAAA,CAIK,MAJLoE,YAIK,GAHApE,mBAAA,CAA+D,aAA1DW,YAAA,CAAqDC,sBAAA;IAAxCC,EAAE,EAAC;EAAkB;sBAAC,MAAI,C;;QACvEb,mBAAA,CAAkE,aAA7DW,YAAA,CAAwDC,sBAAA;IAA3CC,EAAE,EAAC;EAAqB;sBAAC,MAAI,C;;YAKzBb,mBAAA,CAOC,MAPDqE,YAOC,GANDC,YAA8I,EAC9ItE,mBAAA,CAIK,MAJLuE,YAIK,GAF3BvE,mBAAA,CAAyE,aAApEW,YAAA,CAA+DC,sBAAA;IAAlDC,EAAE,EAAC;EAA6B;sBAAC,MAAG,C;;YAOjDb,mBAAA,CAOkB,MAPlBwE,YAOkB,GANDC,YAA+I,EAC/IzE,mBAAA,CAIK,MAJL0E,YAIK,GAF3B1E,mBAAA,CAAqE,aAAhEW,YAAA,CAA2DC,sBAAA;IAA9CC,EAAE,EAAC;EAAwB;sBAAC,MAAI,C;;YAU1Bb,mBAAA,CAOD,MAPC2E,YAOD,GANDC,YAA6I,EAC7I5E,mBAAA,CAIK,MAJL6E,YAIK,GAHA7E,mBAAA,CAA6D,aAAxDW,YAAA,CAAmDC,sBAAA;IAAtCC,EAAE,EAAC;EAAc;sBAAC,MAAM,C;;QACrEb,mBAAA,CAAwD,aAAnDW,YAAA,CAA8CC,sBAAA;IAAjCC,EAAE,EAAC;EAAW;sBAAC,MAAI,C;;8CA/E2BqC,KAAA,CAAAC,IAAI,U,mBAwFhDnD,mBAAA,CAkID,MAlIC8E,YAkID,GAjIDC,YAEK,EACA/E,mBAAA,CAQA,MARAgF,YAQA,GAPDC,YAA6I,EAC7IjF,mBAAA,CAKK,MALLkF,YAKK,GAJAlF,mBAAA,CAAgE,aAA3DW,YAAA,CAAsDC,sBAAA;IAAzCC,EAAE,EAAC;EAAmB;sBAAC,MAAI,C;;QACxEb,mBAAA,CAAmE,aAA9DW,YAAA,CAAyDC,sBAAA;IAA5CC,EAAE,EAAC;EAAsB;sBAAC,MAAI,C;;QAChDb,mBAAA,CAAoE,aAA/DW,YAAA,CAA0DC,sBAAA;IAA7CC,EAAE,EAAC;EAAuB;sBAAC,MAAI,C;;YAI5Cb,mBAAA,CAQkB,MARlBmF,YAQkB,GAPDC,YAA8I,EAC9IpF,mBAAA,CAKK,MALLqF,YAKK,GAJArF,mBAAA,CAA6D,aAAxDW,YAAA,CAAmDC,sBAAA;IAAtCC,EAAE,EAAC;EAAe;sBAAC,MAAK,C;;QACrEb,mBAAA,CAAgE,aAA3DW,YAAA,CAAsDC,sBAAA;IAAzCC,EAAE,EAAC;EAAkB;sBAAC,MAAK,C;;QAC7Cb,mBAAA,CAA+D,aAA1DW,YAAA,CAAqDC,sBAAA;IAAxCC,EAAE,EAAC;EAAgB;sBAAC,MAAM,C;;YAIvCb,mBAAA,CAQkB,MARlBsF,YAQkB,GAPDC,YAA+I,EAC/IvF,mBAAA,CAKK,MALLwF,YAKK,GAJAxF,mBAAA,CAAgE,aAA3DW,YAAA,CAAsDC,sBAAA;IAAzCC,EAAE,EAAC;EAAiB;sBAAC,MAAM,C;;QACxEb,mBAAA,CAAmE,aAA9DW,YAAA,CAAyDC,sBAAA;IAA5CC,EAAE,EAAC;EAAoB;sBAAC,MAAM,C;;QAChDb,mBAAA,CAAoE,aAA/DW,YAAA,CAA0DC,sBAAA;IAA7CC,EAAE,EAAC;EAAqB;sBAAC,MAAM,C;;YAI5Cb,mBAAA,CAQkB,MARlByF,YAQkB,GAPDC,YAA+I,EAC/I1F,mBAAA,CAKK,MALL2F,YAKK,GAJA3F,mBAAA,CAAmE,aAA9DW,YAAA,CAAyDC,sBAAA;IAA5CC,EAAE,EAAC;EAAoB;sBAAC,MAAM,C;;QAC3Eb,mBAAA,CAAsE,aAAjEW,YAAA,CAA4DC,sBAAA;IAA/CC,EAAE,EAAC;EAAuB;sBAAC,MAAM,C;;QACnDb,mBAAA,CAAuE,aAAlEW,YAAA,CAA6DC,sBAAA;IAAhDC,EAAE,EAAC;EAAwB;sBAAC,MAAM,C;;YAI/Cb,mBAAA,CAQkB,MARlB4F,YAQkB,GAPDC,YAA+I,EAC/I7F,mBAAA,CAKK,MALL8F,YAKK,GAJA9F,mBAAA,CAAoE,aAA/DW,YAAA,CAA0DC,sBAAA;IAA7CC,EAAE,EAAC;EAAqB;sBAAC,MAAM,C;;QAC5Eb,mBAAA,CAAuE,aAAlEW,YAAA,CAA6DC,sBAAA;IAAhDC,EAAE,EAAC;EAAwB;sBAAC,MAAM,C;;QACpDb,mBAAA,CAAwE,aAAnEW,YAAA,CAA8DC,sBAAA;IAAjDC,EAAE,EAAC;EAAyB;sBAAC,MAAM,C;;YAIhDb,mBAAA,CAQkB,MARlB+F,YAQkB,GAPDC,YAA8I,EAC9IhG,mBAAA,CAKK,MALLiG,YAKK,GAJAjG,mBAAA,CAAuE,aAAlEW,YAAA,CAA6DC,sBAAA;IAAhDC,EAAE,EAAC;EAAyB;sBAAC,MAAK,C;;QAC/Eb,mBAAA,CAA0E,aAArEW,YAAA,CAAgEC,sBAAA;IAAnDC,EAAE,EAAC;EAA4B;sBAAC,MAAK,C;;QACvDb,mBAAA,CAA2E,aAAtEW,YAAA,CAAiEC,sBAAA;IAApDC,EAAE,EAAC;EAA6B;sBAAC,MAAK,C;;YAInDb,mBAAA,CAOkB,MAPlBkG,YAOkB,GANDC,YAA+I,EAC/InG,mBAAA,CAIK,MAJLoG,YAIK,GAHApG,mBAAA,CAA+D,aAA1DW,YAAA,CAAqDC,sBAAA;IAAxCC,EAAE,EAAC;EAAgB;sBAAC,MAAM,C;;QACvEb,mBAAA,CAAkE,aAA7DW,YAAA,CAAwDC,sBAAA;IAA3CC,EAAE,EAAC;EAAmB;sBAAC,MAAM,C;;YAI1Cb,mBAAA,CAOkB,MAPlBqG,YAOkB,GANDC,YAA8I,EAC9ItG,mBAAA,CAIK,MAJLuG,YAIK,GAHAvG,mBAAA,CAAgE,aAA3DW,YAAA,CAAsDC,sBAAA;IAAzCC,EAAE,EAAC;EAAkB;sBAAC,MAAK,C;;QACxEb,mBAAA,CAAmE,aAA9DW,YAAA,CAAyDC,sBAAA;IAA5CC,EAAE,EAAC;EAAqB;sBAAC,MAAK,C;;YAI3Cb,mBAAA,CAQkB,MARlBwG,YAQkB,GAPDC,YAA6I,EAC7IzG,mBAAA,CAKK,MALL0G,YAKK,GAJA1G,mBAAA,CAA0D,aAArDW,YAAA,CAAgDC,sBAAA;IAAnCC,EAAE,EAAC;EAAa;sBAAC,MAAI,C;;QAClEb,mBAAA,CAA6D,aAAxDW,YAAA,CAAmDC,sBAAA;IAAtCC,EAAE,EAAC;EAAgB;sBAAC,MAAI,C;;QAC1Cb,mBAAA,CAA6D,aAAxDW,YAAA,CAAmDC,sBAAA;IAAtCC,EAAE,EAAC;EAAc;sBAAC,MAAM,C;;YAIrCb,mBAAA,CAQkB,MARlB2G,YAQkB,GAPDC,YAA6I,EAC7I5G,mBAAA,CAKK,MALL6G,YAKK,GAJA7G,mBAAA,CAA+D,aAA1DW,YAAA,CAAqDC,sBAAA;IAAxCC,EAAE,EAAC;EAAkB;sBAAC,MAAI,C;;QACvEb,mBAAA,CAAkE,aAA7DW,YAAA,CAAwDC,sBAAA;IAA3CC,EAAE,EAAC;EAAqB;sBAAC,MAAI,C;;QAC/Cb,mBAAA,CAAmE,aAA9DW,YAAA,CAAyDC,sBAAA;IAA5CC,EAAE,EAAC;EAAsB;sBAAC,MAAI,C;;YAI3Cb,mBAAA,CAQkB,MARlB8G,YAQkB,GAPDC,YAA+I,EAC/I/G,mBAAA,CAKK,MALLgH,YAKK,GAJAhH,mBAAA,CAAiE,aAA5DW,YAAA,CAAuDC,sBAAA;IAA1CC,EAAE,EAAC;EAAkB;sBAAC,MAAM,C;;QACzEb,mBAAA,CAAoE,aAA/DW,YAAA,CAA0DC,sBAAA;IAA7CC,EAAE,EAAC;EAAqB;sBAAC,MAAM,C;;QACjDb,mBAAA,CAAqE,aAAhEW,YAAA,CAA2DC,sBAAA;IAA9CC,EAAE,EAAC;EAAsB;sBAAC,MAAM,C;;YAI7Cb,mBAAA,CAQkB,MARlBiH,YAQkB,GAPDC,YAA+I,EAC/IlH,mBAAA,CAKK,MALLmH,YAKK,GAJAnH,mBAAA,CAA4D,aAAvDW,YAAA,CAAkDC,sBAAA;IAArCC,EAAE,EAAC;EAAa;sBAAC,MAAM,C;;QACpEb,mBAAA,CAA+D,aAA1DW,YAAA,CAAqDC,sBAAA;IAAxCC,EAAE,EAAC;EAAgB;sBAAC,MAAM,C;;QAC5Cb,mBAAA,CAA6D,aAAxDW,YAAA,CAAmDC,sBAAA;IAAtCC,EAAE,EAAC;EAAc;sBAAC,MAAM,C;;YAIrCb,mBAAA,CAOkB,MAPlBoH,YAOkB,GANDC,YAA6I,EAC7IrH,mBAAA,CAIK,MAJLsH,YAIK,GAHAtH,mBAAA,CAA4D,aAAvDW,YAAA,CAAkDC,sBAAA;IAArCC,EAAE,EAAC;EAAe;sBAAC,MAAI,C;;QACpEb,mBAAA,CAA+D,aAA1DW,YAAA,CAAqDC,sBAAA;IAAxCC,EAAE,EAAC;EAAkB;sBAAC,MAAI,C;;YAIvCb,mBAAA,CASkB,MATlBuH,YASkB,GARDC,YAA6I,EAC7IxH,mBAAA,CAMK,MANLyH,YAMK,GALAzH,mBAAA,CAAqD,aAAhDW,YAAA,CAA2CC,sBAAA;IAA9BC,EAAE,EAAC;EAAS;sBAAC,MAAG,C;;QAC7Db,mBAAA,CAAqD,aAAhDW,YAAA,CAA2CC,sBAAA;IAA9BC,EAAE,EAAC;EAAS;sBAAC,MAAG,C;;QAClCb,mBAAA,CAAqD,aAAhDW,YAAA,CAA2CC,sBAAA;IAA9BC,EAAE,EAAC;EAAS;sBAAC,MAAG,C;;QAClCb,mBAAA,CAAqD,aAAhDW,YAAA,CAA2CC,sBAAA;IAA9BC,EAAE,EAAC;EAAS;sBAAC,MAAG,C;;8CA5H8BqC,KAAA,CAAAC,IAAI,W,mBAoIhDnD,mBAAA,CA2ED,MA3EC0H,YA2ED,GA1EDC,YAEK,EACA3H,mBAAA,CAOA,MAPA4H,YAOA,GANDC,YAA6I,EAC7I7H,mBAAA,CAIK,MAJL8H,YAIK,GAF3B9H,mBAAA,CAAoE,aAA/DW,YAAA,CAA0DC,sBAAA;IAA7CC,EAAE,EAAC;EAAuB;sBAAC,MAAI,C;;YAKxBb,mBAAA,CAQF,MARE+H,YAQF,GAPDC,YAA+I,EAC/IhI,mBAAA,CAKK,MALLiI,YAKK,GAH3BjI,mBAAA,CAAuE,aAAlEW,YAAA,CAA6DC,sBAAA;IAAhDC,EAAE,EAAC;EAAwB;sBAAC,MAAM,C;;QACpDb,mBAAA,CAAwE,aAAnEW,YAAA,CAA8DC,sBAAA;IAAjDC,EAAE,EAAC;EAAyB;sBAAC,MAAM,C;;YAKhDb,mBAAA,CAQkB,MARlBkI,YAQkB,GAPDC,YAA+I,EAC/InI,mBAAA,CAKK,MALLoI,YAKK,GAH3BpI,mBAAA,CAAmE,aAA9DW,YAAA,CAAyDC,sBAAA;IAA5CC,EAAE,EAAC;EAAoB;sBAAC,MAAM,C;;QAChDb,mBAAA,CAAoE,aAA/DW,YAAA,CAA0DC,sBAAA;IAA7CC,EAAE,EAAC;EAAqB;sBAAC,MAAM,C;;YAKzBb,mBAAA,CAQD,MARCqI,YAQD,GAPDC,YAA+I,EAC/ItI,mBAAA,CAKK,MALLuI,YAKK,GAJAvI,mBAAA,CAAiE,aAA5DW,YAAA,CAAuDC,sBAAA;IAA1CC,EAAE,EAAC;EAAkB;sBAAC,MAAM,C;;QACzEb,mBAAA,CAAoE,aAA/DW,YAAA,CAA0DC,sBAAA;IAA7CC,EAAE,EAAC;EAAqB;sBAAC,MAAM,C;;QACjDb,mBAAA,CAAqE,aAAhEW,YAAA,CAA2DC,sBAAA;IAA9CC,EAAE,EAAC;EAAsB;sBAAC,MAAM,C;;YAK1Bb,mBAAA,CAOD,MAPCwI,YAOD,GANDC,YAA8I,EAC9IzI,mBAAA,CAIK,MAJL0I,YAIK,GAHA1I,mBAAA,CAAuE,aAAlEW,YAAA,CAA6DC,sBAAA;IAAhDC,EAAE,EAAC;EAAyB;sBAAC,MAAK,C;;QAC/Eb,mBAAA,CAA0E,aAArEW,YAAA,CAAgEC,sBAAA;IAAnDC,EAAE,EAAC;EAA4B;sBAAC,MAAK,C;;YAKlDb,mBAAA,CAOkB,MAPlB2I,YAOkB,GANDC,YAA+I,EAC/I5I,mBAAA,CAIK,MAJL6I,YAIK,GAHA7I,mBAAA,CAAmE,aAA9DW,YAAA,CAAyDC,sBAAA;IAA5CC,EAAE,EAAC;EAAoB;sBAAC,MAAM,C;;QAC3Eb,mBAAA,CAAsE,aAAjEW,YAAA,CAA4DC,sBAAA;IAA/CC,EAAE,EAAC;EAAuB;sBAAC,MAAM,C;;YAQPb,mBAAA,CAOrB,MAPqB8I,YAOrB,GANDC,YAA6I,EAC7I/I,mBAAA,CAIK,MAJLgJ,YAIK,GAHVhJ,mBAAA,CAA6D,aAAxDW,YAAA,CAAmDC,sBAAA;IAAtCC,EAAE,EAAC;EAAc;sBAAC,MAAM,C;;QAC3Db,mBAAA,CAAwD,aAAnDW,YAAA,CAA8CC,sBAAA;IAAjCC,EAAE,EAAC;EAAW;sBAAC,MAAI,C;;8CApE2BqC,KAAA,CAAAC,IAAI,Y"}]}