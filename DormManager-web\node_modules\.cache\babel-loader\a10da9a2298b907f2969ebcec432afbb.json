{"remainingRequest": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\B7839DormManager\\DormManager-web\\src\\components\\LeftMenu.vue?vue&type=template&id=edc10994&scoped=true", "dependencies": [{"path": "I:\\product4\\B7839DormManager\\DormManager-web\\src\\components\\LeftMenu.vue", "mtime": 1749044010880}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\babel.config.js", "mtime": 1749034452000}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749043011899}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749043012919}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749043013784}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749043011899}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749043013130}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["class", "_createElementVNode", "href", "_createElementBlock", "_hoisted_1", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_hoisted_5", "_hoisted_6", "_hoisted_7", "_hoisted_8", "_hoisted_9", "_createVNode", "_component_router_link", "to", "_hoisted_12", "_hoisted_13", "_hoisted_14", "_hoisted_17", "_hoisted_18", "_hoisted_19", "_hoisted_22", "_hoisted_23", "_hoisted_24", "_hoisted_27", "_hoisted_28", "_hoisted_29", "_hoisted_32", "_hoisted_33", "_hoisted_34", "_hoisted_37", "_hoisted_38", "_hoisted_39", "_hoisted_42", "_hoisted_43", "_hoisted_44", "_hoisted_49", "_hoisted_50", "_hoisted_51", "_hoisted_55", "_hoisted_56", "_hoisted_57", "_hoisted_61", "_hoisted_62", "_hoisted_63", "_hoisted_67", "_hoisted_68", "_hoisted_69", "_hoisted_73", "_hoisted_74", "_hoisted_75", "_hoisted_79", "_hoisted_80", "_hoisted_81", "$data", "role", "_hoisted_83", "_hoisted_84", "_hoisted_85", "_hoisted_86", "_hoisted_87", "_hoisted_91", "_hoisted_92", "_hoisted_93", "_hoisted_97", "_hoisted_98", "_hoisted_99", "_hoisted_103", "_hoisted_104", "_hoisted_105", "_hoisted_109", "_hoisted_110", "_hoisted_111", "_hoisted_115", "_hoisted_116", "_hoisted_117", "_hoisted_121", "_hoisted_122", "_hoisted_123", "_hoisted_126", "_hoisted_127", "_hoisted_128", "_hoisted_131", "_hoisted_132", "_hoisted_133", "_hoisted_137", "_hoisted_138", "_hoisted_139", "_hoisted_143", "_hoisted_144", "_hoisted_145", "_hoisted_149", "_hoisted_150", "_hoisted_151", "_hoisted_155", "_hoisted_156", "_hoisted_157", "_hoisted_160", "_hoisted_161", "_hoisted_162", "_hoisted_167", "_hoisted_168", "_hoisted_169", "_hoisted_170", "_hoisted_171", "_hoisted_175", "_hoisted_176", "_hoisted_177", "_hoisted_181", "_hoisted_182", "_hoisted_183", "_hoisted_187", "_hoisted_188", "_hoisted_189", "_hoisted_193", "_hoisted_194", "_hoisted_195", "_hoisted_199", "_hoisted_200", "_hoisted_201", "_hoisted_205", "_hoisted_206", "_hoisted_207", "_hoisted_210", "_hoisted_211", "_hoisted_212", "_hoisted_215", "_hoisted_216", "_hoisted_217", "_hoisted_221", "_hoisted_222", "_hoisted_223", "_hoisted_227", "_hoisted_228", "_hoisted_229", "_hoisted_233", "_hoisted_234", "_hoisted_235", "_hoisted_239", "_hoisted_240", "_hoisted_241", "_hoisted_244", "_hoisted_245", "_hoisted_246", "_hoisted_251", "_hoisted_252", "_hoisted_253", "_hoisted_254", "_hoisted_255", "_hoisted_259", "_hoisted_260", "_hoisted_261", "_hoisted_265", "_hoisted_266", "_hoisted_267", "_hoisted_271", "_hoisted_272", "_hoisted_273", "_hoisted_277", "_hoisted_278", "_hoisted_279", "_hoisted_283", "_hoisted_284", "_hoisted_285", "_hoisted_289", "_hoisted_290", "_hoisted_291", "_hoisted_294", "_hoisted_295", "_hoisted_296", "_hoisted_299", "_hoisted_300", "_hoisted_301", "_hoisted_305", "_hoisted_306", "_hoisted_307", "_hoisted_311", "_hoisted_312", "_hoisted_313", "_hoisted_317", "_hoisted_318", "_hoisted_319", "_hoisted_323", "_hoisted_324", "_hoisted_325", "_hoisted_328", "_hoisted_329", "_hoisted_330"], "sources": ["I:\\product4\\B7839DormManager\\DormManager-web\\src\\components\\LeftMenu.vue"], "sourcesContent": ["<template>\r\n <nav class=\"pcoded-navbar\">\r\n        <div class=\"navbar-wrapper\">\r\n            <div class=\"navbar-brand header-logo\">\r\n                <a href=\"/main\" class=\"b-brand\">\r\n                    <div class=\"b-bg\">\r\n                        <i class=\"feather icon-trending-up\"></i>\r\n                    </div>\r\n                    <span class=\"b-title\">宿舍管理系统</span>\r\n                </a>\r\n\r\n            </div>\r\n            <div class=\"navbar-content scroll-div\">\r\n                <ul class=\"nav pcoded-inner-navbar\" v-show=\"role == '管理员'\">\r\n                    <li class=\"nav-item pcoded-menu-caption\">\r\n                        <label>功能菜单</label>\r\n                    </li>\r\n\r\n                       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿舍楼管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/dormbuildingAdd\">添加宿舍楼</router-link></li>\r\n  <li> <router-link to=\"/dormbuildingManage\">管理宿舍楼</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                      <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿舍管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/dormitoryAdd\">添加宿舍</router-link></li>\r\n  <li> <router-link to=\"/dormitoryManage\">管理宿舍</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                         <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">学生管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/studentAdd\">添加学生</router-link></li>\r\n  <li> <router-link to=\"/studentManage\">管理学生</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                           <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿管阿姨管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/hostessAdd\">添加宿管阿姨</router-link></li>\r\n  <li> <router-link to=\"/hostessManage\">管理宿管阿姨</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                         <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">维修员管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/repairmenAdd\">添加维修员</router-link></li>\r\n  <li> <router-link to=\"/repairmenManage\">管理维修员</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n\r\n                         <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">公告管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/systemnoticesAdd\">添加公告</router-link></li>\r\n  <li> <router-link to=\"/systemnoticesManage\">管理公告</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                          <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿舍更换管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/dormitorychangeAdd\">添加宿舍更换</router-link></li>\r\n  <li> <router-link to=\"/dormitorychangeManage\">管理宿舍更换</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                     \r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">报修管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                         <li> <router-link to=\"/repairtypeAdd\">添加报修类型</router-link></li>\r\n  <li> <router-link to=\"/repairtypeManage\">管理报修类型</router-link></li>\r\n  <li> <router-link to=\"/repairordersManage\">管理报修</router-link></li>\r\n  <li> <router-link to=\"/repairordersManage2\">报修列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                    \r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">水电费管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/waterelectricityfeeAdd\">添加水电费</router-link></li>\r\n  <li> <router-link to=\"/waterelectricityfeeManage\">管理水电费</router-link></li>\r\n  <li> <router-link to=\"/waterelectricityfeeManage2\">水电费列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n    \r\n\r\n  \r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">离校登记管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/leaveschoolAdd\">添加离校登记</router-link></li>\r\n  <li> <router-link to=\"/leaveschoolManage\">管理离校登记</router-link></li>\r\n  <li> <router-link to=\"/leaveschoolManage2\">离校登记列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                     \r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">返校登记管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/registerinfoAdd\">添加返校登记</router-link></li>\r\n  <li> <router-link to=\"/registerinfoManage\">管理返校登记</router-link></li>\r\n  <li> <router-link to=\"/registerinfoManage2\">返校登记列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿舍评分管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/dormitoryscoreAdd\">添加宿舍评分</router-link></li>\r\n  <li> <router-link to=\"/dormitoryscoreManage\">管理宿舍评分</router-link></li>\r\n  <li> <router-link to=\"/dormitoryscoreManage2\">宿舍评分列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n \r\n    \r\n\r\n     \r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">统计报表</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/total1\">报修类型统计</router-link></li>\r\n  <li> <router-link to=\"/total2\">维修员维修统计</router-link></li>\r\n  <li> <router-link to=\"/total3\">宿舍评分统计</router-link></li>\r\n\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                      <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">系统管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                           \r\n  <li> <router-link to=\"/password\">修改密码</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n          \r\n                </ul>\r\n\r\n                      <ul class=\"nav pcoded-inner-navbar\" v-show=\"role == '学生'\">\r\n                    <li class=\"nav-item pcoded-menu-caption\">\r\n                        <label>功能菜单</label>\r\n                    </li>\r\n                         <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">公告管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/systemnoticesAdd\">添加公告</router-link></li>\r\n  <li> <router-link to=\"/systemnoticesManage\">管理公告</router-link></li>\r\n  <li> <router-link to=\"/systemnoticesManage2\">公告列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">维修员管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/repairmenAdd\">添加维修员</router-link></li>\r\n  <li> <router-link to=\"/repairmenManage\">管理维修员</router-link></li>\r\n  <li> <router-link to=\"/repairmenInfo\">修改个人信息</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">离校登记管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/leaveschoolAdd\">添加离校登记</router-link></li>\r\n  <li> <router-link to=\"/leaveschoolManage\">管理离校登记</router-link></li>\r\n  <li> <router-link to=\"/leaveschoolManage2\">离校登记列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿舍评分管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/dormitoryscoreAdd\">添加宿舍评分</router-link></li>\r\n  <li> <router-link to=\"/dormitoryscoreManage\">管理宿舍评分</router-link></li>\r\n  <li> <router-link to=\"/dormitoryscoreManage2\">宿舍评分列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿舍更换管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/dormitorychangeAdd\">添加宿舍更换</router-link></li>\r\n  <li> <router-link to=\"/dormitorychangeManage\">管理宿舍更换</router-link></li>\r\n  <li> <router-link to=\"/dormitorychangeManage2\">宿舍更换列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">水电费管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/waterelectricityfeeAdd\">添加水电费</router-link></li>\r\n  <li> <router-link to=\"/waterelectricityfeeManage\">管理水电费</router-link></li>\r\n  <li> <router-link to=\"/waterelectricityfeeManage2\">水电费列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">报修类型管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/repairtypeAdd\">添加报修类型</router-link></li>\r\n  <li> <router-link to=\"/repairtypeManage\">管理报修类型</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿舍楼管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/dormbuildingAdd\">添加宿舍楼</router-link></li>\r\n  <li> <router-link to=\"/dormbuildingManage\">管理宿舍楼</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">学生管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/studentAdd\">添加学生</router-link></li>\r\n  <li> <router-link to=\"/studentManage\">管理学生</router-link></li>\r\n  <li> <router-link to=\"/studentInfo\">修改个人信息</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">报修管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/repairordersAdd\">添加报修</router-link></li>\r\n  <li> <router-link to=\"/repairordersManage\">管理报修</router-link></li>\r\n  <li> <router-link to=\"/repairordersManage2\">报修列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">返校登记管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/registerinfoAdd\">添加返校登记</router-link></li>\r\n  <li> <router-link to=\"/registerinfoManage\">管理返校登记</router-link></li>\r\n  <li> <router-link to=\"/registerinfoManage2\">返校登记列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿管阿姨管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/hostessAdd\">添加宿管阿姨</router-link></li>\r\n  <li> <router-link to=\"/hostessManage\">管理宿管阿姨</router-link></li>\r\n  <li> <router-link to=\"/hostessInfo\">修改个人信息</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿舍管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/dormitoryAdd\">添加宿舍</router-link></li>\r\n  <li> <router-link to=\"/dormitoryManage\">管理宿舍</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">图表管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/total1\">图表1</router-link></li>\r\n  <li> <router-link to=\"/total2\">图表2</router-link></li>\r\n  <li> <router-link to=\"/total3\">图表3</router-link></li>\r\n  <li> <router-link to=\"/total4\">图表4</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n          \r\n                </ul>\r\n\r\n\r\n                      <ul class=\"nav pcoded-inner-navbar\" v-show=\"role == '维修员'\">\r\n                    <li class=\"nav-item pcoded-menu-caption\">\r\n                        <label>功能菜单</label>\r\n                    </li>\r\n                         <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">公告管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/systemnoticesAdd\">添加公告</router-link></li>\r\n  <li> <router-link to=\"/systemnoticesManage\">管理公告</router-link></li>\r\n  <li> <router-link to=\"/systemnoticesManage2\">公告列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">维修员管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/repairmenAdd\">添加维修员</router-link></li>\r\n  <li> <router-link to=\"/repairmenManage\">管理维修员</router-link></li>\r\n  <li> <router-link to=\"/repairmenInfo\">修改个人信息</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">离校登记管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/leaveschoolAdd\">添加离校登记</router-link></li>\r\n  <li> <router-link to=\"/leaveschoolManage\">管理离校登记</router-link></li>\r\n  <li> <router-link to=\"/leaveschoolManage2\">离校登记列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿舍评分管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/dormitoryscoreAdd\">添加宿舍评分</router-link></li>\r\n  <li> <router-link to=\"/dormitoryscoreManage\">管理宿舍评分</router-link></li>\r\n  <li> <router-link to=\"/dormitoryscoreManage2\">宿舍评分列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿舍更换管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/dormitorychangeAdd\">添加宿舍更换</router-link></li>\r\n  <li> <router-link to=\"/dormitorychangeManage\">管理宿舍更换</router-link></li>\r\n  <li> <router-link to=\"/dormitorychangeManage2\">宿舍更换列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">水电费管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/waterelectricityfeeAdd\">添加水电费</router-link></li>\r\n  <li> <router-link to=\"/waterelectricityfeeManage\">管理水电费</router-link></li>\r\n  <li> <router-link to=\"/waterelectricityfeeManage2\">水电费列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">报修类型管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/repairtypeAdd\">添加报修类型</router-link></li>\r\n  <li> <router-link to=\"/repairtypeManage\">管理报修类型</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿舍楼管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/dormbuildingAdd\">添加宿舍楼</router-link></li>\r\n  <li> <router-link to=\"/dormbuildingManage\">管理宿舍楼</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">学生管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/studentAdd\">添加学生</router-link></li>\r\n  <li> <router-link to=\"/studentManage\">管理学生</router-link></li>\r\n  <li> <router-link to=\"/studentInfo\">修改个人信息</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">报修管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/repairordersAdd\">添加报修</router-link></li>\r\n  <li> <router-link to=\"/repairordersManage\">管理报修</router-link></li>\r\n  <li> <router-link to=\"/repairordersManage2\">报修列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">返校登记管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/registerinfoAdd\">添加返校登记</router-link></li>\r\n  <li> <router-link to=\"/registerinfoManage\">管理返校登记</router-link></li>\r\n  <li> <router-link to=\"/registerinfoManage2\">返校登记列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿管阿姨管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/hostessAdd\">添加宿管阿姨</router-link></li>\r\n  <li> <router-link to=\"/hostessManage\">管理宿管阿姨</router-link></li>\r\n  <li> <router-link to=\"/hostessInfo\">修改个人信息</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿舍管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/dormitoryAdd\">添加宿舍</router-link></li>\r\n  <li> <router-link to=\"/dormitoryManage\">管理宿舍</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">图表管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/total1\">图表1</router-link></li>\r\n  <li> <router-link to=\"/total2\">图表2</router-link></li>\r\n  <li> <router-link to=\"/total3\">图表3</router-link></li>\r\n  <li> <router-link to=\"/total4\">图表4</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n          \r\n                </ul>\r\n\r\n                      <ul class=\"nav pcoded-inner-navbar\" v-show=\"role == '宿管阿姨'\">\r\n                    <li class=\"nav-item pcoded-menu-caption\">\r\n                        <label>功能菜单</label>\r\n                    </li>\r\n                         <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">公告管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/systemnoticesAdd\">添加公告</router-link></li>\r\n  <li> <router-link to=\"/systemnoticesManage\">管理公告</router-link></li>\r\n  <li> <router-link to=\"/systemnoticesManage2\">公告列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">维修员管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/repairmenAdd\">添加维修员</router-link></li>\r\n  <li> <router-link to=\"/repairmenManage\">管理维修员</router-link></li>\r\n  <li> <router-link to=\"/repairmenInfo\">修改个人信息</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">离校登记管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/leaveschoolAdd\">添加离校登记</router-link></li>\r\n  <li> <router-link to=\"/leaveschoolManage\">管理离校登记</router-link></li>\r\n  <li> <router-link to=\"/leaveschoolManage2\">离校登记列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿舍评分管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/dormitoryscoreAdd\">添加宿舍评分</router-link></li>\r\n  <li> <router-link to=\"/dormitoryscoreManage\">管理宿舍评分</router-link></li>\r\n  <li> <router-link to=\"/dormitoryscoreManage2\">宿舍评分列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿舍更换管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/dormitorychangeAdd\">添加宿舍更换</router-link></li>\r\n  <li> <router-link to=\"/dormitorychangeManage\">管理宿舍更换</router-link></li>\r\n  <li> <router-link to=\"/dormitorychangeManage2\">宿舍更换列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">水电费管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/waterelectricityfeeAdd\">添加水电费</router-link></li>\r\n  <li> <router-link to=\"/waterelectricityfeeManage\">管理水电费</router-link></li>\r\n  <li> <router-link to=\"/waterelectricityfeeManage2\">水电费列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">报修类型管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/repairtypeAdd\">添加报修类型</router-link></li>\r\n  <li> <router-link to=\"/repairtypeManage\">管理报修类型</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿舍楼管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/dormbuildingAdd\">添加宿舍楼</router-link></li>\r\n  <li> <router-link to=\"/dormbuildingManage\">管理宿舍楼</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">学生管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/studentAdd\">添加学生</router-link></li>\r\n  <li> <router-link to=\"/studentManage\">管理学生</router-link></li>\r\n  <li> <router-link to=\"/studentInfo\">修改个人信息</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">报修管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/repairordersAdd\">添加报修</router-link></li>\r\n  <li> <router-link to=\"/repairordersManage\">管理报修</router-link></li>\r\n  <li> <router-link to=\"/repairordersManage2\">报修列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">返校登记管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/registerinfoAdd\">添加返校登记</router-link></li>\r\n  <li> <router-link to=\"/registerinfoManage\">管理返校登记</router-link></li>\r\n  <li> <router-link to=\"/registerinfoManage2\">返校登记列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿管阿姨管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/hostessAdd\">添加宿管阿姨</router-link></li>\r\n  <li> <router-link to=\"/hostessManage\">管理宿管阿姨</router-link></li>\r\n  <li> <router-link to=\"/hostessInfo\">修改个人信息</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿舍管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/dormitoryAdd\">添加宿舍</router-link></li>\r\n  <li> <router-link to=\"/dormitoryManage\">管理宿舍</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">图表管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/total1\">图表1</router-link></li>\r\n  <li> <router-link to=\"/total2\">图表2</router-link></li>\r\n  <li> <router-link to=\"/total3\">图表3</router-link></li>\r\n  <li> <router-link to=\"/total4\">图表4</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n          \r\n                </ul>\r\n\r\n\r\n            </div>\r\n        </div>\r\n    </nav>\r\n    \r\n\r\n</template>\r\n\r\n\r\n<script>\r\nimport $ from 'jquery';\r\n\r\nexport default {\r\n  name: \"LeftMenu\",\r\n  data() {\r\n    return {\r\n      userLname: \"\",\r\n      role: \"\",\r\n      activeMenu: null, // 用于跟踪当前激活的菜单\r\n    };\r\n  },\r\n  watch: {\r\n    $route(to, from) {\r\n      this.activeMenu = to.name;\r\n      this.$nextTick(() => {\r\n        this.initializeMenu();\r\n      });\r\n    }\r\n  },\r\n  mounted() {\r\n    this.userLname = sessionStorage.getItem(\"userLname\");\r\n    this.role = sessionStorage.getItem(\"role\");\r\n    this.$nextTick(() => {\r\n      this.initializeMenu();\r\n    });\r\n  },\r\n  methods: {\r\n    initializeMenu() {\r\n      $('.nav-item.pcoded-hasmenu > a').off('click').on('click', function(e) {\r\n        e.preventDefault();\r\n        const $parent = $(this).parent();\r\n\r\n        if ($parent.hasClass('pcoded-trigger')) {\r\n          $parent.removeClass('pcoded-trigger');\r\n          $parent.children('ul').slideUp();\r\n        } else {\r\n          $('.nav-item.pcoded-hasmenu').removeClass('pcoded-trigger');\r\n          $('.nav-item.pcoded-hasmenu > ul').slideUp();\r\n          $parent.addClass('pcoded-trigger');\r\n          $parent.children('ul').slideDown();\r\n        }\r\n      });\r\n\r\n      // 初始化：根据当前路由展开对应的菜单\r\n      const currentPath = this.$route.path;\r\n      $('.pcoded-submenu a').each(function() {\r\n        if ($(this).attr('href') === currentPath) {\r\n          $(this).parents('.nav-item.pcoded-hasmenu').addClass('pcoded-trigger');\r\n          $(this).parents('ul.pcoded-submenu').show();\r\n        }\r\n      });\r\n    },\r\n\r\n    exit: function () {\r\n      var _this = this;\r\n      this.$confirm(\"确认退出吗?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n          .then(() => {\r\n            sessionStorage.removeItem(\"userLname\");\r\n            sessionStorage.removeItem(\"role\");\r\n            _this.$router.push(\"/\");\r\n          })\r\n          .catch(() => { });\r\n    },\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.example-showcase .el-dropdown-link {\r\n  cursor: pointer;\r\n  color:green;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.pcoded-submenu a{\r\n  text-decoration: none;\r\n  font-size: 14px;\r\n}\r\n\r\n/*加点击效果*/\r\n.pcoded-submenu a:hover{\r\n  color: #fff;\r\n  color: #ff6600;\r\n}\r\n</style>\r\n\r\n"], "mappings": ";;;EACMA,KAAK,EAAC;AAAe;;EACdA,KAAK,EAAC;AAAgB;;;EAUlBA,KAAK,EAAC;AAA2B;;EAC9BA,KAAK,EAAC;AAAyB;gEAC/BC,mBAAA,CAEK;EAFDD,KAAK,EAAC;AAA8B,I,aACpCC,mBAAA,CAAmB,eAAZ,MAAI,E;;EAGPD,KAAK,EAAC;AAAyB;gEACnCC,mBAAA,CAA8I;EAA3IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAuC;EAAjCD,KAAK,EAAC;AAAc,GAAC,OAAK,E;;EAC/HA,KAAK,EAAC;AAAgB;kDACmB,OAAK;kDAC7B,OAAK;;EAKvBA,KAAK,EAAC;AAAyB;iEAClCC,mBAAA,CAA6I;EAA1IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAsC;EAAhCD,KAAK,EAAC;AAAc,GAAC,MAAI,E;;EAC9HA,KAAK,EAAC;AAAgB;kDACgB,MAAI;kDAC5B,MAAI;;EAKhBA,KAAK,EAAC;AAAyB;iEACrCC,mBAAA,CAA6I;EAA1IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAsC;EAAhCD,KAAK,EAAC;AAAc,GAAC,MAAI,E;;EAC9HA,KAAK,EAAC;AAAgB;kDACc,MAAI;kDAC5B,MAAI;;EAKZA,KAAK,EAAC;AAAyB;iEACvCC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;kDACc,QAAM;kDAC9B,QAAM;;EAKhBA,KAAK,EAAC;AAAyB;iEACrCC,mBAAA,CAA8I;EAA3IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAuC;EAAjCD,KAAK,EAAC;AAAc,GAAC,OAAK,E;;EAC/HA,KAAK,EAAC;AAAgB;kDACgB,OAAK;kDAC7B,OAAK;;EAMjBA,KAAK,EAAC;AAAyB;iEACrCC,mBAAA,CAA6I;EAA1IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAsC;EAAhCD,KAAK,EAAC;AAAc,GAAC,MAAI,E;;EAC9HA,KAAK,EAAC;AAAgB;kDACoB,MAAI;kDAC5B,MAAI;;EAKnBA,KAAK,EAAC;AAAyB;iEACtCC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;kDACsB,QAAM;kDAC9B,QAAM;;EAM1CA,KAAK,EAAC;AAAyB;iEACnBC,mBAAA,CAA6I;EAA1IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAsC;EAAhCD,KAAK,EAAC;AAAc,GAAC,MAAI,E;;EAC9HA,KAAK,EAAC;AAAgB;kDACa,QAAM;kDAC1B,QAAM;kDACJ,MAAI;kDACH,MAAI;;EAMtCA,KAAK,EAAC;AAAyB;iEACnBC,mBAAA,CAA8I;EAA3IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAuC;EAAjCD,KAAK,EAAC;AAAc,GAAC,OAAK,E;;EAC/HA,KAAK,EAAC;AAAgB;kDAC0B,OAAK;kDAC7B,OAAK;kDACJ,OAAK;;EAO9CA,KAAK,EAAC;AAAyB;iEACnBC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;kDACkB,QAAM;kDAC9B,QAAM;kDACL,QAAM;;EAMvCA,KAAK,EAAC;AAAyB;iEACnBC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;kDACmB,QAAM;kDAC9B,QAAM;kDACL,QAAM;;EAMxCA,KAAK,EAAC;AAAyB;iEACnBC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;kDACqB,QAAM;kDAC9B,QAAM;kDACL,QAAM;;EAQ1CA,KAAK,EAAC;AAAyB;iEACnBC,mBAAA,CAA6I;EAA1IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAsC;EAAhCD,KAAK,EAAC;AAAc,GAAC,MAAI,E;;EAC9HA,KAAK,EAAC;AAAgB;kDACU,QAAM;kDACjC,SAAO;kDACP,QAAM;;EAMZA,KAAK,EAAC;AAAyB;iEAClCC,mBAAA,CAA6I;EAA1IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAsC;EAAhCD,KAAK,EAAC;AAAc,GAAC,MAAI,E;;EAC9HA,KAAK,EAAC;AAAgB;kDAEf,MAAI;;EAQbA,KAAK,EAAC;AAAyB;iEACrCC,mBAAA,CAEK;EAFDD,KAAK,EAAC;AAA8B,I,aACpCC,mBAAA,CAAmB,eAAZ,MAAI,E;;EAELD,KAAK,EAAC;AAAyB;iEACrCC,mBAAA,CAA6I;EAA1IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAsC;EAAhCD,KAAK,EAAC;AAAc,GAAC,MAAI,E;;EAC9HA,KAAK,EAAC;AAAgB;kDACoB,MAAI;kDAC5B,MAAI;kDACH,MAAI;;EAIvCA,KAAK,EAAC;AAAyB;iEACnBC,mBAAA,CAA8I;EAA3IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAuC;EAAjCD,KAAK,EAAC;AAAc,GAAC,OAAK,E;;EAC/HA,KAAK,EAAC;AAAgB;kDACgB,OAAK;kDAC7B,OAAK;kDACP,QAAM;;EAIlCA,KAAK,EAAC;AAAyB;iEACnBC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;mDACkB,QAAM;mDAC9B,QAAM;mDACL,QAAM;;EAIvCA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;mDACqB,QAAM;mDAC9B,QAAM;mDACL,QAAM;;EAI1CA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;mDACsB,QAAM;mDAC9B,QAAM;mDACL,QAAM;;EAI3CA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA8I;EAA3IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAuC;EAAjCD,KAAK,EAAC;AAAc,GAAC,OAAK,E;;EAC/HA,KAAK,EAAC;AAAgB;mDAC0B,OAAK;mDAC7B,OAAK;mDACJ,OAAK;;EAI9CA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;mDACiB,QAAM;mDAC9B,QAAM;;EAIrCA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA8I;EAA3IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAuC;EAAjCD,KAAK,EAAC;AAAc,GAAC,OAAK,E;;EAC/HA,KAAK,EAAC;AAAgB;mDACmB,OAAK;mDAC7B,OAAK;;EAItCA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA6I;EAA1IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAsC;EAAhCD,KAAK,EAAC;AAAc,GAAC,MAAI,E;;EAC9HA,KAAK,EAAC;AAAgB;mDACc,MAAI;mDAC5B,MAAI;mDACN,QAAM;;EAIhCA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA6I;EAA1IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAsC;EAAhCD,KAAK,EAAC;AAAc,GAAC,MAAI,E;;EAC9HA,KAAK,EAAC;AAAgB;mDACmB,MAAI;mDAC5B,MAAI;mDACH,MAAI;;EAItCA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;mDACmB,QAAM;mDAC9B,QAAM;mDACL,QAAM;;EAIxCA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;mDACc,QAAM;mDAC9B,QAAM;mDACR,QAAM;;EAIhCA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA6I;EAA1IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAsC;EAAhCD,KAAK,EAAC;AAAc,GAAC,MAAI,E;;EAC9HA,KAAK,EAAC;AAAgB;mDACgB,MAAI;mDAC5B,MAAI;;EAIlCA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA6I;EAA1IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAsC;EAAhCD,KAAK,EAAC;AAAc,GAAC,MAAI,E;;EAC9HA,KAAK,EAAC;AAAgB;mDACU,KAAG;mDAC9B,KAAG;mDACH,KAAG;mDACH,KAAG;;EASVA,KAAK,EAAC;AAAyB;kEACrCC,mBAAA,CAEK;EAFDD,KAAK,EAAC;AAA8B,I,aACpCC,mBAAA,CAAmB,eAAZ,MAAI,E;;EAELD,KAAK,EAAC;AAAyB;kEACrCC,mBAAA,CAA6I;EAA1IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAsC;EAAhCD,KAAK,EAAC;AAAc,GAAC,MAAI,E;;EAC9HA,KAAK,EAAC;AAAgB;mDACoB,MAAI;mDAC5B,MAAI;mDACH,MAAI;;EAIvCA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA8I;EAA3IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAuC;EAAjCD,KAAK,EAAC;AAAc,GAAC,OAAK,E;;EAC/HA,KAAK,EAAC;AAAgB;mDACgB,OAAK;mDAC7B,OAAK;mDACP,QAAM;;EAIlCA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;mDACkB,QAAM;mDAC9B,QAAM;mDACL,QAAM;;EAIvCA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;mDACqB,QAAM;mDAC9B,QAAM;mDACL,QAAM;;EAI1CA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;mDACsB,QAAM;mDAC9B,QAAM;mDACL,QAAM;;EAI3CA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA8I;EAA3IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAuC;EAAjCD,KAAK,EAAC;AAAc,GAAC,OAAK,E;;EAC/HA,KAAK,EAAC;AAAgB;mDAC0B,OAAK;mDAC7B,OAAK;mDACJ,OAAK;;EAI9CA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;mDACiB,QAAM;mDAC9B,QAAM;;EAIrCA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA8I;EAA3IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAuC;EAAjCD,KAAK,EAAC;AAAc,GAAC,OAAK,E;;EAC/HA,KAAK,EAAC;AAAgB;mDACmB,OAAK;mDAC7B,OAAK;;EAItCA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA6I;EAA1IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAsC;EAAhCD,KAAK,EAAC;AAAc,GAAC,MAAI,E;;EAC9HA,KAAK,EAAC;AAAgB;mDACc,MAAI;mDAC5B,MAAI;mDACN,QAAM;;EAIhCA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA6I;EAA1IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAsC;EAAhCD,KAAK,EAAC;AAAc,GAAC,MAAI,E;;EAC9HA,KAAK,EAAC;AAAgB;mDACmB,MAAI;mDAC5B,MAAI;mDACH,MAAI;;EAItCA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;mDACmB,QAAM;mDAC9B,QAAM;mDACL,QAAM;;EAIxCA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;mDACc,QAAM;mDAC9B,QAAM;mDACR,QAAM;;EAIhCA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA6I;EAA1IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAsC;EAAhCD,KAAK,EAAC;AAAc,GAAC,MAAI,E;;EAC9HA,KAAK,EAAC;AAAgB;mDACgB,MAAI;mDAC5B,MAAI;;EAIlCA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA6I;EAA1IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAsC;EAAhCD,KAAK,EAAC;AAAc,GAAC,MAAI,E;;EAC9HA,KAAK,EAAC;AAAgB;mDACU,KAAG;mDAC9B,KAAG;mDACH,KAAG;mDACH,KAAG;;EAQVA,KAAK,EAAC;AAAyB;kEACrCC,mBAAA,CAEK;EAFDD,KAAK,EAAC;AAA8B,I,aACpCC,mBAAA,CAAmB,eAAZ,MAAI,E;;EAELD,KAAK,EAAC;AAAyB;kEACrCC,mBAAA,CAA6I;EAA1IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAsC;EAAhCD,KAAK,EAAC;AAAc,GAAC,MAAI,E;;EAC9HA,KAAK,EAAC;AAAgB;mDACoB,MAAI;mDAC5B,MAAI;mDACH,MAAI;;EAIvCA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA8I;EAA3IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAuC;EAAjCD,KAAK,EAAC;AAAc,GAAC,OAAK,E;;EAC/HA,KAAK,EAAC;AAAgB;mDACgB,OAAK;mDAC7B,OAAK;mDACP,QAAM;;EAIlCA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;mDACkB,QAAM;mDAC9B,QAAM;mDACL,QAAM;;EAIvCA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;mDACqB,QAAM;mDAC9B,QAAM;mDACL,QAAM;;EAI1CA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;mDACsB,QAAM;mDAC9B,QAAM;mDACL,QAAM;;EAI3CA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA8I;EAA3IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAuC;EAAjCD,KAAK,EAAC;AAAc,GAAC,OAAK,E;;EAC/HA,KAAK,EAAC;AAAgB;mDAC0B,OAAK;mDAC7B,OAAK;mDACJ,OAAK;;EAI9CA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;mDACiB,QAAM;mDAC9B,QAAM;;EAIrCA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA8I;EAA3IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAuC;EAAjCD,KAAK,EAAC;AAAc,GAAC,OAAK,E;;EAC/HA,KAAK,EAAC;AAAgB;mDACmB,OAAK;mDAC7B,OAAK;;EAItCA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA6I;EAA1IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAsC;EAAhCD,KAAK,EAAC;AAAc,GAAC,MAAI,E;;EAC9HA,KAAK,EAAC;AAAgB;mDACc,MAAI;mDAC5B,MAAI;mDACN,QAAM;;EAIhCA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA6I;EAA1IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAsC;EAAhCD,KAAK,EAAC;AAAc,GAAC,MAAI,E;;EAC9HA,KAAK,EAAC;AAAgB;mDACmB,MAAI;mDAC5B,MAAI;mDACH,MAAI;;EAItCA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;mDACmB,QAAM;mDAC9B,QAAM;mDACL,QAAM;;EAIxCA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;mDACc,QAAM;mDAC9B,QAAM;mDACR,QAAM;;EAIhCA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA6I;EAA1IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAsC;EAAhCD,KAAK,EAAC;AAAc,GAAC,MAAI,E;;EAC9HA,KAAK,EAAC;AAAgB;mDACgB,MAAI;mDAC5B,MAAI;;EAIlCA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA6I;EAA1IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAsC;EAAhCD,KAAK,EAAC;AAAc,GAAC,MAAI,E;;EAC9HA,KAAK,EAAC;AAAgB;mDACU,KAAG;mDAC9B,KAAG;mDACH,KAAG;mDACH,KAAG;;;uBAziBnCG,mBAAA,CAojBS,OApjBTC,UAojBS,GAnjBFH,mBAAA,CAkjBM,OAljBNI,UAkjBM,GAjjBFC,UAQM,EACNL,mBAAA,CAuiBM,OAviBNM,UAuiBM,G,gBAtiBFN,mBAAA,CAsJK,MAtJLO,UAsJK,GArJDC,UAEK,EAEFR,mBAAA,CAOE,MAPFS,UAOE,GANDC,UAA8I,EAC9IV,mBAAA,CAIK,MAJLW,UAIK,GAHAX,mBAAA,CAAgE,aAA3DY,YAAA,CAAsDC,sBAAA;IAAzCC,EAAE,EAAC;EAAkB;sBAAC,MAAK,C;;QACxEd,mBAAA,CAAmE,aAA9DY,YAAA,CAAyDC,sBAAA;IAA5CC,EAAE,EAAC;EAAqB;sBAAC,MAAK,C;;YAK5Bd,mBAAA,CAOG,MAPHe,WAOG,GANDC,WAA6I,EAC7IhB,mBAAA,CAIK,MAJLiB,WAIK,GAHAjB,mBAAA,CAA4D,aAAvDY,YAAA,CAAkDC,sBAAA;IAArCC,EAAE,EAAC;EAAe;sBAAC,MAAI,C;;QACpEd,mBAAA,CAA+D,aAA1DY,YAAA,CAAqDC,sBAAA;IAAxCC,EAAE,EAAC;EAAkB;sBAAC,MAAI,C;;YAKrBd,mBAAA,CAOA,MAPAkB,WAOA,GANDC,WAA6I,EAC7InB,mBAAA,CAIK,MAJLoB,WAIK,GAHApB,mBAAA,CAA0D,aAArDY,YAAA,CAAgDC,sBAAA;IAAnCC,EAAE,EAAC;EAAa;sBAAC,MAAI,C;;QAClEd,mBAAA,CAA6D,aAAxDY,YAAA,CAAmDC,sBAAA;IAAtCC,EAAE,EAAC;EAAgB;sBAAC,MAAI,C;;YAKjBd,mBAAA,CAOF,MAPEqB,WAOF,GANDC,WAA+I,EAC/ItB,mBAAA,CAIK,MAJLuB,WAIK,GAHAvB,mBAAA,CAA4D,aAAvDY,YAAA,CAAkDC,sBAAA;IAArCC,EAAE,EAAC;EAAa;sBAAC,MAAM,C;;QACpEd,mBAAA,CAA+D,aAA1DY,YAAA,CAAqDC,sBAAA;IAAxCC,EAAE,EAAC;EAAgB;sBAAC,MAAM,C;;YAKrBd,mBAAA,CAOA,MAPAwB,WAOA,GANDC,WAA8I,EAC9IzB,mBAAA,CAIK,MAJL0B,WAIK,GAHA1B,mBAAA,CAA6D,aAAxDY,YAAA,CAAmDC,sBAAA;IAAtCC,EAAE,EAAC;EAAe;sBAAC,MAAK,C;;QACrEd,mBAAA,CAAgE,aAA3DY,YAAA,CAAsDC,sBAAA;IAAzCC,EAAE,EAAC;EAAkB;sBAAC,MAAK,C;;YAMtBd,mBAAA,CAOA,MAPA2B,WAOA,GANDC,WAA6I,EAC7I5B,mBAAA,CAIK,MAJL6B,WAIK,GAHA7B,mBAAA,CAAgE,aAA3DY,YAAA,CAAsDC,sBAAA;IAAzCC,EAAE,EAAC;EAAmB;sBAAC,MAAI,C;;QACxEd,mBAAA,CAAmE,aAA9DY,YAAA,CAAyDC,sBAAA;IAA5CC,EAAE,EAAC;EAAsB;sBAAC,MAAI,C;;YAKxBd,mBAAA,CAOD,MAPC8B,WAOD,GANDC,WAA+I,EAC/I/B,mBAAA,CAIK,MAJLgC,WAIK,GAHAhC,mBAAA,CAAoE,aAA/DY,YAAA,CAA0DC,sBAAA;IAA7CC,EAAE,EAAC;EAAqB;sBAAC,MAAM,C;;QAC5Ed,mBAAA,CAAuE,aAAlEY,YAAA,CAA6DC,sBAAA;IAAhDC,EAAE,EAAC;EAAwB;sBAAC,MAAM,C;;YAM/Cd,mBAAA,CASkB,MATlBiC,WASkB,GARDC,WAA6I,EAC7IlC,mBAAA,CAMK,MANLmC,WAMK,GALJnC,mBAAA,CAA+D,aAA1DY,YAAA,CAAqDC,sBAAA;IAAxCC,EAAE,EAAC;EAAgB;sBAAC,MAAM,C;;QACnEd,mBAAA,CAAkE,aAA7DY,YAAA,CAAwDC,sBAAA;IAA3CC,EAAE,EAAC;EAAmB;sBAAC,MAAM,C;;QAC/Cd,mBAAA,CAAkE,aAA7DY,YAAA,CAAwDC,sBAAA;IAA3CC,EAAE,EAAC;EAAqB;sBAAC,MAAI,C;;QAC/Cd,mBAAA,CAAmE,aAA9DY,YAAA,CAAyDC,sBAAA;IAA5CC,EAAE,EAAC;EAAsB;sBAAC,MAAI,C;;YAM3Cd,mBAAA,CAQkB,MARlBoC,WAQkB,GAPDC,WAA8I,EAC9IrC,mBAAA,CAKK,MALLsC,WAKK,GAJAtC,mBAAA,CAAuE,aAAlEY,YAAA,CAA6DC,sBAAA;IAAhDC,EAAE,EAAC;EAAyB;sBAAC,MAAK,C;;QAC/Ed,mBAAA,CAA0E,aAArEY,YAAA,CAAgEC,sBAAA;IAAnDC,EAAE,EAAC;EAA4B;sBAAC,MAAK,C;;QACvDd,mBAAA,CAA2E,aAAtEY,YAAA,CAAiEC,sBAAA;IAApDC,EAAE,EAAC;EAA6B;sBAAC,MAAK,C;;YAOnDd,mBAAA,CAQkB,MARlBuC,WAQkB,GAPDC,WAA+I,EAC/IxC,mBAAA,CAKK,MALLyC,WAKK,GAJAzC,mBAAA,CAAgE,aAA3DY,YAAA,CAAsDC,sBAAA;IAAzCC,EAAE,EAAC;EAAiB;sBAAC,MAAM,C;;QACxEd,mBAAA,CAAmE,aAA9DY,YAAA,CAAyDC,sBAAA;IAA5CC,EAAE,EAAC;EAAoB;sBAAC,MAAM,C;;QAChDd,mBAAA,CAAoE,aAA/DY,YAAA,CAA0DC,sBAAA;IAA7CC,EAAE,EAAC;EAAqB;sBAAC,MAAM,C;;YAM5Cd,mBAAA,CAQkB,MARlB0C,WAQkB,GAPDC,WAA+I,EAC/I3C,mBAAA,CAKK,MALL4C,WAKK,GAJA5C,mBAAA,CAAiE,aAA5DY,YAAA,CAAuDC,sBAAA;IAA1CC,EAAE,EAAC;EAAkB;sBAAC,MAAM,C;;QACzEd,mBAAA,CAAoE,aAA/DY,YAAA,CAA0DC,sBAAA;IAA7CC,EAAE,EAAC;EAAqB;sBAAC,MAAM,C;;QACjDd,mBAAA,CAAqE,aAAhEY,YAAA,CAA2DC,sBAAA;IAA9CC,EAAE,EAAC;EAAsB;sBAAC,MAAM,C;;YAM7Cd,mBAAA,CAQkB,MARlB6C,WAQkB,GAPDC,WAA+I,EAC/I9C,mBAAA,CAKK,MALL+C,WAKK,GAJA/C,mBAAA,CAAmE,aAA9DY,YAAA,CAAyDC,sBAAA;IAA5CC,EAAE,EAAC;EAAoB;sBAAC,MAAM,C;;QAC3Ed,mBAAA,CAAsE,aAAjEY,YAAA,CAA4DC,sBAAA;IAA/CC,EAAE,EAAC;EAAuB;sBAAC,MAAM,C;;QACnDd,mBAAA,CAAuE,aAAlEY,YAAA,CAA6DC,sBAAA;IAAhDC,EAAE,EAAC;EAAwB;sBAAC,MAAM,C;;YAQ/Cd,mBAAA,CASkB,MATlBgD,WASkB,GARDC,WAA6I,EAC7IjD,mBAAA,CAMK,MANLkD,WAMK,GALAlD,mBAAA,CAAwD,aAAnDY,YAAA,CAA8CC,sBAAA;IAAjCC,EAAE,EAAC;EAAS;sBAAC,MAAM,C;;QAChEd,mBAAA,CAAyD,aAApDY,YAAA,CAA+CC,sBAAA;IAAlCC,EAAE,EAAC;EAAS;sBAAC,MAAO,C;;QACtCd,mBAAA,CAAwD,aAAnDY,YAAA,CAA8CC,sBAAA;IAAjCC,EAAE,EAAC;EAAS;sBAAC,MAAM,C;;YAMjBd,mBAAA,CAOG,MAPHmD,WAOG,GANDC,WAA6I,EAC7IpD,mBAAA,CAIK,MAJLqD,WAIK,GAF3BrD,mBAAA,CAAwD,aAAnDY,YAAA,CAA8CC,sBAAA;IAAjCC,EAAE,EAAC;EAAW;sBAAC,MAAI,C;;8CAhJqBwC,KAAA,CAAAC,IAAI,W,mBAwJ1CvD,mBAAA,CAkID,MAlICwD,WAkID,GAjIDC,WAEK,EACAzD,mBAAA,CAQA,MARA0D,WAQA,GAPDC,WAA6I,EAC7I3D,mBAAA,CAKK,MALL4D,WAKK,GAJA5D,mBAAA,CAAgE,aAA3DY,YAAA,CAAsDC,sBAAA;IAAzCC,EAAE,EAAC;EAAmB;sBAAC,MAAI,C;;QACxEd,mBAAA,CAAmE,aAA9DY,YAAA,CAAyDC,sBAAA;IAA5CC,EAAE,EAAC;EAAsB;sBAAC,MAAI,C;;QAChDd,mBAAA,CAAoE,aAA/DY,YAAA,CAA0DC,sBAAA;IAA7CC,EAAE,EAAC;EAAuB;sBAAC,MAAI,C;;YAI5Cd,mBAAA,CAQkB,MARlB6D,WAQkB,GAPDC,WAA8I,EAC9I9D,mBAAA,CAKK,MALL+D,WAKK,GAJA/D,mBAAA,CAA6D,aAAxDY,YAAA,CAAmDC,sBAAA;IAAtCC,EAAE,EAAC;EAAe;sBAAC,MAAK,C;;QACrEd,mBAAA,CAAgE,aAA3DY,YAAA,CAAsDC,sBAAA;IAAzCC,EAAE,EAAC;EAAkB;sBAAC,MAAK,C;;QAC7Cd,mBAAA,CAA+D,aAA1DY,YAAA,CAAqDC,sBAAA;IAAxCC,EAAE,EAAC;EAAgB;sBAAC,MAAM,C;;YAIvCd,mBAAA,CAQkB,MARlBgE,WAQkB,GAPDC,WAA+I,EAC/IjE,mBAAA,CAKK,MALLkE,WAKK,GAJAlE,mBAAA,CAAgE,aAA3DY,YAAA,CAAsDC,sBAAA;IAAzCC,EAAE,EAAC;EAAiB;sBAAC,MAAM,C;;QACxEd,mBAAA,CAAmE,aAA9DY,YAAA,CAAyDC,sBAAA;IAA5CC,EAAE,EAAC;EAAoB;sBAAC,MAAM,C;;QAChDd,mBAAA,CAAoE,aAA/DY,YAAA,CAA0DC,sBAAA;IAA7CC,EAAE,EAAC;EAAqB;sBAAC,MAAM,C;;YAI5Cd,mBAAA,CAQkB,MARlBmE,YAQkB,GAPDC,YAA+I,EAC/IpE,mBAAA,CAKK,MALLqE,YAKK,GAJArE,mBAAA,CAAmE,aAA9DY,YAAA,CAAyDC,sBAAA;IAA5CC,EAAE,EAAC;EAAoB;sBAAC,MAAM,C;;QAC3Ed,mBAAA,CAAsE,aAAjEY,YAAA,CAA4DC,sBAAA;IAA/CC,EAAE,EAAC;EAAuB;sBAAC,MAAM,C;;QACnDd,mBAAA,CAAuE,aAAlEY,YAAA,CAA6DC,sBAAA;IAAhDC,EAAE,EAAC;EAAwB;sBAAC,MAAM,C;;YAI/Cd,mBAAA,CAQkB,MARlBsE,YAQkB,GAPDC,YAA+I,EAC/IvE,mBAAA,CAKK,MALLwE,YAKK,GAJAxE,mBAAA,CAAoE,aAA/DY,YAAA,CAA0DC,sBAAA;IAA7CC,EAAE,EAAC;EAAqB;sBAAC,MAAM,C;;QAC5Ed,mBAAA,CAAuE,aAAlEY,YAAA,CAA6DC,sBAAA;IAAhDC,EAAE,EAAC;EAAwB;sBAAC,MAAM,C;;QACpDd,mBAAA,CAAwE,aAAnEY,YAAA,CAA8DC,sBAAA;IAAjDC,EAAE,EAAC;EAAyB;sBAAC,MAAM,C;;YAIhDd,mBAAA,CAQkB,MARlByE,YAQkB,GAPDC,YAA8I,EAC9I1E,mBAAA,CAKK,MALL2E,YAKK,GAJA3E,mBAAA,CAAuE,aAAlEY,YAAA,CAA6DC,sBAAA;IAAhDC,EAAE,EAAC;EAAyB;sBAAC,MAAK,C;;QAC/Ed,mBAAA,CAA0E,aAArEY,YAAA,CAAgEC,sBAAA;IAAnDC,EAAE,EAAC;EAA4B;sBAAC,MAAK,C;;QACvDd,mBAAA,CAA2E,aAAtEY,YAAA,CAAiEC,sBAAA;IAApDC,EAAE,EAAC;EAA6B;sBAAC,MAAK,C;;YAInDd,mBAAA,CAOkB,MAPlB4E,YAOkB,GANDC,YAA+I,EAC/I7E,mBAAA,CAIK,MAJL8E,YAIK,GAHA9E,mBAAA,CAA+D,aAA1DY,YAAA,CAAqDC,sBAAA;IAAxCC,EAAE,EAAC;EAAgB;sBAAC,MAAM,C;;QACvEd,mBAAA,CAAkE,aAA7DY,YAAA,CAAwDC,sBAAA;IAA3CC,EAAE,EAAC;EAAmB;sBAAC,MAAM,C;;YAI1Cd,mBAAA,CAOkB,MAPlB+E,YAOkB,GANDC,YAA8I,EAC9IhF,mBAAA,CAIK,MAJLiF,YAIK,GAHAjF,mBAAA,CAAgE,aAA3DY,YAAA,CAAsDC,sBAAA;IAAzCC,EAAE,EAAC;EAAkB;sBAAC,MAAK,C;;QACxEd,mBAAA,CAAmE,aAA9DY,YAAA,CAAyDC,sBAAA;IAA5CC,EAAE,EAAC;EAAqB;sBAAC,MAAK,C;;YAI3Cd,mBAAA,CAQkB,MARlBkF,YAQkB,GAPDC,YAA6I,EAC7InF,mBAAA,CAKK,MALLoF,YAKK,GAJApF,mBAAA,CAA0D,aAArDY,YAAA,CAAgDC,sBAAA;IAAnCC,EAAE,EAAC;EAAa;sBAAC,MAAI,C;;QAClEd,mBAAA,CAA6D,aAAxDY,YAAA,CAAmDC,sBAAA;IAAtCC,EAAE,EAAC;EAAgB;sBAAC,MAAI,C;;QAC1Cd,mBAAA,CAA6D,aAAxDY,YAAA,CAAmDC,sBAAA;IAAtCC,EAAE,EAAC;EAAc;sBAAC,MAAM,C;;YAIrCd,mBAAA,CAQkB,MARlBqF,YAQkB,GAPDC,YAA6I,EAC7ItF,mBAAA,CAKK,MALLuF,YAKK,GAJAvF,mBAAA,CAA+D,aAA1DY,YAAA,CAAqDC,sBAAA;IAAxCC,EAAE,EAAC;EAAkB;sBAAC,MAAI,C;;QACvEd,mBAAA,CAAkE,aAA7DY,YAAA,CAAwDC,sBAAA;IAA3CC,EAAE,EAAC;EAAqB;sBAAC,MAAI,C;;QAC/Cd,mBAAA,CAAmE,aAA9DY,YAAA,CAAyDC,sBAAA;IAA5CC,EAAE,EAAC;EAAsB;sBAAC,MAAI,C;;YAI3Cd,mBAAA,CAQkB,MARlBwF,YAQkB,GAPDC,YAA+I,EAC/IzF,mBAAA,CAKK,MALL0F,YAKK,GAJA1F,mBAAA,CAAiE,aAA5DY,YAAA,CAAuDC,sBAAA;IAA1CC,EAAE,EAAC;EAAkB;sBAAC,MAAM,C;;QACzEd,mBAAA,CAAoE,aAA/DY,YAAA,CAA0DC,sBAAA;IAA7CC,EAAE,EAAC;EAAqB;sBAAC,MAAM,C;;QACjDd,mBAAA,CAAqE,aAAhEY,YAAA,CAA2DC,sBAAA;IAA9CC,EAAE,EAAC;EAAsB;sBAAC,MAAM,C;;YAI7Cd,mBAAA,CAQkB,MARlB2F,YAQkB,GAPDC,YAA+I,EAC/I5F,mBAAA,CAKK,MALL6F,YAKK,GAJA7F,mBAAA,CAA4D,aAAvDY,YAAA,CAAkDC,sBAAA;IAArCC,EAAE,EAAC;EAAa;sBAAC,MAAM,C;;QACpEd,mBAAA,CAA+D,aAA1DY,YAAA,CAAqDC,sBAAA;IAAxCC,EAAE,EAAC;EAAgB;sBAAC,MAAM,C;;QAC5Cd,mBAAA,CAA6D,aAAxDY,YAAA,CAAmDC,sBAAA;IAAtCC,EAAE,EAAC;EAAc;sBAAC,MAAM,C;;YAIrCd,mBAAA,CAOkB,MAPlB8F,YAOkB,GANDC,YAA6I,EAC7I/F,mBAAA,CAIK,MAJLgG,YAIK,GAHAhG,mBAAA,CAA4D,aAAvDY,YAAA,CAAkDC,sBAAA;IAArCC,EAAE,EAAC;EAAe;sBAAC,MAAI,C;;QACpEd,mBAAA,CAA+D,aAA1DY,YAAA,CAAqDC,sBAAA;IAAxCC,EAAE,EAAC;EAAkB;sBAAC,MAAI,C;;YAIvCd,mBAAA,CASkB,MATlBiG,YASkB,GARDC,YAA6I,EAC7IlG,mBAAA,CAMK,MANLmG,YAMK,GALAnG,mBAAA,CAAqD,aAAhDY,YAAA,CAA2CC,sBAAA;IAA9BC,EAAE,EAAC;EAAS;sBAAC,MAAG,C;;QAC7Dd,mBAAA,CAAqD,aAAhDY,YAAA,CAA2CC,sBAAA;IAA9BC,EAAE,EAAC;EAAS;sBAAC,MAAG,C;;QAClCd,mBAAA,CAAqD,aAAhDY,YAAA,CAA2CC,sBAAA;IAA9BC,EAAE,EAAC;EAAS;sBAAC,MAAG,C;;QAClCd,mBAAA,CAAqD,aAAhDY,YAAA,CAA2CC,sBAAA;IAA9BC,EAAE,EAAC;EAAS;sBAAC,MAAG,C;;8CA5H8BwC,KAAA,CAAAC,IAAI,U,mBAqIhDvD,mBAAA,CAkID,MAlICoG,YAkID,GAjIDC,YAEK,EACArG,mBAAA,CAQA,MARAsG,YAQA,GAPDC,YAA6I,EAC7IvG,mBAAA,CAKK,MALLwG,YAKK,GAJAxG,mBAAA,CAAgE,aAA3DY,YAAA,CAAsDC,sBAAA;IAAzCC,EAAE,EAAC;EAAmB;sBAAC,MAAI,C;;QACxEd,mBAAA,CAAmE,aAA9DY,YAAA,CAAyDC,sBAAA;IAA5CC,EAAE,EAAC;EAAsB;sBAAC,MAAI,C;;QAChDd,mBAAA,CAAoE,aAA/DY,YAAA,CAA0DC,sBAAA;IAA7CC,EAAE,EAAC;EAAuB;sBAAC,MAAI,C;;YAI5Cd,mBAAA,CAQkB,MARlByG,YAQkB,GAPDC,YAA8I,EAC9I1G,mBAAA,CAKK,MALL2G,YAKK,GAJA3G,mBAAA,CAA6D,aAAxDY,YAAA,CAAmDC,sBAAA;IAAtCC,EAAE,EAAC;EAAe;sBAAC,MAAK,C;;QACrEd,mBAAA,CAAgE,aAA3DY,YAAA,CAAsDC,sBAAA;IAAzCC,EAAE,EAAC;EAAkB;sBAAC,MAAK,C;;QAC7Cd,mBAAA,CAA+D,aAA1DY,YAAA,CAAqDC,sBAAA;IAAxCC,EAAE,EAAC;EAAgB;sBAAC,MAAM,C;;YAIvCd,mBAAA,CAQkB,MARlB4G,YAQkB,GAPDC,YAA+I,EAC/I7G,mBAAA,CAKK,MALL8G,YAKK,GAJA9G,mBAAA,CAAgE,aAA3DY,YAAA,CAAsDC,sBAAA;IAAzCC,EAAE,EAAC;EAAiB;sBAAC,MAAM,C;;QACxEd,mBAAA,CAAmE,aAA9DY,YAAA,CAAyDC,sBAAA;IAA5CC,EAAE,EAAC;EAAoB;sBAAC,MAAM,C;;QAChDd,mBAAA,CAAoE,aAA/DY,YAAA,CAA0DC,sBAAA;IAA7CC,EAAE,EAAC;EAAqB;sBAAC,MAAM,C;;YAI5Cd,mBAAA,CAQkB,MARlB+G,YAQkB,GAPDC,YAA+I,EAC/IhH,mBAAA,CAKK,MALLiH,YAKK,GAJAjH,mBAAA,CAAmE,aAA9DY,YAAA,CAAyDC,sBAAA;IAA5CC,EAAE,EAAC;EAAoB;sBAAC,MAAM,C;;QAC3Ed,mBAAA,CAAsE,aAAjEY,YAAA,CAA4DC,sBAAA;IAA/CC,EAAE,EAAC;EAAuB;sBAAC,MAAM,C;;QACnDd,mBAAA,CAAuE,aAAlEY,YAAA,CAA6DC,sBAAA;IAAhDC,EAAE,EAAC;EAAwB;sBAAC,MAAM,C;;YAI/Cd,mBAAA,CAQkB,MARlBkH,YAQkB,GAPDC,YAA+I,EAC/InH,mBAAA,CAKK,MALLoH,YAKK,GAJApH,mBAAA,CAAoE,aAA/DY,YAAA,CAA0DC,sBAAA;IAA7CC,EAAE,EAAC;EAAqB;sBAAC,MAAM,C;;QAC5Ed,mBAAA,CAAuE,aAAlEY,YAAA,CAA6DC,sBAAA;IAAhDC,EAAE,EAAC;EAAwB;sBAAC,MAAM,C;;QACpDd,mBAAA,CAAwE,aAAnEY,YAAA,CAA8DC,sBAAA;IAAjDC,EAAE,EAAC;EAAyB;sBAAC,MAAM,C;;YAIhDd,mBAAA,CAQkB,MARlBqH,YAQkB,GAPDC,YAA8I,EAC9ItH,mBAAA,CAKK,MALLuH,YAKK,GAJAvH,mBAAA,CAAuE,aAAlEY,YAAA,CAA6DC,sBAAA;IAAhDC,EAAE,EAAC;EAAyB;sBAAC,MAAK,C;;QAC/Ed,mBAAA,CAA0E,aAArEY,YAAA,CAAgEC,sBAAA;IAAnDC,EAAE,EAAC;EAA4B;sBAAC,MAAK,C;;QACvDd,mBAAA,CAA2E,aAAtEY,YAAA,CAAiEC,sBAAA;IAApDC,EAAE,EAAC;EAA6B;sBAAC,MAAK,C;;YAInDd,mBAAA,CAOkB,MAPlBwH,YAOkB,GANDC,YAA+I,EAC/IzH,mBAAA,CAIK,MAJL0H,YAIK,GAHA1H,mBAAA,CAA+D,aAA1DY,YAAA,CAAqDC,sBAAA;IAAxCC,EAAE,EAAC;EAAgB;sBAAC,MAAM,C;;QACvEd,mBAAA,CAAkE,aAA7DY,YAAA,CAAwDC,sBAAA;IAA3CC,EAAE,EAAC;EAAmB;sBAAC,MAAM,C;;YAI1Cd,mBAAA,CAOkB,MAPlB2H,YAOkB,GANDC,YAA8I,EAC9I5H,mBAAA,CAIK,MAJL6H,YAIK,GAHA7H,mBAAA,CAAgE,aAA3DY,YAAA,CAAsDC,sBAAA;IAAzCC,EAAE,EAAC;EAAkB;sBAAC,MAAK,C;;QACxEd,mBAAA,CAAmE,aAA9DY,YAAA,CAAyDC,sBAAA;IAA5CC,EAAE,EAAC;EAAqB;sBAAC,MAAK,C;;YAI3Cd,mBAAA,CAQkB,MARlB8H,YAQkB,GAPDC,YAA6I,EAC7I/H,mBAAA,CAKK,MALLgI,YAKK,GAJAhI,mBAAA,CAA0D,aAArDY,YAAA,CAAgDC,sBAAA;IAAnCC,EAAE,EAAC;EAAa;sBAAC,MAAI,C;;QAClEd,mBAAA,CAA6D,aAAxDY,YAAA,CAAmDC,sBAAA;IAAtCC,EAAE,EAAC;EAAgB;sBAAC,MAAI,C;;QAC1Cd,mBAAA,CAA6D,aAAxDY,YAAA,CAAmDC,sBAAA;IAAtCC,EAAE,EAAC;EAAc;sBAAC,MAAM,C;;YAIrCd,mBAAA,CAQkB,MARlBiI,YAQkB,GAPDC,YAA6I,EAC7IlI,mBAAA,CAKK,MALLmI,YAKK,GAJAnI,mBAAA,CAA+D,aAA1DY,YAAA,CAAqDC,sBAAA;IAAxCC,EAAE,EAAC;EAAkB;sBAAC,MAAI,C;;QACvEd,mBAAA,CAAkE,aAA7DY,YAAA,CAAwDC,sBAAA;IAA3CC,EAAE,EAAC;EAAqB;sBAAC,MAAI,C;;QAC/Cd,mBAAA,CAAmE,aAA9DY,YAAA,CAAyDC,sBAAA;IAA5CC,EAAE,EAAC;EAAsB;sBAAC,MAAI,C;;YAI3Cd,mBAAA,CAQkB,MARlBoI,YAQkB,GAPDC,YAA+I,EAC/IrI,mBAAA,CAKK,MALLsI,YAKK,GAJAtI,mBAAA,CAAiE,aAA5DY,YAAA,CAAuDC,sBAAA;IAA1CC,EAAE,EAAC;EAAkB;sBAAC,MAAM,C;;QACzEd,mBAAA,CAAoE,aAA/DY,YAAA,CAA0DC,sBAAA;IAA7CC,EAAE,EAAC;EAAqB;sBAAC,MAAM,C;;QACjDd,mBAAA,CAAqE,aAAhEY,YAAA,CAA2DC,sBAAA;IAA9CC,EAAE,EAAC;EAAsB;sBAAC,MAAM,C;;YAI7Cd,mBAAA,CAQkB,MARlBuI,YAQkB,GAPDC,YAA+I,EAC/IxI,mBAAA,CAKK,MALLyI,YAKK,GAJAzI,mBAAA,CAA4D,aAAvDY,YAAA,CAAkDC,sBAAA;IAArCC,EAAE,EAAC;EAAa;sBAAC,MAAM,C;;QACpEd,mBAAA,CAA+D,aAA1DY,YAAA,CAAqDC,sBAAA;IAAxCC,EAAE,EAAC;EAAgB;sBAAC,MAAM,C;;QAC5Cd,mBAAA,CAA6D,aAAxDY,YAAA,CAAmDC,sBAAA;IAAtCC,EAAE,EAAC;EAAc;sBAAC,MAAM,C;;YAIrCd,mBAAA,CAOkB,MAPlB0I,YAOkB,GANDC,YAA6I,EAC7I3I,mBAAA,CAIK,MAJL4I,YAIK,GAHA5I,mBAAA,CAA4D,aAAvDY,YAAA,CAAkDC,sBAAA;IAArCC,EAAE,EAAC;EAAe;sBAAC,MAAI,C;;QACpEd,mBAAA,CAA+D,aAA1DY,YAAA,CAAqDC,sBAAA;IAAxCC,EAAE,EAAC;EAAkB;sBAAC,MAAI,C;;YAIvCd,mBAAA,CASkB,MATlB6I,YASkB,GARDC,YAA6I,EAC7I9I,mBAAA,CAMK,MANL+I,YAMK,GALA/I,mBAAA,CAAqD,aAAhDY,YAAA,CAA2CC,sBAAA;IAA9BC,EAAE,EAAC;EAAS;sBAAC,MAAG,C;;QAC7Dd,mBAAA,CAAqD,aAAhDY,YAAA,CAA2CC,sBAAA;IAA9BC,EAAE,EAAC;EAAS;sBAAC,MAAG,C;;QAClCd,mBAAA,CAAqD,aAAhDY,YAAA,CAA2CC,sBAAA;IAA9BC,EAAE,EAAC;EAAS;sBAAC,MAAG,C;;QAClCd,mBAAA,CAAqD,aAAhDY,YAAA,CAA2CC,sBAAA;IAA9BC,EAAE,EAAC;EAAS;sBAAC,MAAG,C;;8CA5H8BwC,KAAA,CAAAC,IAAI,W,mBAoIhDvD,mBAAA,CAkID,MAlICgJ,YAkID,GAjIDC,YAEK,EACAjJ,mBAAA,CAQA,MARAkJ,YAQA,GAPDC,YAA6I,EAC7InJ,mBAAA,CAKK,MALLoJ,YAKK,GAJApJ,mBAAA,CAAgE,aAA3DY,YAAA,CAAsDC,sBAAA;IAAzCC,EAAE,EAAC;EAAmB;sBAAC,MAAI,C;;QACxEd,mBAAA,CAAmE,aAA9DY,YAAA,CAAyDC,sBAAA;IAA5CC,EAAE,EAAC;EAAsB;sBAAC,MAAI,C;;QAChDd,mBAAA,CAAoE,aAA/DY,YAAA,CAA0DC,sBAAA;IAA7CC,EAAE,EAAC;EAAuB;sBAAC,MAAI,C;;YAI5Cd,mBAAA,CAQkB,MARlBqJ,YAQkB,GAPDC,YAA8I,EAC9ItJ,mBAAA,CAKK,MALLuJ,YAKK,GAJAvJ,mBAAA,CAA6D,aAAxDY,YAAA,CAAmDC,sBAAA;IAAtCC,EAAE,EAAC;EAAe;sBAAC,MAAK,C;;QACrEd,mBAAA,CAAgE,aAA3DY,YAAA,CAAsDC,sBAAA;IAAzCC,EAAE,EAAC;EAAkB;sBAAC,MAAK,C;;QAC7Cd,mBAAA,CAA+D,aAA1DY,YAAA,CAAqDC,sBAAA;IAAxCC,EAAE,EAAC;EAAgB;sBAAC,MAAM,C;;YAIvCd,mBAAA,CAQkB,MARlBwJ,YAQkB,GAPDC,YAA+I,EAC/IzJ,mBAAA,CAKK,MALL0J,YAKK,GAJA1J,mBAAA,CAAgE,aAA3DY,YAAA,CAAsDC,sBAAA;IAAzCC,EAAE,EAAC;EAAiB;sBAAC,MAAM,C;;QACxEd,mBAAA,CAAmE,aAA9DY,YAAA,CAAyDC,sBAAA;IAA5CC,EAAE,EAAC;EAAoB;sBAAC,MAAM,C;;QAChDd,mBAAA,CAAoE,aAA/DY,YAAA,CAA0DC,sBAAA;IAA7CC,EAAE,EAAC;EAAqB;sBAAC,MAAM,C;;YAI5Cd,mBAAA,CAQkB,MARlB2J,YAQkB,GAPDC,YAA+I,EAC/I5J,mBAAA,CAKK,MALL6J,YAKK,GAJA7J,mBAAA,CAAmE,aAA9DY,YAAA,CAAyDC,sBAAA;IAA5CC,EAAE,EAAC;EAAoB;sBAAC,MAAM,C;;QAC3Ed,mBAAA,CAAsE,aAAjEY,YAAA,CAA4DC,sBAAA;IAA/CC,EAAE,EAAC;EAAuB;sBAAC,MAAM,C;;QACnDd,mBAAA,CAAuE,aAAlEY,YAAA,CAA6DC,sBAAA;IAAhDC,EAAE,EAAC;EAAwB;sBAAC,MAAM,C;;YAI/Cd,mBAAA,CAQkB,MARlB8J,YAQkB,GAPDC,YAA+I,EAC/I/J,mBAAA,CAKK,MALLgK,YAKK,GAJAhK,mBAAA,CAAoE,aAA/DY,YAAA,CAA0DC,sBAAA;IAA7CC,EAAE,EAAC;EAAqB;sBAAC,MAAM,C;;QAC5Ed,mBAAA,CAAuE,aAAlEY,YAAA,CAA6DC,sBAAA;IAAhDC,EAAE,EAAC;EAAwB;sBAAC,MAAM,C;;QACpDd,mBAAA,CAAwE,aAAnEY,YAAA,CAA8DC,sBAAA;IAAjDC,EAAE,EAAC;EAAyB;sBAAC,MAAM,C;;YAIhDd,mBAAA,CAQkB,MARlBiK,YAQkB,GAPDC,YAA8I,EAC9IlK,mBAAA,CAKK,MALLmK,YAKK,GAJAnK,mBAAA,CAAuE,aAAlEY,YAAA,CAA6DC,sBAAA;IAAhDC,EAAE,EAAC;EAAyB;sBAAC,MAAK,C;;QAC/Ed,mBAAA,CAA0E,aAArEY,YAAA,CAAgEC,sBAAA;IAAnDC,EAAE,EAAC;EAA4B;sBAAC,MAAK,C;;QACvDd,mBAAA,CAA2E,aAAtEY,YAAA,CAAiEC,sBAAA;IAApDC,EAAE,EAAC;EAA6B;sBAAC,MAAK,C;;YAInDd,mBAAA,CAOkB,MAPlBoK,YAOkB,GANDC,YAA+I,EAC/IrK,mBAAA,CAIK,MAJLsK,YAIK,GAHAtK,mBAAA,CAA+D,aAA1DY,YAAA,CAAqDC,sBAAA;IAAxCC,EAAE,EAAC;EAAgB;sBAAC,MAAM,C;;QACvEd,mBAAA,CAAkE,aAA7DY,YAAA,CAAwDC,sBAAA;IAA3CC,EAAE,EAAC;EAAmB;sBAAC,MAAM,C;;YAI1Cd,mBAAA,CAOkB,MAPlBuK,YAOkB,GANDC,YAA8I,EAC9IxK,mBAAA,CAIK,MAJLyK,YAIK,GAHAzK,mBAAA,CAAgE,aAA3DY,YAAA,CAAsDC,sBAAA;IAAzCC,EAAE,EAAC;EAAkB;sBAAC,MAAK,C;;QACxEd,mBAAA,CAAmE,aAA9DY,YAAA,CAAyDC,sBAAA;IAA5CC,EAAE,EAAC;EAAqB;sBAAC,MAAK,C;;YAI3Cd,mBAAA,CAQkB,MARlB0K,YAQkB,GAPDC,YAA6I,EAC7I3K,mBAAA,CAKK,MALL4K,YAKK,GAJA5K,mBAAA,CAA0D,aAArDY,YAAA,CAAgDC,sBAAA;IAAnCC,EAAE,EAAC;EAAa;sBAAC,MAAI,C;;QAClEd,mBAAA,CAA6D,aAAxDY,YAAA,CAAmDC,sBAAA;IAAtCC,EAAE,EAAC;EAAgB;sBAAC,MAAI,C;;QAC1Cd,mBAAA,CAA6D,aAAxDY,YAAA,CAAmDC,sBAAA;IAAtCC,EAAE,EAAC;EAAc;sBAAC,MAAM,C;;YAIrCd,mBAAA,CAQkB,MARlB6K,YAQkB,GAPDC,YAA6I,EAC7I9K,mBAAA,CAKK,MALL+K,YAKK,GAJA/K,mBAAA,CAA+D,aAA1DY,YAAA,CAAqDC,sBAAA;IAAxCC,EAAE,EAAC;EAAkB;sBAAC,MAAI,C;;QACvEd,mBAAA,CAAkE,aAA7DY,YAAA,CAAwDC,sBAAA;IAA3CC,EAAE,EAAC;EAAqB;sBAAC,MAAI,C;;QAC/Cd,mBAAA,CAAmE,aAA9DY,YAAA,CAAyDC,sBAAA;IAA5CC,EAAE,EAAC;EAAsB;sBAAC,MAAI,C;;YAI3Cd,mBAAA,CAQkB,MARlBgL,YAQkB,GAPDC,YAA+I,EAC/IjL,mBAAA,CAKK,MALLkL,YAKK,GAJAlL,mBAAA,CAAiE,aAA5DY,YAAA,CAAuDC,sBAAA;IAA1CC,EAAE,EAAC;EAAkB;sBAAC,MAAM,C;;QACzEd,mBAAA,CAAoE,aAA/DY,YAAA,CAA0DC,sBAAA;IAA7CC,EAAE,EAAC;EAAqB;sBAAC,MAAM,C;;QACjDd,mBAAA,CAAqE,aAAhEY,YAAA,CAA2DC,sBAAA;IAA9CC,EAAE,EAAC;EAAsB;sBAAC,MAAM,C;;YAI7Cd,mBAAA,CAQkB,MARlBmL,YAQkB,GAPDC,YAA+I,EAC/IpL,mBAAA,CAKK,MALLqL,YAKK,GAJArL,mBAAA,CAA4D,aAAvDY,YAAA,CAAkDC,sBAAA;IAArCC,EAAE,EAAC;EAAa;sBAAC,MAAM,C;;QACpEd,mBAAA,CAA+D,aAA1DY,YAAA,CAAqDC,sBAAA;IAAxCC,EAAE,EAAC;EAAgB;sBAAC,MAAM,C;;QAC5Cd,mBAAA,CAA6D,aAAxDY,YAAA,CAAmDC,sBAAA;IAAtCC,EAAE,EAAC;EAAc;sBAAC,MAAM,C;;YAIrCd,mBAAA,CAOkB,MAPlBsL,YAOkB,GANDC,YAA6I,EAC7IvL,mBAAA,CAIK,MAJLwL,YAIK,GAHAxL,mBAAA,CAA4D,aAAvDY,YAAA,CAAkDC,sBAAA;IAArCC,EAAE,EAAC;EAAe;sBAAC,MAAI,C;;QACpEd,mBAAA,CAA+D,aAA1DY,YAAA,CAAqDC,sBAAA;IAAxCC,EAAE,EAAC;EAAkB;sBAAC,MAAI,C;;YAIvCd,mBAAA,CASkB,MATlByL,YASkB,GARDC,YAA6I,EAC7I1L,mBAAA,CAMK,MANL2L,YAMK,GALA3L,mBAAA,CAAqD,aAAhDY,YAAA,CAA2CC,sBAAA;IAA9BC,EAAE,EAAC;EAAS;sBAAC,MAAG,C;;QAC7Dd,mBAAA,CAAqD,aAAhDY,YAAA,CAA2CC,sBAAA;IAA9BC,EAAE,EAAC;EAAS;sBAAC,MAAG,C;;QAClCd,mBAAA,CAAqD,aAAhDY,YAAA,CAA2CC,sBAAA;IAA9BC,EAAE,EAAC;EAAS;sBAAC,MAAG,C;;QAClCd,mBAAA,CAAqD,aAAhDY,YAAA,CAA2CC,sBAAA;IAA9BC,EAAE,EAAC;EAAS;sBAAC,MAAG,C;;8CA5H8BwC,KAAA,CAAAC,IAAI,Y"}]}