{"remainingRequest": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\B7839DormManager\\DormManager-web\\src\\components\\LeftMenu.vue?vue&type=template&id=edc10994&scoped=true", "dependencies": [{"path": "I:\\product4\\B7839DormManager\\DormManager-web\\src\\components\\LeftMenu.vue", "mtime": 1749044379993}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\babel.config.js", "mtime": 1749034452000}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749043011899}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749043012919}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749043013784}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749043011899}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749043013130}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["class", "_createElementVNode", "href", "_createElementBlock", "_hoisted_1", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_hoisted_5", "_hoisted_6", "_hoisted_7", "_hoisted_8", "_hoisted_9", "_createVNode", "_component_router_link", "to", "_hoisted_14", "_hoisted_15", "_hoisted_16", "_hoisted_19", "_hoisted_20", "_hoisted_21", "_hoisted_24", "_hoisted_25", "_hoisted_26", "_hoisted_29", "_hoisted_30", "_hoisted_31", "_hoisted_34", "_hoisted_35", "_hoisted_36", "_hoisted_39", "_hoisted_40", "_hoisted_41", "_hoisted_46", "_hoisted_47", "_hoisted_48", "_hoisted_52", "_hoisted_53", "_hoisted_54", "_hoisted_58", "_hoisted_59", "_hoisted_60", "_hoisted_64", "_hoisted_65", "_hoisted_66", "_hoisted_70", "_hoisted_71", "_hoisted_72", "_hoisted_76", "_hoisted_77", "_hoisted_78", "$data", "role", "_hoisted_80", "_hoisted_81", "_hoisted_82", "_hoisted_83", "_hoisted_84", "_hoisted_88", "_hoisted_89", "_hoisted_90", "_hoisted_94", "_hoisted_95", "_hoisted_96", "_hoisted_100", "_hoisted_101", "_hoisted_102", "_hoisted_106", "_hoisted_107", "_hoisted_108", "_hoisted_112", "_hoisted_113", "_hoisted_114", "_hoisted_118", "_hoisted_119", "_hoisted_120", "_hoisted_123", "_hoisted_124", "_hoisted_125", "_hoisted_128", "_hoisted_129", "_hoisted_130", "_hoisted_134", "_hoisted_135", "_hoisted_136", "_hoisted_140", "_hoisted_141", "_hoisted_142", "_hoisted_146", "_hoisted_147", "_hoisted_148", "_hoisted_152", "_hoisted_153", "_hoisted_154", "_hoisted_157", "_hoisted_158", "_hoisted_159", "_hoisted_164", "_hoisted_165", "_hoisted_166", "_hoisted_167", "_hoisted_168", "_hoisted_172", "_hoisted_173", "_hoisted_174", "_hoisted_178", "_hoisted_179", "_hoisted_180", "_hoisted_184", "_hoisted_185", "_hoisted_186", "_hoisted_190", "_hoisted_191", "_hoisted_192", "_hoisted_196", "_hoisted_197", "_hoisted_198", "_hoisted_202", "_hoisted_203", "_hoisted_204", "_hoisted_207", "_hoisted_208", "_hoisted_209", "_hoisted_212", "_hoisted_213", "_hoisted_214", "_hoisted_218", "_hoisted_219", "_hoisted_220", "_hoisted_224", "_hoisted_225", "_hoisted_226", "_hoisted_230", "_hoisted_231", "_hoisted_232", "_hoisted_236", "_hoisted_237", "_hoisted_238", "_hoisted_241", "_hoisted_242", "_hoisted_243", "_hoisted_248", "_hoisted_249", "_hoisted_250", "_hoisted_251", "_hoisted_252", "_hoisted_256", "_hoisted_257", "_hoisted_258", "_hoisted_262", "_hoisted_263", "_hoisted_264", "_hoisted_268", "_hoisted_269", "_hoisted_270", "_hoisted_274", "_hoisted_275", "_hoisted_276", "_hoisted_280", "_hoisted_281", "_hoisted_282", "_hoisted_286", "_hoisted_287", "_hoisted_288", "_hoisted_291", "_hoisted_292", "_hoisted_293", "_hoisted_296", "_hoisted_297", "_hoisted_298", "_hoisted_302", "_hoisted_303", "_hoisted_304", "_hoisted_308", "_hoisted_309", "_hoisted_310", "_hoisted_314", "_hoisted_315", "_hoisted_316", "_hoisted_320", "_hoisted_321", "_hoisted_322", "_hoisted_325", "_hoisted_326", "_hoisted_327"], "sources": ["I:\\product4\\B7839DormManager\\DormManager-web\\src\\components\\LeftMenu.vue"], "sourcesContent": ["<template>\r\n <nav class=\"pcoded-navbar\">\r\n        <div class=\"navbar-wrapper\">\r\n            <div class=\"navbar-brand header-logo\">\r\n                <a href=\"/main\" class=\"b-brand\">\r\n                    <div class=\"b-bg\">\r\n                        <i class=\"feather icon-trending-up\"></i>\r\n                    </div>\r\n                    <span class=\"b-title\">宿舍管理系统</span>\r\n                </a>\r\n\r\n            </div>\r\n            <div class=\"navbar-content scroll-div\">\r\n                <ul class=\"nav pcoded-inner-navbar\" v-show=\"role == '管理员'\">\r\n                 \r\n                       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿舍楼管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                      \r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                      <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿舍管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                                 <li> <router-link to=\"/dormbuildingAdd\">添加宿舍楼</router-link></li>\r\n  <li> <router-link to=\"/dormbuildingManage\">管理宿舍楼</router-link></li>\r\n                             <li> <router-link to=\"/dormitoryAdd\">添加宿舍</router-link></li>\r\n  <li> <router-link to=\"/dormitoryManage\">管理宿舍</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                         <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">学生管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/studentAdd\">添加学生</router-link></li>\r\n  <li> <router-link to=\"/studentManage\">管理学生</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                           <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿管阿姨管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/hostessAdd\">添加宿管阿姨</router-link></li>\r\n  <li> <router-link to=\"/hostessManage\">管理宿管阿姨</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                         <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">维修员管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/repairmenAdd\">添加维修员</router-link></li>\r\n  <li> <router-link to=\"/repairmenManage\">管理维修员</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n\r\n                         <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">公告管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/systemnoticesAdd\">添加公告</router-link></li>\r\n  <li> <router-link to=\"/systemnoticesManage\">管理公告</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                          <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿舍更换管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/dormitorychangeAdd\">添加宿舍更换</router-link></li>\r\n  <li> <router-link to=\"/dormitorychangeManage\">管理宿舍更换</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                     \r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">报修管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                         <li> <router-link to=\"/repairtypeAdd\">添加报修类型</router-link></li>\r\n  <li> <router-link to=\"/repairtypeManage\">管理报修类型</router-link></li>\r\n  <li> <router-link to=\"/repairordersManage\">管理报修</router-link></li>\r\n  <li> <router-link to=\"/repairordersManage2\">报修列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                    \r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">水电费管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/waterelectricityfeeAdd\">添加水电费</router-link></li>\r\n  <li> <router-link to=\"/waterelectricityfeeManage\">管理水电费</router-link></li>\r\n  <li> <router-link to=\"/waterelectricityfeeManage2\">水电费列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n    \r\n\r\n  \r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">离校登记管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/leaveschoolAdd\">添加离校登记</router-link></li>\r\n  <li> <router-link to=\"/leaveschoolManage\">管理离校登记</router-link></li>\r\n  <li> <router-link to=\"/leaveschoolManage2\">离校登记列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                     \r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">返校登记管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/registerinfoAdd\">添加返校登记</router-link></li>\r\n  <li> <router-link to=\"/registerinfoManage\">管理返校登记</router-link></li>\r\n  <li> <router-link to=\"/registerinfoManage2\">返校登记列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿舍评分管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/dormitoryscoreAdd\">添加宿舍评分</router-link></li>\r\n  <li> <router-link to=\"/dormitoryscoreManage\">管理宿舍评分</router-link></li>\r\n  <li> <router-link to=\"/dormitoryscoreManage2\">宿舍评分列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n \r\n    \r\n\r\n     \r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">统计报表</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/total1\">报修类型统计</router-link></li>\r\n  <li> <router-link to=\"/total2\">维修员维修统计</router-link></li>\r\n  <li> <router-link to=\"/total3\">宿舍评分统计</router-link></li>\r\n\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                      <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">系统管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                           \r\n  <li> <router-link to=\"/password\">修改密码</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n          \r\n                </ul>\r\n\r\n                      <ul class=\"nav pcoded-inner-navbar\" v-show=\"role == '学生'\">\r\n                    <li class=\"nav-item pcoded-menu-caption\">\r\n                        <label>功能菜单</label>\r\n                    </li>\r\n                         <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">公告管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/systemnoticesAdd\">添加公告</router-link></li>\r\n  <li> <router-link to=\"/systemnoticesManage\">管理公告</router-link></li>\r\n  <li> <router-link to=\"/systemnoticesManage2\">公告列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">维修员管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/repairmenAdd\">添加维修员</router-link></li>\r\n  <li> <router-link to=\"/repairmenManage\">管理维修员</router-link></li>\r\n  <li> <router-link to=\"/repairmenInfo\">修改个人信息</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">离校登记管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/leaveschoolAdd\">添加离校登记</router-link></li>\r\n  <li> <router-link to=\"/leaveschoolManage\">管理离校登记</router-link></li>\r\n  <li> <router-link to=\"/leaveschoolManage2\">离校登记列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿舍评分管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/dormitoryscoreAdd\">添加宿舍评分</router-link></li>\r\n  <li> <router-link to=\"/dormitoryscoreManage\">管理宿舍评分</router-link></li>\r\n  <li> <router-link to=\"/dormitoryscoreManage2\">宿舍评分列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿舍更换管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/dormitorychangeAdd\">添加宿舍更换</router-link></li>\r\n  <li> <router-link to=\"/dormitorychangeManage\">管理宿舍更换</router-link></li>\r\n  <li> <router-link to=\"/dormitorychangeManage2\">宿舍更换列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">水电费管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/waterelectricityfeeAdd\">添加水电费</router-link></li>\r\n  <li> <router-link to=\"/waterelectricityfeeManage\">管理水电费</router-link></li>\r\n  <li> <router-link to=\"/waterelectricityfeeManage2\">水电费列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">报修类型管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/repairtypeAdd\">添加报修类型</router-link></li>\r\n  <li> <router-link to=\"/repairtypeManage\">管理报修类型</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿舍楼管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/dormbuildingAdd\">添加宿舍楼</router-link></li>\r\n  <li> <router-link to=\"/dormbuildingManage\">管理宿舍楼</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">学生管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/studentAdd\">添加学生</router-link></li>\r\n  <li> <router-link to=\"/studentManage\">管理学生</router-link></li>\r\n  <li> <router-link to=\"/studentInfo\">修改个人信息</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">报修管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/repairordersAdd\">添加报修</router-link></li>\r\n  <li> <router-link to=\"/repairordersManage\">管理报修</router-link></li>\r\n  <li> <router-link to=\"/repairordersManage2\">报修列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">返校登记管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/registerinfoAdd\">添加返校登记</router-link></li>\r\n  <li> <router-link to=\"/registerinfoManage\">管理返校登记</router-link></li>\r\n  <li> <router-link to=\"/registerinfoManage2\">返校登记列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿管阿姨管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/hostessAdd\">添加宿管阿姨</router-link></li>\r\n  <li> <router-link to=\"/hostessManage\">管理宿管阿姨</router-link></li>\r\n  <li> <router-link to=\"/hostessInfo\">修改个人信息</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿舍管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/dormitoryAdd\">添加宿舍</router-link></li>\r\n  <li> <router-link to=\"/dormitoryManage\">管理宿舍</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">图表管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/total1\">图表1</router-link></li>\r\n  <li> <router-link to=\"/total2\">图表2</router-link></li>\r\n  <li> <router-link to=\"/total3\">图表3</router-link></li>\r\n  <li> <router-link to=\"/total4\">图表4</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n          \r\n                </ul>\r\n\r\n\r\n                      <ul class=\"nav pcoded-inner-navbar\" v-show=\"role == '维修员'\">\r\n                    <li class=\"nav-item pcoded-menu-caption\">\r\n                        <label>功能菜单</label>\r\n                    </li>\r\n                         <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">公告管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/systemnoticesAdd\">添加公告</router-link></li>\r\n  <li> <router-link to=\"/systemnoticesManage\">管理公告</router-link></li>\r\n  <li> <router-link to=\"/systemnoticesManage2\">公告列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">维修员管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/repairmenAdd\">添加维修员</router-link></li>\r\n  <li> <router-link to=\"/repairmenManage\">管理维修员</router-link></li>\r\n  <li> <router-link to=\"/repairmenInfo\">修改个人信息</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">离校登记管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/leaveschoolAdd\">添加离校登记</router-link></li>\r\n  <li> <router-link to=\"/leaveschoolManage\">管理离校登记</router-link></li>\r\n  <li> <router-link to=\"/leaveschoolManage2\">离校登记列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿舍评分管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/dormitoryscoreAdd\">添加宿舍评分</router-link></li>\r\n  <li> <router-link to=\"/dormitoryscoreManage\">管理宿舍评分</router-link></li>\r\n  <li> <router-link to=\"/dormitoryscoreManage2\">宿舍评分列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿舍更换管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/dormitorychangeAdd\">添加宿舍更换</router-link></li>\r\n  <li> <router-link to=\"/dormitorychangeManage\">管理宿舍更换</router-link></li>\r\n  <li> <router-link to=\"/dormitorychangeManage2\">宿舍更换列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">水电费管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/waterelectricityfeeAdd\">添加水电费</router-link></li>\r\n  <li> <router-link to=\"/waterelectricityfeeManage\">管理水电费</router-link></li>\r\n  <li> <router-link to=\"/waterelectricityfeeManage2\">水电费列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">报修类型管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/repairtypeAdd\">添加报修类型</router-link></li>\r\n  <li> <router-link to=\"/repairtypeManage\">管理报修类型</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿舍楼管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/dormbuildingAdd\">添加宿舍楼</router-link></li>\r\n  <li> <router-link to=\"/dormbuildingManage\">管理宿舍楼</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">学生管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/studentAdd\">添加学生</router-link></li>\r\n  <li> <router-link to=\"/studentManage\">管理学生</router-link></li>\r\n  <li> <router-link to=\"/studentInfo\">修改个人信息</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">报修管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/repairordersAdd\">添加报修</router-link></li>\r\n  <li> <router-link to=\"/repairordersManage\">管理报修</router-link></li>\r\n  <li> <router-link to=\"/repairordersManage2\">报修列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">返校登记管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/registerinfoAdd\">添加返校登记</router-link></li>\r\n  <li> <router-link to=\"/registerinfoManage\">管理返校登记</router-link></li>\r\n  <li> <router-link to=\"/registerinfoManage2\">返校登记列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿管阿姨管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/hostessAdd\">添加宿管阿姨</router-link></li>\r\n  <li> <router-link to=\"/hostessManage\">管理宿管阿姨</router-link></li>\r\n  <li> <router-link to=\"/hostessInfo\">修改个人信息</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿舍管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/dormitoryAdd\">添加宿舍</router-link></li>\r\n  <li> <router-link to=\"/dormitoryManage\">管理宿舍</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">图表管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/total1\">图表1</router-link></li>\r\n  <li> <router-link to=\"/total2\">图表2</router-link></li>\r\n  <li> <router-link to=\"/total3\">图表3</router-link></li>\r\n  <li> <router-link to=\"/total4\">图表4</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n          \r\n                </ul>\r\n\r\n                      <ul class=\"nav pcoded-inner-navbar\" v-show=\"role == '宿管阿姨'\">\r\n                    <li class=\"nav-item pcoded-menu-caption\">\r\n                        <label>功能菜单</label>\r\n                    </li>\r\n                         <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">公告管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/systemnoticesAdd\">添加公告</router-link></li>\r\n  <li> <router-link to=\"/systemnoticesManage\">管理公告</router-link></li>\r\n  <li> <router-link to=\"/systemnoticesManage2\">公告列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">维修员管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/repairmenAdd\">添加维修员</router-link></li>\r\n  <li> <router-link to=\"/repairmenManage\">管理维修员</router-link></li>\r\n  <li> <router-link to=\"/repairmenInfo\">修改个人信息</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">离校登记管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/leaveschoolAdd\">添加离校登记</router-link></li>\r\n  <li> <router-link to=\"/leaveschoolManage\">管理离校登记</router-link></li>\r\n  <li> <router-link to=\"/leaveschoolManage2\">离校登记列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿舍评分管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/dormitoryscoreAdd\">添加宿舍评分</router-link></li>\r\n  <li> <router-link to=\"/dormitoryscoreManage\">管理宿舍评分</router-link></li>\r\n  <li> <router-link to=\"/dormitoryscoreManage2\">宿舍评分列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿舍更换管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/dormitorychangeAdd\">添加宿舍更换</router-link></li>\r\n  <li> <router-link to=\"/dormitorychangeManage\">管理宿舍更换</router-link></li>\r\n  <li> <router-link to=\"/dormitorychangeManage2\">宿舍更换列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">水电费管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/waterelectricityfeeAdd\">添加水电费</router-link></li>\r\n  <li> <router-link to=\"/waterelectricityfeeManage\">管理水电费</router-link></li>\r\n  <li> <router-link to=\"/waterelectricityfeeManage2\">水电费列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">报修类型管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/repairtypeAdd\">添加报修类型</router-link></li>\r\n  <li> <router-link to=\"/repairtypeManage\">管理报修类型</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿舍楼管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/dormbuildingAdd\">添加宿舍楼</router-link></li>\r\n  <li> <router-link to=\"/dormbuildingManage\">管理宿舍楼</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">学生管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/studentAdd\">添加学生</router-link></li>\r\n  <li> <router-link to=\"/studentManage\">管理学生</router-link></li>\r\n  <li> <router-link to=\"/studentInfo\">修改个人信息</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">报修管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/repairordersAdd\">添加报修</router-link></li>\r\n  <li> <router-link to=\"/repairordersManage\">管理报修</router-link></li>\r\n  <li> <router-link to=\"/repairordersManage2\">报修列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">返校登记管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/registerinfoAdd\">添加返校登记</router-link></li>\r\n  <li> <router-link to=\"/registerinfoManage\">管理返校登记</router-link></li>\r\n  <li> <router-link to=\"/registerinfoManage2\">返校登记列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿管阿姨管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/hostessAdd\">添加宿管阿姨</router-link></li>\r\n  <li> <router-link to=\"/hostessManage\">管理宿管阿姨</router-link></li>\r\n  <li> <router-link to=\"/hostessInfo\">修改个人信息</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿舍管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/dormitoryAdd\">添加宿舍</router-link></li>\r\n  <li> <router-link to=\"/dormitoryManage\">管理宿舍</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">图表管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/total1\">图表1</router-link></li>\r\n  <li> <router-link to=\"/total2\">图表2</router-link></li>\r\n  <li> <router-link to=\"/total3\">图表3</router-link></li>\r\n  <li> <router-link to=\"/total4\">图表4</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n          \r\n                </ul>\r\n\r\n\r\n            </div>\r\n        </div>\r\n    </nav>\r\n    \r\n\r\n</template>\r\n\r\n\r\n<script>\r\nimport $ from 'jquery';\r\n\r\nexport default {\r\n  name: \"LeftMenu\",\r\n  data() {\r\n    return {\r\n      userLname: \"\",\r\n      role: \"\",\r\n      activeMenu: null, // 用于跟踪当前激活的菜单\r\n    };\r\n  },\r\n  watch: {\r\n    $route(to, from) {\r\n      this.activeMenu = to.name;\r\n      this.$nextTick(() => {\r\n        this.initializeMenu();\r\n      });\r\n    }\r\n  },\r\n  mounted() {\r\n    this.userLname = sessionStorage.getItem(\"userLname\");\r\n    this.role = sessionStorage.getItem(\"role\");\r\n    this.$nextTick(() => {\r\n      this.initializeMenu();\r\n    });\r\n  },\r\n  methods: {\r\n    initializeMenu() {\r\n      $('.nav-item.pcoded-hasmenu > a').off('click').on('click', function(e) {\r\n        e.preventDefault();\r\n        const $parent = $(this).parent();\r\n\r\n        if ($parent.hasClass('pcoded-trigger')) {\r\n          $parent.removeClass('pcoded-trigger');\r\n          $parent.children('ul').slideUp();\r\n        } else {\r\n          $('.nav-item.pcoded-hasmenu').removeClass('pcoded-trigger');\r\n          $('.nav-item.pcoded-hasmenu > ul').slideUp();\r\n          $parent.addClass('pcoded-trigger');\r\n          $parent.children('ul').slideDown();\r\n        }\r\n      });\r\n\r\n      // 初始化：根据当前路由展开对应的菜单\r\n      const currentPath = this.$route.path;\r\n      $('.pcoded-submenu a').each(function() {\r\n        if ($(this).attr('href') === currentPath) {\r\n          $(this).parents('.nav-item.pcoded-hasmenu').addClass('pcoded-trigger');\r\n          $(this).parents('ul.pcoded-submenu').show();\r\n        }\r\n      });\r\n    },\r\n\r\n    exit: function () {\r\n      var _this = this;\r\n      this.$confirm(\"确认退出吗?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n          .then(() => {\r\n            sessionStorage.removeItem(\"userLname\");\r\n            sessionStorage.removeItem(\"role\");\r\n            _this.$router.push(\"/\");\r\n          })\r\n          .catch(() => { });\r\n    },\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.example-showcase .el-dropdown-link {\r\n  cursor: pointer;\r\n  color:green;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.pcoded-submenu a{\r\n  text-decoration: none;\r\n  font-size: 14px;\r\n}\r\n\r\n/*加点击效果*/\r\n.pcoded-submenu a:hover{\r\n  color: #fff;\r\n  color: #ff6600;\r\n}\r\n</style>\r\n\r\n"], "mappings": ";;;EACMA,KAAK,EAAC;AAAe;;EACdA,KAAK,EAAC;AAAgB;;;EAUlBA,KAAK,EAAC;AAA2B;;EAC9BA,KAAK,EAAC;AAAyB;;;EAUxBA,KAAK,EAAC;AAAyB;gEAClCC,mBAAA,CAA6I;EAA1IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAsC;EAAhCD,KAAK,EAAC;AAAc,GAAC,MAAI,E;;EAC9HA,KAAK,EAAC;AAAgB;kDACuB,OAAK;kDACjC,OAAK;kDACgB,MAAI;kDAC5B,MAAI;;EAKhBA,KAAK,EAAC;AAAyB;iEACrCC,mBAAA,CAA6I;EAA1IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAsC;EAAhCD,KAAK,EAAC;AAAc,GAAC,MAAI,E;;EAC9HA,KAAK,EAAC;AAAgB;kDACc,MAAI;kDAC5B,MAAI;;EAKZA,KAAK,EAAC;AAAyB;iEACvCC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;kDACc,QAAM;kDAC9B,QAAM;;EAKhBA,KAAK,EAAC;AAAyB;iEACrCC,mBAAA,CAA8I;EAA3IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAuC;EAAjCD,KAAK,EAAC;AAAc,GAAC,OAAK,E;;EAC/HA,KAAK,EAAC;AAAgB;kDACgB,OAAK;kDAC7B,OAAK;;EAMjBA,KAAK,EAAC;AAAyB;iEACrCC,mBAAA,CAA6I;EAA1IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAsC;EAAhCD,KAAK,EAAC;AAAc,GAAC,MAAI,E;;EAC9HA,KAAK,EAAC;AAAgB;kDACoB,MAAI;kDAC5B,MAAI;;EAKnBA,KAAK,EAAC;AAAyB;iEACtCC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;kDACsB,QAAM;kDAC9B,QAAM;;EAM1CA,KAAK,EAAC;AAAyB;iEACnBC,mBAAA,CAA6I;EAA1IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAsC;EAAhCD,KAAK,EAAC;AAAc,GAAC,MAAI,E;;EAC9HA,KAAK,EAAC;AAAgB;kDACa,QAAM;kDAC1B,QAAM;kDACJ,MAAI;kDACH,MAAI;;EAMtCA,KAAK,EAAC;AAAyB;iEACnBC,mBAAA,CAA8I;EAA3IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAuC;EAAjCD,KAAK,EAAC;AAAc,GAAC,OAAK,E;;EAC/HA,KAAK,EAAC;AAAgB;kDAC0B,OAAK;kDAC7B,OAAK;kDACJ,OAAK;;EAO9CA,KAAK,EAAC;AAAyB;iEACnBC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;kDACkB,QAAM;kDAC9B,QAAM;kDACL,QAAM;;EAMvCA,KAAK,EAAC;AAAyB;iEACnBC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;kDACmB,QAAM;kDAC9B,QAAM;kDACL,QAAM;;EAMxCA,KAAK,EAAC;AAAyB;iEACnBC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;kDACqB,QAAM;kDAC9B,QAAM;kDACL,QAAM;;EAQ1CA,KAAK,EAAC;AAAyB;iEACnBC,mBAAA,CAA6I;EAA1IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAsC;EAAhCD,KAAK,EAAC;AAAc,GAAC,MAAI,E;;EAC9HA,KAAK,EAAC;AAAgB;kDACU,QAAM;kDACjC,SAAO;kDACP,QAAM;;EAMZA,KAAK,EAAC;AAAyB;iEAClCC,mBAAA,CAA6I;EAA1IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAsC;EAAhCD,KAAK,EAAC;AAAc,GAAC,MAAI,E;;EAC9HA,KAAK,EAAC;AAAgB;kDAEf,MAAI;;EAQbA,KAAK,EAAC;AAAyB;iEACrCC,mBAAA,CAEK;EAFDD,KAAK,EAAC;AAA8B,I,aACpCC,mBAAA,CAAmB,eAAZ,MAAI,E;;EAELD,KAAK,EAAC;AAAyB;iEACrCC,mBAAA,CAA6I;EAA1IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAsC;EAAhCD,KAAK,EAAC;AAAc,GAAC,MAAI,E;;EAC9HA,KAAK,EAAC;AAAgB;kDACoB,MAAI;kDAC5B,MAAI;kDACH,MAAI;;EAIvCA,KAAK,EAAC;AAAyB;iEACnBC,mBAAA,CAA8I;EAA3IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAuC;EAAjCD,KAAK,EAAC;AAAc,GAAC,OAAK,E;;EAC/HA,KAAK,EAAC;AAAgB;kDACgB,OAAK;kDAC7B,OAAK;kDACP,QAAM;;EAIlCA,KAAK,EAAC;AAAyB;iEACnBC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;kDACkB,QAAM;kDAC9B,QAAM;kDACL,QAAM;;EAIvCA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;mDACqB,QAAM;mDAC9B,QAAM;mDACL,QAAM;;EAI1CA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;mDACsB,QAAM;mDAC9B,QAAM;mDACL,QAAM;;EAI3CA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA8I;EAA3IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAuC;EAAjCD,KAAK,EAAC;AAAc,GAAC,OAAK,E;;EAC/HA,KAAK,EAAC;AAAgB;mDAC0B,OAAK;mDAC7B,OAAK;mDACJ,OAAK;;EAI9CA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;mDACiB,QAAM;mDAC9B,QAAM;;EAIrCA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA8I;EAA3IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAuC;EAAjCD,KAAK,EAAC;AAAc,GAAC,OAAK,E;;EAC/HA,KAAK,EAAC;AAAgB;mDACmB,OAAK;mDAC7B,OAAK;;EAItCA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA6I;EAA1IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAsC;EAAhCD,KAAK,EAAC;AAAc,GAAC,MAAI,E;;EAC9HA,KAAK,EAAC;AAAgB;mDACc,MAAI;mDAC5B,MAAI;mDACN,QAAM;;EAIhCA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA6I;EAA1IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAsC;EAAhCD,KAAK,EAAC;AAAc,GAAC,MAAI,E;;EAC9HA,KAAK,EAAC;AAAgB;mDACmB,MAAI;mDAC5B,MAAI;mDACH,MAAI;;EAItCA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;mDACmB,QAAM;mDAC9B,QAAM;mDACL,QAAM;;EAIxCA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;mDACc,QAAM;mDAC9B,QAAM;mDACR,QAAM;;EAIhCA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA6I;EAA1IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAsC;EAAhCD,KAAK,EAAC;AAAc,GAAC,MAAI,E;;EAC9HA,KAAK,EAAC;AAAgB;mDACgB,MAAI;mDAC5B,MAAI;;EAIlCA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA6I;EAA1IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAsC;EAAhCD,KAAK,EAAC;AAAc,GAAC,MAAI,E;;EAC9HA,KAAK,EAAC;AAAgB;mDACU,KAAG;mDAC9B,KAAG;mDACH,KAAG;mDACH,KAAG;;EASVA,KAAK,EAAC;AAAyB;kEACrCC,mBAAA,CAEK;EAFDD,KAAK,EAAC;AAA8B,I,aACpCC,mBAAA,CAAmB,eAAZ,MAAI,E;;EAELD,KAAK,EAAC;AAAyB;kEACrCC,mBAAA,CAA6I;EAA1IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAsC;EAAhCD,KAAK,EAAC;AAAc,GAAC,MAAI,E;;EAC9HA,KAAK,EAAC;AAAgB;mDACoB,MAAI;mDAC5B,MAAI;mDACH,MAAI;;EAIvCA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA8I;EAA3IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAuC;EAAjCD,KAAK,EAAC;AAAc,GAAC,OAAK,E;;EAC/HA,KAAK,EAAC;AAAgB;mDACgB,OAAK;mDAC7B,OAAK;mDACP,QAAM;;EAIlCA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;mDACkB,QAAM;mDAC9B,QAAM;mDACL,QAAM;;EAIvCA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;mDACqB,QAAM;mDAC9B,QAAM;mDACL,QAAM;;EAI1CA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;mDACsB,QAAM;mDAC9B,QAAM;mDACL,QAAM;;EAI3CA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA8I;EAA3IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAuC;EAAjCD,KAAK,EAAC;AAAc,GAAC,OAAK,E;;EAC/HA,KAAK,EAAC;AAAgB;mDAC0B,OAAK;mDAC7B,OAAK;mDACJ,OAAK;;EAI9CA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;mDACiB,QAAM;mDAC9B,QAAM;;EAIrCA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA8I;EAA3IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAuC;EAAjCD,KAAK,EAAC;AAAc,GAAC,OAAK,E;;EAC/HA,KAAK,EAAC;AAAgB;mDACmB,OAAK;mDAC7B,OAAK;;EAItCA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA6I;EAA1IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAsC;EAAhCD,KAAK,EAAC;AAAc,GAAC,MAAI,E;;EAC9HA,KAAK,EAAC;AAAgB;mDACc,MAAI;mDAC5B,MAAI;mDACN,QAAM;;EAIhCA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA6I;EAA1IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAsC;EAAhCD,KAAK,EAAC;AAAc,GAAC,MAAI,E;;EAC9HA,KAAK,EAAC;AAAgB;mDACmB,MAAI;mDAC5B,MAAI;mDACH,MAAI;;EAItCA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;mDACmB,QAAM;mDAC9B,QAAM;mDACL,QAAM;;EAIxCA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;mDACc,QAAM;mDAC9B,QAAM;mDACR,QAAM;;EAIhCA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA6I;EAA1IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAsC;EAAhCD,KAAK,EAAC;AAAc,GAAC,MAAI,E;;EAC9HA,KAAK,EAAC;AAAgB;mDACgB,MAAI;mDAC5B,MAAI;;EAIlCA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA6I;EAA1IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAsC;EAAhCD,KAAK,EAAC;AAAc,GAAC,MAAI,E;;EAC9HA,KAAK,EAAC;AAAgB;mDACU,KAAG;mDAC9B,KAAG;mDACH,KAAG;mDACH,KAAG;;EAQVA,KAAK,EAAC;AAAyB;kEACrCC,mBAAA,CAEK;EAFDD,KAAK,EAAC;AAA8B,I,aACpCC,mBAAA,CAAmB,eAAZ,MAAI,E;;EAELD,KAAK,EAAC;AAAyB;kEACrCC,mBAAA,CAA6I;EAA1IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAsC;EAAhCD,KAAK,EAAC;AAAc,GAAC,MAAI,E;;EAC9HA,KAAK,EAAC;AAAgB;mDACoB,MAAI;mDAC5B,MAAI;mDACH,MAAI;;EAIvCA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA8I;EAA3IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAuC;EAAjCD,KAAK,EAAC;AAAc,GAAC,OAAK,E;;EAC/HA,KAAK,EAAC;AAAgB;mDACgB,OAAK;mDAC7B,OAAK;mDACP,QAAM;;EAIlCA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;mDACkB,QAAM;mDAC9B,QAAM;mDACL,QAAM;;EAIvCA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;mDACqB,QAAM;mDAC9B,QAAM;mDACL,QAAM;;EAI1CA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;mDACsB,QAAM;mDAC9B,QAAM;mDACL,QAAM;;EAI3CA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA8I;EAA3IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAuC;EAAjCD,KAAK,EAAC;AAAc,GAAC,OAAK,E;;EAC/HA,KAAK,EAAC;AAAgB;mDAC0B,OAAK;mDAC7B,OAAK;mDACJ,OAAK;;EAI9CA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;mDACiB,QAAM;mDAC9B,QAAM;;EAIrCA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA8I;EAA3IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAuC;EAAjCD,KAAK,EAAC;AAAc,GAAC,OAAK,E;;EAC/HA,KAAK,EAAC;AAAgB;mDACmB,OAAK;mDAC7B,OAAK;;EAItCA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA6I;EAA1IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAsC;EAAhCD,KAAK,EAAC;AAAc,GAAC,MAAI,E;;EAC9HA,KAAK,EAAC;AAAgB;mDACc,MAAI;mDAC5B,MAAI;mDACN,QAAM;;EAIhCA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA6I;EAA1IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAsC;EAAhCD,KAAK,EAAC;AAAc,GAAC,MAAI,E;;EAC9HA,KAAK,EAAC;AAAgB;mDACmB,MAAI;mDAC5B,MAAI;mDACH,MAAI;;EAItCA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;mDACmB,QAAM;mDAC9B,QAAM;mDACL,QAAM;;EAIxCA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA+I;EAA5IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAwC;EAAlCD,KAAK,EAAC;AAAc,GAAC,QAAM,E;;EAChIA,KAAK,EAAC;AAAgB;mDACc,QAAM;mDAC9B,QAAM;mDACR,QAAM;;EAIhCA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA6I;EAA1IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAsC;EAAhCD,KAAK,EAAC;AAAc,GAAC,MAAI,E;;EAC9HA,KAAK,EAAC;AAAgB;mDACgB,MAAI;mDAC5B,MAAI;;EAIlCA,KAAK,EAAC;AAAyB;kEACnBC,mBAAA,CAA6I;EAA1IC,IAAI,EAAC,IAAI;EAACF,KAAK,EAAC;iBAAWC,mBAAA,CAAqE;EAA/DD,KAAK,EAAC;AAAc,I,aAACC,mBAAA,CAAmC;EAAhCD,KAAK,EAAC;AAAqB,G,gBAAYC,mBAAA,CAAsC;EAAhCD,KAAK,EAAC;AAAc,GAAC,MAAI,E;;EAC9HA,KAAK,EAAC;AAAgB;mDACU,KAAG;mDAC9B,KAAG;mDACH,KAAG;mDACH,KAAG;;;uBAviBnCG,mBAAA,CAkjBS,OAljBTC,UAkjBS,GAjjBFH,mBAAA,CAgjBM,OAhjBNI,UAgjBM,GA/iBFC,UAQM,EACNL,mBAAA,CAqiBM,OAriBNM,UAqiBM,G,gBApiBFN,mBAAA,CAoJK,MApJLO,UAoJK,GAlJEC,UAME,EAEHR,mBAAA,CASG,MATHS,UASG,GARDC,UAA6I,EAC7IV,mBAAA,CAMK,MANLW,UAMK,GALIX,mBAAA,CAAgE,aAA3DY,YAAA,CAAsDC,sBAAA;IAAzCC,EAAE,EAAC;EAAkB;sBAAC,MAAK,C;;QAC5Ed,mBAAA,CAAmE,aAA9DY,YAAA,CAAyDC,sBAAA;IAA5CC,EAAE,EAAC;EAAqB;sBAAC,MAAK,C;;QACrBd,mBAAA,CAA4D,aAAvDY,YAAA,CAAkDC,sBAAA;IAArCC,EAAE,EAAC;EAAe;sBAAC,MAAI,C;;QACpEd,mBAAA,CAA+D,aAA1DY,YAAA,CAAqDC,sBAAA;IAAxCC,EAAE,EAAC;EAAkB;sBAAC,MAAI,C;;YAKrBd,mBAAA,CAOA,MAPAe,WAOA,GANDC,WAA6I,EAC7IhB,mBAAA,CAIK,MAJLiB,WAIK,GAHAjB,mBAAA,CAA0D,aAArDY,YAAA,CAAgDC,sBAAA;IAAnCC,EAAE,EAAC;EAAa;sBAAC,MAAI,C;;QAClEd,mBAAA,CAA6D,aAAxDY,YAAA,CAAmDC,sBAAA;IAAtCC,EAAE,EAAC;EAAgB;sBAAC,MAAI,C;;YAKjBd,mBAAA,CAOF,MAPEkB,WAOF,GANDC,WAA+I,EAC/InB,mBAAA,CAIK,MAJLoB,WAIK,GAHApB,mBAAA,CAA4D,aAAvDY,YAAA,CAAkDC,sBAAA;IAArCC,EAAE,EAAC;EAAa;sBAAC,MAAM,C;;QACpEd,mBAAA,CAA+D,aAA1DY,YAAA,CAAqDC,sBAAA;IAAxCC,EAAE,EAAC;EAAgB;sBAAC,MAAM,C;;YAKrBd,mBAAA,CAOA,MAPAqB,WAOA,GANDC,WAA8I,EAC9ItB,mBAAA,CAIK,MAJLuB,WAIK,GAHAvB,mBAAA,CAA6D,aAAxDY,YAAA,CAAmDC,sBAAA;IAAtCC,EAAE,EAAC;EAAe;sBAAC,MAAK,C;;QACrEd,mBAAA,CAAgE,aAA3DY,YAAA,CAAsDC,sBAAA;IAAzCC,EAAE,EAAC;EAAkB;sBAAC,MAAK,C;;YAMtBd,mBAAA,CAOA,MAPAwB,WAOA,GANDC,WAA6I,EAC7IzB,mBAAA,CAIK,MAJL0B,WAIK,GAHA1B,mBAAA,CAAgE,aAA3DY,YAAA,CAAsDC,sBAAA;IAAzCC,EAAE,EAAC;EAAmB;sBAAC,MAAI,C;;QACxEd,mBAAA,CAAmE,aAA9DY,YAAA,CAAyDC,sBAAA;IAA5CC,EAAE,EAAC;EAAsB;sBAAC,MAAI,C;;YAKxBd,mBAAA,CAOD,MAPC2B,WAOD,GANDC,WAA+I,EAC/I5B,mBAAA,CAIK,MAJL6B,WAIK,GAHA7B,mBAAA,CAAoE,aAA/DY,YAAA,CAA0DC,sBAAA;IAA7CC,EAAE,EAAC;EAAqB;sBAAC,MAAM,C;;QAC5Ed,mBAAA,CAAuE,aAAlEY,YAAA,CAA6DC,sBAAA;IAAhDC,EAAE,EAAC;EAAwB;sBAAC,MAAM,C;;YAM/Cd,mBAAA,CASkB,MATlB8B,WASkB,GARDC,WAA6I,EAC7I/B,mBAAA,CAMK,MANLgC,WAMK,GALJhC,mBAAA,CAA+D,aAA1DY,YAAA,CAAqDC,sBAAA;IAAxCC,EAAE,EAAC;EAAgB;sBAAC,MAAM,C;;QACnEd,mBAAA,CAAkE,aAA7DY,YAAA,CAAwDC,sBAAA;IAA3CC,EAAE,EAAC;EAAmB;sBAAC,MAAM,C;;QAC/Cd,mBAAA,CAAkE,aAA7DY,YAAA,CAAwDC,sBAAA;IAA3CC,EAAE,EAAC;EAAqB;sBAAC,MAAI,C;;QAC/Cd,mBAAA,CAAmE,aAA9DY,YAAA,CAAyDC,sBAAA;IAA5CC,EAAE,EAAC;EAAsB;sBAAC,MAAI,C;;YAM3Cd,mBAAA,CAQkB,MARlBiC,WAQkB,GAPDC,WAA8I,EAC9IlC,mBAAA,CAKK,MALLmC,WAKK,GAJAnC,mBAAA,CAAuE,aAAlEY,YAAA,CAA6DC,sBAAA;IAAhDC,EAAE,EAAC;EAAyB;sBAAC,MAAK,C;;QAC/Ed,mBAAA,CAA0E,aAArEY,YAAA,CAAgEC,sBAAA;IAAnDC,EAAE,EAAC;EAA4B;sBAAC,MAAK,C;;QACvDd,mBAAA,CAA2E,aAAtEY,YAAA,CAAiEC,sBAAA;IAApDC,EAAE,EAAC;EAA6B;sBAAC,MAAK,C;;YAOnDd,mBAAA,CAQkB,MARlBoC,WAQkB,GAPDC,WAA+I,EAC/IrC,mBAAA,CAKK,MALLsC,WAKK,GAJAtC,mBAAA,CAAgE,aAA3DY,YAAA,CAAsDC,sBAAA;IAAzCC,EAAE,EAAC;EAAiB;sBAAC,MAAM,C;;QACxEd,mBAAA,CAAmE,aAA9DY,YAAA,CAAyDC,sBAAA;IAA5CC,EAAE,EAAC;EAAoB;sBAAC,MAAM,C;;QAChDd,mBAAA,CAAoE,aAA/DY,YAAA,CAA0DC,sBAAA;IAA7CC,EAAE,EAAC;EAAqB;sBAAC,MAAM,C;;YAM5Cd,mBAAA,CAQkB,MARlBuC,WAQkB,GAPDC,WAA+I,EAC/IxC,mBAAA,CAKK,MALLyC,WAKK,GAJAzC,mBAAA,CAAiE,aAA5DY,YAAA,CAAuDC,sBAAA;IAA1CC,EAAE,EAAC;EAAkB;sBAAC,MAAM,C;;QACzEd,mBAAA,CAAoE,aAA/DY,YAAA,CAA0DC,sBAAA;IAA7CC,EAAE,EAAC;EAAqB;sBAAC,MAAM,C;;QACjDd,mBAAA,CAAqE,aAAhEY,YAAA,CAA2DC,sBAAA;IAA9CC,EAAE,EAAC;EAAsB;sBAAC,MAAM,C;;YAM7Cd,mBAAA,CAQkB,MARlB0C,WAQkB,GAPDC,WAA+I,EAC/I3C,mBAAA,CAKK,MALL4C,WAKK,GAJA5C,mBAAA,CAAmE,aAA9DY,YAAA,CAAyDC,sBAAA;IAA5CC,EAAE,EAAC;EAAoB;sBAAC,MAAM,C;;QAC3Ed,mBAAA,CAAsE,aAAjEY,YAAA,CAA4DC,sBAAA;IAA/CC,EAAE,EAAC;EAAuB;sBAAC,MAAM,C;;QACnDd,mBAAA,CAAuE,aAAlEY,YAAA,CAA6DC,sBAAA;IAAhDC,EAAE,EAAC;EAAwB;sBAAC,MAAM,C;;YAQ/Cd,mBAAA,CASkB,MATlB6C,WASkB,GARDC,WAA6I,EAC7I9C,mBAAA,CAMK,MANL+C,WAMK,GALA/C,mBAAA,CAAwD,aAAnDY,YAAA,CAA8CC,sBAAA;IAAjCC,EAAE,EAAC;EAAS;sBAAC,MAAM,C;;QAChEd,mBAAA,CAAyD,aAApDY,YAAA,CAA+CC,sBAAA;IAAlCC,EAAE,EAAC;EAAS;sBAAC,MAAO,C;;QACtCd,mBAAA,CAAwD,aAAnDY,YAAA,CAA8CC,sBAAA;IAAjCC,EAAE,EAAC;EAAS;sBAAC,MAAM,C;;YAMjBd,mBAAA,CAOG,MAPHgD,WAOG,GANDC,WAA6I,EAC7IjD,mBAAA,CAIK,MAJLkD,WAIK,GAF3BlD,mBAAA,CAAwD,aAAnDY,YAAA,CAA8CC,sBAAA;IAAjCC,EAAE,EAAC;EAAW;sBAAC,MAAI,C;;8CA9IqBqC,KAAA,CAAAC,IAAI,W,mBAsJ1CpD,mBAAA,CAkID,MAlICqD,WAkID,GAjIDC,WAEK,EACAtD,mBAAA,CAQA,MARAuD,WAQA,GAPDC,WAA6I,EAC7IxD,mBAAA,CAKK,MALLyD,WAKK,GAJAzD,mBAAA,CAAgE,aAA3DY,YAAA,CAAsDC,sBAAA;IAAzCC,EAAE,EAAC;EAAmB;sBAAC,MAAI,C;;QACxEd,mBAAA,CAAmE,aAA9DY,YAAA,CAAyDC,sBAAA;IAA5CC,EAAE,EAAC;EAAsB;sBAAC,MAAI,C;;QAChDd,mBAAA,CAAoE,aAA/DY,YAAA,CAA0DC,sBAAA;IAA7CC,EAAE,EAAC;EAAuB;sBAAC,MAAI,C;;YAI5Cd,mBAAA,CAQkB,MARlB0D,WAQkB,GAPDC,WAA8I,EAC9I3D,mBAAA,CAKK,MALL4D,WAKK,GAJA5D,mBAAA,CAA6D,aAAxDY,YAAA,CAAmDC,sBAAA;IAAtCC,EAAE,EAAC;EAAe;sBAAC,MAAK,C;;QACrEd,mBAAA,CAAgE,aAA3DY,YAAA,CAAsDC,sBAAA;IAAzCC,EAAE,EAAC;EAAkB;sBAAC,MAAK,C;;QAC7Cd,mBAAA,CAA+D,aAA1DY,YAAA,CAAqDC,sBAAA;IAAxCC,EAAE,EAAC;EAAgB;sBAAC,MAAM,C;;YAIvCd,mBAAA,CAQkB,MARlB6D,WAQkB,GAPDC,WAA+I,EAC/I9D,mBAAA,CAKK,MALL+D,WAKK,GAJA/D,mBAAA,CAAgE,aAA3DY,YAAA,CAAsDC,sBAAA;IAAzCC,EAAE,EAAC;EAAiB;sBAAC,MAAM,C;;QACxEd,mBAAA,CAAmE,aAA9DY,YAAA,CAAyDC,sBAAA;IAA5CC,EAAE,EAAC;EAAoB;sBAAC,MAAM,C;;QAChDd,mBAAA,CAAoE,aAA/DY,YAAA,CAA0DC,sBAAA;IAA7CC,EAAE,EAAC;EAAqB;sBAAC,MAAM,C;;YAI5Cd,mBAAA,CAQkB,MARlBgE,YAQkB,GAPDC,YAA+I,EAC/IjE,mBAAA,CAKK,MALLkE,YAKK,GAJAlE,mBAAA,CAAmE,aAA9DY,YAAA,CAAyDC,sBAAA;IAA5CC,EAAE,EAAC;EAAoB;sBAAC,MAAM,C;;QAC3Ed,mBAAA,CAAsE,aAAjEY,YAAA,CAA4DC,sBAAA;IAA/CC,EAAE,EAAC;EAAuB;sBAAC,MAAM,C;;QACnDd,mBAAA,CAAuE,aAAlEY,YAAA,CAA6DC,sBAAA;IAAhDC,EAAE,EAAC;EAAwB;sBAAC,MAAM,C;;YAI/Cd,mBAAA,CAQkB,MARlBmE,YAQkB,GAPDC,YAA+I,EAC/IpE,mBAAA,CAKK,MALLqE,YAKK,GAJArE,mBAAA,CAAoE,aAA/DY,YAAA,CAA0DC,sBAAA;IAA7CC,EAAE,EAAC;EAAqB;sBAAC,MAAM,C;;QAC5Ed,mBAAA,CAAuE,aAAlEY,YAAA,CAA6DC,sBAAA;IAAhDC,EAAE,EAAC;EAAwB;sBAAC,MAAM,C;;QACpDd,mBAAA,CAAwE,aAAnEY,YAAA,CAA8DC,sBAAA;IAAjDC,EAAE,EAAC;EAAyB;sBAAC,MAAM,C;;YAIhDd,mBAAA,CAQkB,MARlBsE,YAQkB,GAPDC,YAA8I,EAC9IvE,mBAAA,CAKK,MALLwE,YAKK,GAJAxE,mBAAA,CAAuE,aAAlEY,YAAA,CAA6DC,sBAAA;IAAhDC,EAAE,EAAC;EAAyB;sBAAC,MAAK,C;;QAC/Ed,mBAAA,CAA0E,aAArEY,YAAA,CAAgEC,sBAAA;IAAnDC,EAAE,EAAC;EAA4B;sBAAC,MAAK,C;;QACvDd,mBAAA,CAA2E,aAAtEY,YAAA,CAAiEC,sBAAA;IAApDC,EAAE,EAAC;EAA6B;sBAAC,MAAK,C;;YAInDd,mBAAA,CAOkB,MAPlByE,YAOkB,GANDC,YAA+I,EAC/I1E,mBAAA,CAIK,MAJL2E,YAIK,GAHA3E,mBAAA,CAA+D,aAA1DY,YAAA,CAAqDC,sBAAA;IAAxCC,EAAE,EAAC;EAAgB;sBAAC,MAAM,C;;QACvEd,mBAAA,CAAkE,aAA7DY,YAAA,CAAwDC,sBAAA;IAA3CC,EAAE,EAAC;EAAmB;sBAAC,MAAM,C;;YAI1Cd,mBAAA,CAOkB,MAPlB4E,YAOkB,GANDC,YAA8I,EAC9I7E,mBAAA,CAIK,MAJL8E,YAIK,GAHA9E,mBAAA,CAAgE,aAA3DY,YAAA,CAAsDC,sBAAA;IAAzCC,EAAE,EAAC;EAAkB;sBAAC,MAAK,C;;QACxEd,mBAAA,CAAmE,aAA9DY,YAAA,CAAyDC,sBAAA;IAA5CC,EAAE,EAAC;EAAqB;sBAAC,MAAK,C;;YAI3Cd,mBAAA,CAQkB,MARlB+E,YAQkB,GAPDC,YAA6I,EAC7IhF,mBAAA,CAKK,MALLiF,YAKK,GAJAjF,mBAAA,CAA0D,aAArDY,YAAA,CAAgDC,sBAAA;IAAnCC,EAAE,EAAC;EAAa;sBAAC,MAAI,C;;QAClEd,mBAAA,CAA6D,aAAxDY,YAAA,CAAmDC,sBAAA;IAAtCC,EAAE,EAAC;EAAgB;sBAAC,MAAI,C;;QAC1Cd,mBAAA,CAA6D,aAAxDY,YAAA,CAAmDC,sBAAA;IAAtCC,EAAE,EAAC;EAAc;sBAAC,MAAM,C;;YAIrCd,mBAAA,CAQkB,MARlBkF,YAQkB,GAPDC,YAA6I,EAC7InF,mBAAA,CAKK,MALLoF,YAKK,GAJApF,mBAAA,CAA+D,aAA1DY,YAAA,CAAqDC,sBAAA;IAAxCC,EAAE,EAAC;EAAkB;sBAAC,MAAI,C;;QACvEd,mBAAA,CAAkE,aAA7DY,YAAA,CAAwDC,sBAAA;IAA3CC,EAAE,EAAC;EAAqB;sBAAC,MAAI,C;;QAC/Cd,mBAAA,CAAmE,aAA9DY,YAAA,CAAyDC,sBAAA;IAA5CC,EAAE,EAAC;EAAsB;sBAAC,MAAI,C;;YAI3Cd,mBAAA,CAQkB,MARlBqF,YAQkB,GAPDC,YAA+I,EAC/ItF,mBAAA,CAKK,MALLuF,YAKK,GAJAvF,mBAAA,CAAiE,aAA5DY,YAAA,CAAuDC,sBAAA;IAA1CC,EAAE,EAAC;EAAkB;sBAAC,MAAM,C;;QACzEd,mBAAA,CAAoE,aAA/DY,YAAA,CAA0DC,sBAAA;IAA7CC,EAAE,EAAC;EAAqB;sBAAC,MAAM,C;;QACjDd,mBAAA,CAAqE,aAAhEY,YAAA,CAA2DC,sBAAA;IAA9CC,EAAE,EAAC;EAAsB;sBAAC,MAAM,C;;YAI7Cd,mBAAA,CAQkB,MARlBwF,YAQkB,GAPDC,YAA+I,EAC/IzF,mBAAA,CAKK,MALL0F,YAKK,GAJA1F,mBAAA,CAA4D,aAAvDY,YAAA,CAAkDC,sBAAA;IAArCC,EAAE,EAAC;EAAa;sBAAC,MAAM,C;;QACpEd,mBAAA,CAA+D,aAA1DY,YAAA,CAAqDC,sBAAA;IAAxCC,EAAE,EAAC;EAAgB;sBAAC,MAAM,C;;QAC5Cd,mBAAA,CAA6D,aAAxDY,YAAA,CAAmDC,sBAAA;IAAtCC,EAAE,EAAC;EAAc;sBAAC,MAAM,C;;YAIrCd,mBAAA,CAOkB,MAPlB2F,YAOkB,GANDC,YAA6I,EAC7I5F,mBAAA,CAIK,MAJL6F,YAIK,GAHA7F,mBAAA,CAA4D,aAAvDY,YAAA,CAAkDC,sBAAA;IAArCC,EAAE,EAAC;EAAe;sBAAC,MAAI,C;;QACpEd,mBAAA,CAA+D,aAA1DY,YAAA,CAAqDC,sBAAA;IAAxCC,EAAE,EAAC;EAAkB;sBAAC,MAAI,C;;YAIvCd,mBAAA,CASkB,MATlB8F,YASkB,GARDC,YAA6I,EAC7I/F,mBAAA,CAMK,MANLgG,YAMK,GALAhG,mBAAA,CAAqD,aAAhDY,YAAA,CAA2CC,sBAAA;IAA9BC,EAAE,EAAC;EAAS;sBAAC,MAAG,C;;QAC7Dd,mBAAA,CAAqD,aAAhDY,YAAA,CAA2CC,sBAAA;IAA9BC,EAAE,EAAC;EAAS;sBAAC,MAAG,C;;QAClCd,mBAAA,CAAqD,aAAhDY,YAAA,CAA2CC,sBAAA;IAA9BC,EAAE,EAAC;EAAS;sBAAC,MAAG,C;;QAClCd,mBAAA,CAAqD,aAAhDY,YAAA,CAA2CC,sBAAA;IAA9BC,EAAE,EAAC;EAAS;sBAAC,MAAG,C;;8CA5H8BqC,KAAA,CAAAC,IAAI,U,mBAqIhDpD,mBAAA,CAkID,MAlICiG,YAkID,GAjIDC,YAEK,EACAlG,mBAAA,CAQA,MARAmG,YAQA,GAPDC,YAA6I,EAC7IpG,mBAAA,CAKK,MALLqG,YAKK,GAJArG,mBAAA,CAAgE,aAA3DY,YAAA,CAAsDC,sBAAA;IAAzCC,EAAE,EAAC;EAAmB;sBAAC,MAAI,C;;QACxEd,mBAAA,CAAmE,aAA9DY,YAAA,CAAyDC,sBAAA;IAA5CC,EAAE,EAAC;EAAsB;sBAAC,MAAI,C;;QAChDd,mBAAA,CAAoE,aAA/DY,YAAA,CAA0DC,sBAAA;IAA7CC,EAAE,EAAC;EAAuB;sBAAC,MAAI,C;;YAI5Cd,mBAAA,CAQkB,MARlBsG,YAQkB,GAPDC,YAA8I,EAC9IvG,mBAAA,CAKK,MALLwG,YAKK,GAJAxG,mBAAA,CAA6D,aAAxDY,YAAA,CAAmDC,sBAAA;IAAtCC,EAAE,EAAC;EAAe;sBAAC,MAAK,C;;QACrEd,mBAAA,CAAgE,aAA3DY,YAAA,CAAsDC,sBAAA;IAAzCC,EAAE,EAAC;EAAkB;sBAAC,MAAK,C;;QAC7Cd,mBAAA,CAA+D,aAA1DY,YAAA,CAAqDC,sBAAA;IAAxCC,EAAE,EAAC;EAAgB;sBAAC,MAAM,C;;YAIvCd,mBAAA,CAQkB,MARlByG,YAQkB,GAPDC,YAA+I,EAC/I1G,mBAAA,CAKK,MALL2G,YAKK,GAJA3G,mBAAA,CAAgE,aAA3DY,YAAA,CAAsDC,sBAAA;IAAzCC,EAAE,EAAC;EAAiB;sBAAC,MAAM,C;;QACxEd,mBAAA,CAAmE,aAA9DY,YAAA,CAAyDC,sBAAA;IAA5CC,EAAE,EAAC;EAAoB;sBAAC,MAAM,C;;QAChDd,mBAAA,CAAoE,aAA/DY,YAAA,CAA0DC,sBAAA;IAA7CC,EAAE,EAAC;EAAqB;sBAAC,MAAM,C;;YAI5Cd,mBAAA,CAQkB,MARlB4G,YAQkB,GAPDC,YAA+I,EAC/I7G,mBAAA,CAKK,MALL8G,YAKK,GAJA9G,mBAAA,CAAmE,aAA9DY,YAAA,CAAyDC,sBAAA;IAA5CC,EAAE,EAAC;EAAoB;sBAAC,MAAM,C;;QAC3Ed,mBAAA,CAAsE,aAAjEY,YAAA,CAA4DC,sBAAA;IAA/CC,EAAE,EAAC;EAAuB;sBAAC,MAAM,C;;QACnDd,mBAAA,CAAuE,aAAlEY,YAAA,CAA6DC,sBAAA;IAAhDC,EAAE,EAAC;EAAwB;sBAAC,MAAM,C;;YAI/Cd,mBAAA,CAQkB,MARlB+G,YAQkB,GAPDC,YAA+I,EAC/IhH,mBAAA,CAKK,MALLiH,YAKK,GAJAjH,mBAAA,CAAoE,aAA/DY,YAAA,CAA0DC,sBAAA;IAA7CC,EAAE,EAAC;EAAqB;sBAAC,MAAM,C;;QAC5Ed,mBAAA,CAAuE,aAAlEY,YAAA,CAA6DC,sBAAA;IAAhDC,EAAE,EAAC;EAAwB;sBAAC,MAAM,C;;QACpDd,mBAAA,CAAwE,aAAnEY,YAAA,CAA8DC,sBAAA;IAAjDC,EAAE,EAAC;EAAyB;sBAAC,MAAM,C;;YAIhDd,mBAAA,CAQkB,MARlBkH,YAQkB,GAPDC,YAA8I,EAC9InH,mBAAA,CAKK,MALLoH,YAKK,GAJApH,mBAAA,CAAuE,aAAlEY,YAAA,CAA6DC,sBAAA;IAAhDC,EAAE,EAAC;EAAyB;sBAAC,MAAK,C;;QAC/Ed,mBAAA,CAA0E,aAArEY,YAAA,CAAgEC,sBAAA;IAAnDC,EAAE,EAAC;EAA4B;sBAAC,MAAK,C;;QACvDd,mBAAA,CAA2E,aAAtEY,YAAA,CAAiEC,sBAAA;IAApDC,EAAE,EAAC;EAA6B;sBAAC,MAAK,C;;YAInDd,mBAAA,CAOkB,MAPlBqH,YAOkB,GANDC,YAA+I,EAC/ItH,mBAAA,CAIK,MAJLuH,YAIK,GAHAvH,mBAAA,CAA+D,aAA1DY,YAAA,CAAqDC,sBAAA;IAAxCC,EAAE,EAAC;EAAgB;sBAAC,MAAM,C;;QACvEd,mBAAA,CAAkE,aAA7DY,YAAA,CAAwDC,sBAAA;IAA3CC,EAAE,EAAC;EAAmB;sBAAC,MAAM,C;;YAI1Cd,mBAAA,CAOkB,MAPlBwH,YAOkB,GANDC,YAA8I,EAC9IzH,mBAAA,CAIK,MAJL0H,YAIK,GAHA1H,mBAAA,CAAgE,aAA3DY,YAAA,CAAsDC,sBAAA;IAAzCC,EAAE,EAAC;EAAkB;sBAAC,MAAK,C;;QACxEd,mBAAA,CAAmE,aAA9DY,YAAA,CAAyDC,sBAAA;IAA5CC,EAAE,EAAC;EAAqB;sBAAC,MAAK,C;;YAI3Cd,mBAAA,CAQkB,MARlB2H,YAQkB,GAPDC,YAA6I,EAC7I5H,mBAAA,CAKK,MALL6H,YAKK,GAJA7H,mBAAA,CAA0D,aAArDY,YAAA,CAAgDC,sBAAA;IAAnCC,EAAE,EAAC;EAAa;sBAAC,MAAI,C;;QAClEd,mBAAA,CAA6D,aAAxDY,YAAA,CAAmDC,sBAAA;IAAtCC,EAAE,EAAC;EAAgB;sBAAC,MAAI,C;;QAC1Cd,mBAAA,CAA6D,aAAxDY,YAAA,CAAmDC,sBAAA;IAAtCC,EAAE,EAAC;EAAc;sBAAC,MAAM,C;;YAIrCd,mBAAA,CAQkB,MARlB8H,YAQkB,GAPDC,YAA6I,EAC7I/H,mBAAA,CAKK,MALLgI,YAKK,GAJAhI,mBAAA,CAA+D,aAA1DY,YAAA,CAAqDC,sBAAA;IAAxCC,EAAE,EAAC;EAAkB;sBAAC,MAAI,C;;QACvEd,mBAAA,CAAkE,aAA7DY,YAAA,CAAwDC,sBAAA;IAA3CC,EAAE,EAAC;EAAqB;sBAAC,MAAI,C;;QAC/Cd,mBAAA,CAAmE,aAA9DY,YAAA,CAAyDC,sBAAA;IAA5CC,EAAE,EAAC;EAAsB;sBAAC,MAAI,C;;YAI3Cd,mBAAA,CAQkB,MARlBiI,YAQkB,GAPDC,YAA+I,EAC/IlI,mBAAA,CAKK,MALLmI,YAKK,GAJAnI,mBAAA,CAAiE,aAA5DY,YAAA,CAAuDC,sBAAA;IAA1CC,EAAE,EAAC;EAAkB;sBAAC,MAAM,C;;QACzEd,mBAAA,CAAoE,aAA/DY,YAAA,CAA0DC,sBAAA;IAA7CC,EAAE,EAAC;EAAqB;sBAAC,MAAM,C;;QACjDd,mBAAA,CAAqE,aAAhEY,YAAA,CAA2DC,sBAAA;IAA9CC,EAAE,EAAC;EAAsB;sBAAC,MAAM,C;;YAI7Cd,mBAAA,CAQkB,MARlBoI,YAQkB,GAPDC,YAA+I,EAC/IrI,mBAAA,CAKK,MALLsI,YAKK,GAJAtI,mBAAA,CAA4D,aAAvDY,YAAA,CAAkDC,sBAAA;IAArCC,EAAE,EAAC;EAAa;sBAAC,MAAM,C;;QACpEd,mBAAA,CAA+D,aAA1DY,YAAA,CAAqDC,sBAAA;IAAxCC,EAAE,EAAC;EAAgB;sBAAC,MAAM,C;;QAC5Cd,mBAAA,CAA6D,aAAxDY,YAAA,CAAmDC,sBAAA;IAAtCC,EAAE,EAAC;EAAc;sBAAC,MAAM,C;;YAIrCd,mBAAA,CAOkB,MAPlBuI,YAOkB,GANDC,YAA6I,EAC7IxI,mBAAA,CAIK,MAJLyI,YAIK,GAHAzI,mBAAA,CAA4D,aAAvDY,YAAA,CAAkDC,sBAAA;IAArCC,EAAE,EAAC;EAAe;sBAAC,MAAI,C;;QACpEd,mBAAA,CAA+D,aAA1DY,YAAA,CAAqDC,sBAAA;IAAxCC,EAAE,EAAC;EAAkB;sBAAC,MAAI,C;;YAIvCd,mBAAA,CASkB,MATlB0I,YASkB,GARDC,YAA6I,EAC7I3I,mBAAA,CAMK,MANL4I,YAMK,GALA5I,mBAAA,CAAqD,aAAhDY,YAAA,CAA2CC,sBAAA;IAA9BC,EAAE,EAAC;EAAS;sBAAC,MAAG,C;;QAC7Dd,mBAAA,CAAqD,aAAhDY,YAAA,CAA2CC,sBAAA;IAA9BC,EAAE,EAAC;EAAS;sBAAC,MAAG,C;;QAClCd,mBAAA,CAAqD,aAAhDY,YAAA,CAA2CC,sBAAA;IAA9BC,EAAE,EAAC;EAAS;sBAAC,MAAG,C;;QAClCd,mBAAA,CAAqD,aAAhDY,YAAA,CAA2CC,sBAAA;IAA9BC,EAAE,EAAC;EAAS;sBAAC,MAAG,C;;8CA5H8BqC,KAAA,CAAAC,IAAI,W,mBAoIhDpD,mBAAA,CAkID,MAlIC6I,YAkID,GAjIDC,YAEK,EACA9I,mBAAA,CAQA,MARA+I,YAQA,GAPDC,YAA6I,EAC7IhJ,mBAAA,CAKK,MALLiJ,YAKK,GAJAjJ,mBAAA,CAAgE,aAA3DY,YAAA,CAAsDC,sBAAA;IAAzCC,EAAE,EAAC;EAAmB;sBAAC,MAAI,C;;QACxEd,mBAAA,CAAmE,aAA9DY,YAAA,CAAyDC,sBAAA;IAA5CC,EAAE,EAAC;EAAsB;sBAAC,MAAI,C;;QAChDd,mBAAA,CAAoE,aAA/DY,YAAA,CAA0DC,sBAAA;IAA7CC,EAAE,EAAC;EAAuB;sBAAC,MAAI,C;;YAI5Cd,mBAAA,CAQkB,MARlBkJ,YAQkB,GAPDC,YAA8I,EAC9InJ,mBAAA,CAKK,MALLoJ,YAKK,GAJApJ,mBAAA,CAA6D,aAAxDY,YAAA,CAAmDC,sBAAA;IAAtCC,EAAE,EAAC;EAAe;sBAAC,MAAK,C;;QACrEd,mBAAA,CAAgE,aAA3DY,YAAA,CAAsDC,sBAAA;IAAzCC,EAAE,EAAC;EAAkB;sBAAC,MAAK,C;;QAC7Cd,mBAAA,CAA+D,aAA1DY,YAAA,CAAqDC,sBAAA;IAAxCC,EAAE,EAAC;EAAgB;sBAAC,MAAM,C;;YAIvCd,mBAAA,CAQkB,MARlBqJ,YAQkB,GAPDC,YAA+I,EAC/ItJ,mBAAA,CAKK,MALLuJ,YAKK,GAJAvJ,mBAAA,CAAgE,aAA3DY,YAAA,CAAsDC,sBAAA;IAAzCC,EAAE,EAAC;EAAiB;sBAAC,MAAM,C;;QACxEd,mBAAA,CAAmE,aAA9DY,YAAA,CAAyDC,sBAAA;IAA5CC,EAAE,EAAC;EAAoB;sBAAC,MAAM,C;;QAChDd,mBAAA,CAAoE,aAA/DY,YAAA,CAA0DC,sBAAA;IAA7CC,EAAE,EAAC;EAAqB;sBAAC,MAAM,C;;YAI5Cd,mBAAA,CAQkB,MARlBwJ,YAQkB,GAPDC,YAA+I,EAC/IzJ,mBAAA,CAKK,MALL0J,YAKK,GAJA1J,mBAAA,CAAmE,aAA9DY,YAAA,CAAyDC,sBAAA;IAA5CC,EAAE,EAAC;EAAoB;sBAAC,MAAM,C;;QAC3Ed,mBAAA,CAAsE,aAAjEY,YAAA,CAA4DC,sBAAA;IAA/CC,EAAE,EAAC;EAAuB;sBAAC,MAAM,C;;QACnDd,mBAAA,CAAuE,aAAlEY,YAAA,CAA6DC,sBAAA;IAAhDC,EAAE,EAAC;EAAwB;sBAAC,MAAM,C;;YAI/Cd,mBAAA,CAQkB,MARlB2J,YAQkB,GAPDC,YAA+I,EAC/I5J,mBAAA,CAKK,MALL6J,YAKK,GAJA7J,mBAAA,CAAoE,aAA/DY,YAAA,CAA0DC,sBAAA;IAA7CC,EAAE,EAAC;EAAqB;sBAAC,MAAM,C;;QAC5Ed,mBAAA,CAAuE,aAAlEY,YAAA,CAA6DC,sBAAA;IAAhDC,EAAE,EAAC;EAAwB;sBAAC,MAAM,C;;QACpDd,mBAAA,CAAwE,aAAnEY,YAAA,CAA8DC,sBAAA;IAAjDC,EAAE,EAAC;EAAyB;sBAAC,MAAM,C;;YAIhDd,mBAAA,CAQkB,MARlB8J,YAQkB,GAPDC,YAA8I,EAC9I/J,mBAAA,CAKK,MALLgK,YAKK,GAJAhK,mBAAA,CAAuE,aAAlEY,YAAA,CAA6DC,sBAAA;IAAhDC,EAAE,EAAC;EAAyB;sBAAC,MAAK,C;;QAC/Ed,mBAAA,CAA0E,aAArEY,YAAA,CAAgEC,sBAAA;IAAnDC,EAAE,EAAC;EAA4B;sBAAC,MAAK,C;;QACvDd,mBAAA,CAA2E,aAAtEY,YAAA,CAAiEC,sBAAA;IAApDC,EAAE,EAAC;EAA6B;sBAAC,MAAK,C;;YAInDd,mBAAA,CAOkB,MAPlBiK,YAOkB,GANDC,YAA+I,EAC/IlK,mBAAA,CAIK,MAJLmK,YAIK,GAHAnK,mBAAA,CAA+D,aAA1DY,YAAA,CAAqDC,sBAAA;IAAxCC,EAAE,EAAC;EAAgB;sBAAC,MAAM,C;;QACvEd,mBAAA,CAAkE,aAA7DY,YAAA,CAAwDC,sBAAA;IAA3CC,EAAE,EAAC;EAAmB;sBAAC,MAAM,C;;YAI1Cd,mBAAA,CAOkB,MAPlBoK,YAOkB,GANDC,YAA8I,EAC9IrK,mBAAA,CAIK,MAJLsK,YAIK,GAHAtK,mBAAA,CAAgE,aAA3DY,YAAA,CAAsDC,sBAAA;IAAzCC,EAAE,EAAC;EAAkB;sBAAC,MAAK,C;;QACxEd,mBAAA,CAAmE,aAA9DY,YAAA,CAAyDC,sBAAA;IAA5CC,EAAE,EAAC;EAAqB;sBAAC,MAAK,C;;YAI3Cd,mBAAA,CAQkB,MARlBuK,YAQkB,GAPDC,YAA6I,EAC7IxK,mBAAA,CAKK,MALLyK,YAKK,GAJAzK,mBAAA,CAA0D,aAArDY,YAAA,CAAgDC,sBAAA;IAAnCC,EAAE,EAAC;EAAa;sBAAC,MAAI,C;;QAClEd,mBAAA,CAA6D,aAAxDY,YAAA,CAAmDC,sBAAA;IAAtCC,EAAE,EAAC;EAAgB;sBAAC,MAAI,C;;QAC1Cd,mBAAA,CAA6D,aAAxDY,YAAA,CAAmDC,sBAAA;IAAtCC,EAAE,EAAC;EAAc;sBAAC,MAAM,C;;YAIrCd,mBAAA,CAQkB,MARlB0K,YAQkB,GAPDC,YAA6I,EAC7I3K,mBAAA,CAKK,MALL4K,YAKK,GAJA5K,mBAAA,CAA+D,aAA1DY,YAAA,CAAqDC,sBAAA;IAAxCC,EAAE,EAAC;EAAkB;sBAAC,MAAI,C;;QACvEd,mBAAA,CAAkE,aAA7DY,YAAA,CAAwDC,sBAAA;IAA3CC,EAAE,EAAC;EAAqB;sBAAC,MAAI,C;;QAC/Cd,mBAAA,CAAmE,aAA9DY,YAAA,CAAyDC,sBAAA;IAA5CC,EAAE,EAAC;EAAsB;sBAAC,MAAI,C;;YAI3Cd,mBAAA,CAQkB,MARlB6K,YAQkB,GAPDC,YAA+I,EAC/I9K,mBAAA,CAKK,MALL+K,YAKK,GAJA/K,mBAAA,CAAiE,aAA5DY,YAAA,CAAuDC,sBAAA;IAA1CC,EAAE,EAAC;EAAkB;sBAAC,MAAM,C;;QACzEd,mBAAA,CAAoE,aAA/DY,YAAA,CAA0DC,sBAAA;IAA7CC,EAAE,EAAC;EAAqB;sBAAC,MAAM,C;;QACjDd,mBAAA,CAAqE,aAAhEY,YAAA,CAA2DC,sBAAA;IAA9CC,EAAE,EAAC;EAAsB;sBAAC,MAAM,C;;YAI7Cd,mBAAA,CAQkB,MARlBgL,YAQkB,GAPDC,YAA+I,EAC/IjL,mBAAA,CAKK,MALLkL,YAKK,GAJAlL,mBAAA,CAA4D,aAAvDY,YAAA,CAAkDC,sBAAA;IAArCC,EAAE,EAAC;EAAa;sBAAC,MAAM,C;;QACpEd,mBAAA,CAA+D,aAA1DY,YAAA,CAAqDC,sBAAA;IAAxCC,EAAE,EAAC;EAAgB;sBAAC,MAAM,C;;QAC5Cd,mBAAA,CAA6D,aAAxDY,YAAA,CAAmDC,sBAAA;IAAtCC,EAAE,EAAC;EAAc;sBAAC,MAAM,C;;YAIrCd,mBAAA,CAOkB,MAPlBmL,YAOkB,GANDC,YAA6I,EAC7IpL,mBAAA,CAIK,MAJLqL,YAIK,GAHArL,mBAAA,CAA4D,aAAvDY,YAAA,CAAkDC,sBAAA;IAArCC,EAAE,EAAC;EAAe;sBAAC,MAAI,C;;QACpEd,mBAAA,CAA+D,aAA1DY,YAAA,CAAqDC,sBAAA;IAAxCC,EAAE,EAAC;EAAkB;sBAAC,MAAI,C;;YAIvCd,mBAAA,CASkB,MATlBsL,YASkB,GARDC,YAA6I,EAC7IvL,mBAAA,CAMK,MANLwL,YAMK,GALAxL,mBAAA,CAAqD,aAAhDY,YAAA,CAA2CC,sBAAA;IAA9BC,EAAE,EAAC;EAAS;sBAAC,MAAG,C;;QAC7Dd,mBAAA,CAAqD,aAAhDY,YAAA,CAA2CC,sBAAA;IAA9BC,EAAE,EAAC;EAAS;sBAAC,MAAG,C;;QAClCd,mBAAA,CAAqD,aAAhDY,YAAA,CAA2CC,sBAAA;IAA9BC,EAAE,EAAC;EAAS;sBAAC,MAAG,C;;QAClCd,mBAAA,CAAqD,aAAhDY,YAAA,CAA2CC,sBAAA;IAA9BC,EAAE,EAAC;EAAS;sBAAC,MAAG,C;;8CA5H8BqC,KAAA,CAAAC,IAAI,Y"}]}