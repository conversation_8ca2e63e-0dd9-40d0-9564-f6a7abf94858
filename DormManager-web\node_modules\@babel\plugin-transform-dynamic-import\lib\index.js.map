{"version": 3, "names": ["_helper<PERSON>lugin<PERSON><PERSON>s", "require", "SUPPORTED_MODULES", "MODULES_NOT_FOUND", "_default", "declare", "api", "assertVersion", "name", "inherits", "default", "pre", "file", "set", "visitor", "Program", "modules", "get", "includes", "Error", "exports"], "sources": ["../src/index.ts"], "sourcesContent": ["import { declare } from \"@babel/helper-plugin-utils\";\n\nconst SUPPORTED_MODULES = [\"commonjs\", \"amd\", \"systemjs\"];\n\nconst MODULES_NOT_FOUND = `\\\n@babel/plugin-transform-dynamic-import depends on a modules\ntransform plugin. Supported plugins are:\n - @babel/plugin-transform-modules-commonjs ^7.4.0\n - @babel/plugin-transform-modules-amd ^7.4.0\n - @babel/plugin-transform-modules-systemjs ^7.4.0\n\nIf you are using Webpack or Rollup and thus don't want\n<PERSON><PERSON> to transpile your imports and exports, you can use\nthe @babel/plugin-syntax-dynamic-import plugin and let your\nbundler handle dynamic imports.\n`;\n\nexport default declare(api => {\n  api.assertVersion(7);\n\n  return {\n    name: \"transform-dynamic-import\",\n    inherits: USE_ESM\n      ? undefined\n      : IS_STANDALONE\n      ? undefined\n      : // eslint-disable-next-line no-restricted-globals\n        require(\"@babel/plugin-syntax-dynamic-import\").default,\n\n    pre() {\n      // We keep using the old name, for compatibility with older\n      // version of the CommonJS transform.\n      this.file.set(\n        \"@babel/plugin-proposal-dynamic-import\",\n        PACKAGE_JSON.version,\n      );\n    },\n\n    visitor: {\n      Program() {\n        const modules = this.file.get(\"@babel/plugin-transform-modules-*\");\n\n        if (!SUPPORTED_MODULES.includes(modules)) {\n          throw new Error(MODULES_NOT_FOUND);\n        }\n      },\n    },\n  };\n});\n"], "mappings": ";;;;;;AAAA,IAAAA,kBAAA,GAAAC,OAAA;AAEA,MAAMC,iBAAiB,GAAG,CAAC,UAAU,EAAE,KAAK,EAAE,UAAU,CAAC;AAEzD,MAAMC,iBAAiB,GAAI;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC,IAAAC,QAAA,GAEa,IAAAC,0BAAO,EAACC,GAAG,IAAI;EAC5BA,GAAG,CAACC,aAAa,CAAC,CAAC,CAAC;EAEpB,OAAO;IACLC,IAAI,EAAE,0BAA0B;IAChCC,QAAQ,EAKJR,OAAO,CAAC,qCAAqC,CAAC,CAACS,OAAO;IAE1DC,GAAGA,CAAA,EAAG;MAGJ,IAAI,CAACC,IAAI,CAACC,GAAG,CACX,uCAAuC,WAEzC,CAAC;IACH,CAAC;IAEDC,OAAO,EAAE;MACPC,OAAOA,CAAA,EAAG;QACR,MAAMC,OAAO,GAAG,IAAI,CAACJ,IAAI,CAACK,GAAG,CAAC,mCAAmC,CAAC;QAElE,IAAI,CAACf,iBAAiB,CAACgB,QAAQ,CAACF,OAAO,CAAC,EAAE;UACxC,MAAM,IAAIG,KAAK,CAAChB,iBAAiB,CAAC;QACpC;MACF;IACF;EACF,CAAC;AACH,CAAC,CAAC;AAAAiB,OAAA,CAAAV,OAAA,GAAAN,QAAA"}