{"version": 3, "names": ["_helper<PERSON>lugin<PERSON><PERSON>s", "require", "_default", "declare", "api", "assertVersion", "name", "manipulateOptions", "opts", "parserOpts", "plugins", "some", "p", "Array", "isArray", "push", "exports", "default"], "sources": ["../src/index.ts"], "sourcesContent": ["import { declare } from \"@babel/helper-plugin-utils\";\n\nexport default declare(api => {\n  api.assertVersion(7);\n\n  return {\n    name: \"syntax-jsx\",\n\n    manipulateOptions(opts, parserOpts) {\n      if (!process.env.BABEL_8_BREAKING) {\n        // If the Typescript plugin already ran, it will have decided whether\n        // or not this is a TSX file.\n        if (\n          parserOpts.plugins.some(\n            p => (Array.isArray(p) ? p[0] : p) === \"typescript\",\n          )\n        ) {\n          return;\n        }\n      }\n\n      parserOpts.plugins.push(\"jsx\");\n    },\n  };\n});\n"], "mappings": ";;;;;;AAAA,IAAAA,kBAAA,GAAAC,OAAA;AAAqD,IAAAC,QAAA,GAEtC,IAAAC,0BAAO,EAACC,GAAG,IAAI;EAC5BA,GAAG,CAACC,aAAa,CAAC,CAAC,CAAC;EAEpB,OAAO;IACLC,IAAI,EAAE,YAAY;IAElBC,iBAAiBA,CAACC,IAAI,EAAEC,UAAU,EAAE;MACC;QAGjC,IACEA,UAAU,CAACC,OAAO,CAACC,IAAI,CACrBC,CAAC,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,MAAM,YACzC,CAAC,EACD;UACA;QACF;MACF;MAEAH,UAAU,CAACC,OAAO,CAACK,IAAI,CAAC,KAAK,CAAC;IAChC;EACF,CAAC;AACH,CAAC,CAAC;AAAAC,OAAA,CAAAC,OAAA,GAAAf,QAAA"}