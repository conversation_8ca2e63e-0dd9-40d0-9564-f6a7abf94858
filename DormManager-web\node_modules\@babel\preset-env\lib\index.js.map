{"version": 3, "names": ["_semver", "require", "_debug", "_getOptionSpecificExcludes", "_filterItems", "_moduleTransformations", "_normalizeOptions", "_shippedProposals", "_pluginsCompatData", "_regenerator", "_babelPolyfill", "_babelPluginPolyfillCorejs", "_babelPluginPolyfillCorejs2", "_babelPluginPolyfillRegenerator", "_helperCompilationTargets", "_availablePlugins", "_helper<PERSON>lugin<PERSON><PERSON>s", "pluginCoreJS2", "_pluginCoreJS2", "default", "pluginCoreJS3", "_pluginCoreJS3", "pluginRegenerator", "_pluginRegenerator", "isPluginRequired", "targets", "support", "isRequired", "compatData", "filterStageFromList", "list", "stageList", "Object", "keys", "reduce", "result", "item", "has", "pluginLists", "withProposals", "withoutBugfixes", "pluginsList", "withBugfixes", "assign", "pluginsBugfixesList", "withoutProposals", "proposalPlugins", "getPluginList", "proposals", "bugfixes", "getPlugin", "pluginName", "plugin", "availablePlugins", "Error", "transformIncludesAndExcludes", "opts", "opt", "target", "match", "add", "all", "plugins", "Set", "builtIns", "exports", "getModulesPluginNames", "modules", "transformations", "shouldTransformESM", "shouldTransformDynamicImport", "shouldTransformExportNamespaceFrom", "modulesPluginNames", "push", "console", "warn", "getPolyfillPlugins", "useBuiltIns", "corejs", "polyfillTargets", "include", "exclude", "shippedProposals", "regenerator", "debug", "polyfillPlugins", "pluginOptions", "method", "version", "toString", "undefined", "noRuntimeName", "major", "legacyBabelPolyfillPlugin", "usage", "deprecated", "removeRegeneratorEntryPlugin", "getLocalTargets", "optionsTargets", "ignoreBrowserslistConfig", "config<PERSON><PERSON>", "browserslistEnv", "<PERSON><PERSON><PERSON><PERSON>", "browsers", "getTargets", "supportsStaticESM", "caller", "supportsDynamicImport", "supportsExportNamespaceFrom", "_default", "declarePreset", "api", "assertVersion", "babelTargets", "optionsExclude", "forceAllTransforms", "optionsInclude", "loose", "spec", "normalizeOptions", "semver", "lt", "hasUglifyTarget", "uglify", "transformTargets", "shouldSkipExportNamespaceFrom", "includes", "excludes", "moduleTransformations", "pluginNames", "filterItems", "getOptionSpecificExcludesFor", "pluginSyntaxMap", "addProposalSyntaxPlugins", "proposalSyntaxPlugins", "removeUnsupportedItems", "removeUnnecessaryItems", "overlappingPlugins", "pluginUseBuiltIns", "Array", "from", "map", "deprecatedAssertSyntax", "concat", "log", "JSON", "stringify", "prettifyTargets", "for<PERSON>ach", "logPlugin"], "sources": ["../src/index.ts"], "sourcesContent": ["import semver, { type SemVer } from \"semver\";\nimport { logPlugin } from \"./debug.ts\";\nimport getOptionSpecificExcludesFor from \"./get-option-specific-excludes.ts\";\nimport {\n  addProposalSyntaxPlugins,\n  removeUnnecessaryItems,\n  removeUnsupportedItems,\n} from \"./filter-items.ts\";\nimport moduleTransformations from \"./module-transformations.ts\";\nimport normalizeOptions from \"./normalize-options.ts\";\nimport {\n  pluginSyntaxMap,\n  proposalPlugins,\n  proposalSyntaxPlugins,\n} from \"./shipped-proposals.ts\";\nimport {\n  plugins as pluginsList,\n  pluginsBugfixes as pluginsBugfixesList,\n  overlappingPlugins,\n} from \"./plugins-compat-data.ts\";\n\nimport removeRegeneratorEntryPlugin from \"./polyfills/regenerator.ts\";\nimport legacyBabelPolyfillPlugin from \"./polyfills/babel-polyfill.ts\";\n\nimport type { CallerMetadata } from \"@babel/core\";\n\nimport _pluginCoreJS2 from \"babel-plugin-polyfill-corejs2\";\nimport _pluginCoreJS3 from \"babel-plugin-polyfill-corejs3\";\nimport _pluginRegenerator from \"babel-plugin-polyfill-regenerator\";\nconst pluginCoreJS2 = _pluginCoreJS2.default || _pluginCoreJS2;\nconst pluginCoreJS3 = _pluginCoreJS3.default || _pluginCoreJS3;\nconst pluginRegenerator = _pluginRegenerator.default || _pluginRegenerator;\n\nimport getTargets, {\n  prettifyTargets,\n  filterItems,\n  isRequired,\n} from \"@babel/helper-compilation-targets\";\nimport type { Targets, InputTargets } from \"@babel/helper-compilation-targets\";\nimport availablePlugins from \"./available-plugins.ts\";\nimport { declarePreset } from \"@babel/helper-plugin-utils\";\n\ntype ModuleTransformationsType =\n  typeof import(\"./module-transformations\").default;\nimport type { BuiltInsOption, ModuleOption, Options } from \"./types.ts\";\n\n// TODO: Remove in Babel 8\nexport function isPluginRequired(targets: Targets, support: Targets) {\n  return isRequired(\"fake-name\", targets, {\n    compatData: { \"fake-name\": support },\n  });\n}\n\nfunction filterStageFromList(\n  list: { [feature: string]: Targets },\n  stageList: Set<string>,\n) {\n  return Object.keys(list).reduce((result, item) => {\n    if (!stageList.has(item)) {\n      // @ts-expect-error todo: refine result types\n      result[item] = list[item];\n    }\n\n    return result;\n  }, {});\n}\n\nconst pluginLists = {\n  withProposals: {\n    withoutBugfixes: pluginsList,\n    withBugfixes: Object.assign({}, pluginsList, pluginsBugfixesList),\n  },\n  withoutProposals: {\n    withoutBugfixes: filterStageFromList(pluginsList, proposalPlugins),\n    withBugfixes: filterStageFromList(\n      Object.assign({}, pluginsList, pluginsBugfixesList),\n      proposalPlugins,\n    ),\n  },\n};\n\nfunction getPluginList(proposals: boolean, bugfixes: boolean) {\n  if (proposals) {\n    if (bugfixes) return pluginLists.withProposals.withBugfixes;\n    else return pluginLists.withProposals.withoutBugfixes;\n  } else {\n    if (bugfixes) return pluginLists.withoutProposals.withBugfixes;\n    else return pluginLists.withoutProposals.withoutBugfixes;\n  }\n}\n\nconst getPlugin = (pluginName: string) => {\n  const plugin =\n    // @ts-expect-error plugin name is constructed from available plugin list\n    availablePlugins[pluginName]();\n\n  if (!plugin) {\n    throw new Error(\n      `Could not find plugin \"${pluginName}\". Ensure there is an entry in ./available-plugins.js for it.`,\n    );\n  }\n\n  return plugin;\n};\n\nexport const transformIncludesAndExcludes = (opts: Array<string>): any => {\n  return opts.reduce(\n    (result, opt) => {\n      const target = opt.match(/^(es|es6|es7|esnext|web)\\./)\n        ? \"builtIns\"\n        : \"plugins\";\n      result[target].add(opt);\n      return result;\n    },\n    {\n      all: opts,\n      plugins: new Set(),\n      builtIns: new Set(),\n    },\n  );\n};\n\nexport const getModulesPluginNames = ({\n  modules,\n  transformations,\n  shouldTransformESM,\n  shouldTransformDynamicImport,\n  shouldTransformExportNamespaceFrom,\n}: {\n  modules: ModuleOption;\n  transformations: ModuleTransformationsType;\n  shouldTransformESM: boolean;\n  shouldTransformDynamicImport: boolean;\n  shouldTransformExportNamespaceFrom: boolean;\n}) => {\n  const modulesPluginNames = [];\n  if (modules !== false && transformations[modules]) {\n    if (shouldTransformESM) {\n      modulesPluginNames.push(transformations[modules]);\n    }\n\n    if (shouldTransformDynamicImport) {\n      if (shouldTransformESM && modules !== \"umd\") {\n        modulesPluginNames.push(\"transform-dynamic-import\");\n      } else {\n        console.warn(\n          \"Dynamic import can only be transformed when transforming ES\" +\n            \" modules to AMD, CommonJS or SystemJS.\",\n        );\n      }\n    }\n  }\n\n  if (shouldTransformExportNamespaceFrom) {\n    modulesPluginNames.push(\"transform-export-namespace-from\");\n  }\n\n  if (!process.env.BABEL_8_BREAKING) {\n    // Enable module-related syntax plugins for older Babel versions\n    if (!shouldTransformDynamicImport) {\n      modulesPluginNames.push(\"syntax-dynamic-import\");\n    }\n    if (!shouldTransformExportNamespaceFrom) {\n      modulesPluginNames.push(\"syntax-export-namespace-from\");\n    }\n    modulesPluginNames.push(\"syntax-top-level-await\");\n    modulesPluginNames.push(\"syntax-import-meta\");\n  }\n\n  return modulesPluginNames;\n};\n\nexport const getPolyfillPlugins = ({\n  useBuiltIns,\n  corejs,\n  polyfillTargets,\n  include,\n  exclude,\n  proposals,\n  shippedProposals,\n  regenerator,\n  debug,\n}: {\n  useBuiltIns: BuiltInsOption;\n  corejs: SemVer | null | false;\n  polyfillTargets: Targets;\n  include: Set<string>;\n  exclude: Set<string>;\n  proposals: boolean;\n  shippedProposals: boolean;\n  regenerator: boolean;\n  debug: boolean;\n}) => {\n  const polyfillPlugins = [];\n  if (useBuiltIns === \"usage\" || useBuiltIns === \"entry\") {\n    const pluginOptions = {\n      method: `${useBuiltIns}-global`,\n      version: corejs ? corejs.toString() : undefined,\n      targets: polyfillTargets,\n      include,\n      exclude,\n      proposals,\n      shippedProposals,\n      debug,\n      \"#__secret_key__@babel/preset-env__compatibility\": {\n        noRuntimeName: true,\n      },\n    };\n\n    if (corejs) {\n      if (useBuiltIns === \"usage\") {\n        if (corejs.major === 2) {\n          polyfillPlugins.push(\n            [pluginCoreJS2, pluginOptions],\n            [legacyBabelPolyfillPlugin, { usage: true }],\n          );\n        } else {\n          polyfillPlugins.push(\n            [pluginCoreJS3, pluginOptions],\n            [legacyBabelPolyfillPlugin, { usage: true, deprecated: true }],\n          );\n        }\n        if (regenerator) {\n          polyfillPlugins.push([\n            pluginRegenerator,\n            { method: \"usage-global\", debug },\n          ]);\n        }\n      } else {\n        if (corejs.major === 2) {\n          polyfillPlugins.push(\n            [legacyBabelPolyfillPlugin, { regenerator }],\n            [pluginCoreJS2, pluginOptions],\n          );\n        } else {\n          polyfillPlugins.push(\n            [pluginCoreJS3, pluginOptions],\n            [legacyBabelPolyfillPlugin, { deprecated: true }],\n          );\n          if (!regenerator) {\n            polyfillPlugins.push([removeRegeneratorEntryPlugin, pluginOptions]);\n          }\n        }\n      }\n    }\n  }\n  return polyfillPlugins;\n};\n\nfunction getLocalTargets(\n  optionsTargets: Options[\"targets\"],\n  ignoreBrowserslistConfig: boolean,\n  configPath: string,\n  browserslistEnv: string,\n) {\n  if (optionsTargets?.esmodules && optionsTargets.browsers) {\n    console.warn(`\n@babel/preset-env: esmodules and browsers targets have been specified together.\n\\`browsers\\` target, \\`${optionsTargets.browsers.toString()}\\` will be ignored.\n`);\n  }\n\n  return getTargets(optionsTargets as InputTargets, {\n    ignoreBrowserslistConfig,\n    configPath,\n    browserslistEnv,\n  });\n}\n\nfunction supportsStaticESM(caller: CallerMetadata | undefined) {\n  // @ts-expect-error supportsStaticESM is not defined in CallerMetadata\n  return !!caller?.supportsStaticESM;\n}\n\nfunction supportsDynamicImport(caller: CallerMetadata | undefined) {\n  // @ts-expect-error supportsDynamicImport is not defined in CallerMetadata\n  return !!caller?.supportsDynamicImport;\n}\n\nfunction supportsExportNamespaceFrom(caller: CallerMetadata | undefined) {\n  // @ts-expect-error supportsExportNamespaceFrom is not defined in CallerMetadata\n  return !!caller?.supportsExportNamespaceFrom;\n}\n\nexport default declarePreset((api, opts: Options) => {\n  api.assertVersion(7);\n\n  const babelTargets = api.targets();\n\n  const {\n    bugfixes,\n    configPath,\n    debug,\n    exclude: optionsExclude,\n    forceAllTransforms,\n    ignoreBrowserslistConfig,\n    include: optionsInclude,\n    loose,\n    modules,\n    shippedProposals,\n    spec,\n    targets: optionsTargets,\n    useBuiltIns,\n    corejs: { version: corejs, proposals },\n    browserslistEnv,\n  } = normalizeOptions(opts);\n\n  let targets = babelTargets;\n\n  if (\n    // @babel/core < 7.13.0 doesn't load targets (api.targets() always\n    // returns {} thanks to @babel/helper-plugin-utils), so we always want\n    // to fallback to the old targets behavior in this case.\n    semver.lt(api.version, \"7.13.0\") ||\n    // If any browserslist-related option is specified, fallback to the old\n    // behavior of not using the targets specified in the top-level options.\n    opts.targets ||\n    opts.configPath ||\n    opts.browserslistEnv ||\n    opts.ignoreBrowserslistConfig\n  ) {\n    if (!process.env.BABEL_8_BREAKING) {\n      // eslint-disable-next-line no-var\n      var hasUglifyTarget = false;\n\n      if (optionsTargets?.uglify) {\n        hasUglifyTarget = true;\n        delete optionsTargets.uglify;\n\n        console.warn(`\nThe uglify target has been deprecated. Set the top level\noption \\`forceAllTransforms: true\\` instead.\n`);\n      }\n    }\n\n    targets = getLocalTargets(\n      optionsTargets,\n      ignoreBrowserslistConfig,\n      configPath,\n      browserslistEnv,\n    );\n  }\n\n  const transformTargets = (\n    process.env.BABEL_8_BREAKING\n      ? forceAllTransforms\n      : forceAllTransforms || hasUglifyTarget\n  )\n    ? ({} as Targets)\n    : targets;\n\n  const include = transformIncludesAndExcludes(optionsInclude);\n  const exclude = transformIncludesAndExcludes(optionsExclude);\n\n  const compatData = getPluginList(shippedProposals, bugfixes);\n  const shouldSkipExportNamespaceFrom =\n    (modules === \"auto\" && api.caller?.(supportsExportNamespaceFrom)) ||\n    (modules === false &&\n      !isRequired(\"transform-export-namespace-from\", transformTargets, {\n        compatData,\n        includes: include.plugins,\n        excludes: exclude.plugins,\n      }));\n  const modulesPluginNames = getModulesPluginNames({\n    modules,\n    transformations: moduleTransformations,\n    // TODO: Remove the 'api.caller' check eventually. Just here to prevent\n    // unnecessary breakage in the short term for users on older betas/RCs.\n    shouldTransformESM: modules !== \"auto\" || !api.caller?.(supportsStaticESM),\n    shouldTransformDynamicImport:\n      modules !== \"auto\" || !api.caller?.(supportsDynamicImport),\n    shouldTransformExportNamespaceFrom: !shouldSkipExportNamespaceFrom,\n  });\n\n  const pluginNames = filterItems(\n    compatData,\n    include.plugins,\n    exclude.plugins,\n    transformTargets,\n    modulesPluginNames,\n    getOptionSpecificExcludesFor({ loose }),\n    pluginSyntaxMap,\n  );\n  if (shippedProposals) {\n    addProposalSyntaxPlugins(pluginNames, proposalSyntaxPlugins);\n  }\n  removeUnsupportedItems(pluginNames, api.version);\n  removeUnnecessaryItems(pluginNames, overlappingPlugins);\n\n  const polyfillPlugins = getPolyfillPlugins({\n    useBuiltIns,\n    corejs,\n    polyfillTargets: targets,\n    include: include.builtIns,\n    exclude: exclude.builtIns,\n    proposals,\n    shippedProposals,\n    regenerator: pluginNames.has(\"transform-regenerator\"),\n    debug,\n  });\n\n  const pluginUseBuiltIns = useBuiltIns !== false;\n  const plugins = Array.from(pluginNames)\n    .map(pluginName => {\n      if (\n        pluginName === \"transform-class-properties\" ||\n        pluginName === \"transform-private-methods\" ||\n        pluginName === \"transform-private-property-in-object\"\n      ) {\n        return [\n          getPlugin(pluginName),\n          {\n            loose: loose\n              ? \"#__internal__@babel/preset-env__prefer-true-but-false-is-ok-if-it-prevents-an-error\"\n              : \"#__internal__@babel/preset-env__prefer-false-but-true-is-ok-if-it-prevents-an-error\",\n          },\n        ];\n      }\n      if (pluginName === \"syntax-import-attributes\") {\n        // For backward compatibility with the import-assertions plugin, we\n        // allow the deprecated `assert` keyword.\n        // TODO(Babel 8): Revisit this.\n        return [getPlugin(pluginName), { deprecatedAssertSyntax: true }];\n      }\n      return [\n        getPlugin(pluginName),\n        { spec, loose, useBuiltIns: pluginUseBuiltIns },\n      ];\n    })\n    .concat(polyfillPlugins);\n\n  if (debug) {\n    console.log(\"@babel/preset-env: `DEBUG` option\");\n    console.log(\"\\nUsing targets:\");\n    console.log(JSON.stringify(prettifyTargets(targets), null, 2));\n    console.log(`\\nUsing modules transform: ${modules.toString()}`);\n    console.log(\"\\nUsing plugins:\");\n    pluginNames.forEach(pluginName => {\n      logPlugin(pluginName, targets, compatData);\n    });\n\n    if (!useBuiltIns) {\n      console.log(\n        \"\\nUsing polyfills: No polyfills were added, since the `useBuiltIns` option was not set.\",\n      );\n    }\n  }\n\n  return { plugins };\n});\n"], "mappings": ";;;;;;;;AAAA,IAAAA,OAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAD,OAAA;AACA,IAAAE,0BAAA,GAAAF,OAAA;AACA,IAAAG,YAAA,GAAAH,OAAA;AAKA,IAAAI,sBAAA,GAAAJ,OAAA;AACA,IAAAK,iBAAA,GAAAL,OAAA;AACA,IAAAM,iBAAA,GAAAN,OAAA;AAKA,IAAAO,kBAAA,GAAAP,OAAA;AAMA,IAAAQ,YAAA,GAAAR,OAAA;AACA,IAAAS,cAAA,GAAAT,OAAA;AAIA,IAAAU,0BAAA,GAAAV,OAAA;AACA,IAAAW,2BAAA,GAAAX,OAAA;AACA,IAAAY,+BAAA,GAAAZ,OAAA;AAKA,IAAAa,yBAAA,GAAAb,OAAA;AAMA,IAAAc,iBAAA,GAAAd,OAAA;AACA,IAAAe,kBAAA,GAAAf,OAAA;AAXA,MAAMgB,aAAa,GAAGC,0BAAc,CAACC,OAAO,IAAID,0BAAc;AAC9D,MAAME,aAAa,GAAGC,2BAAc,CAACF,OAAO,IAAIE,2BAAc;AAC9D,MAAMC,iBAAiB,GAAGC,+BAAkB,CAACJ,OAAO,IAAII,+BAAkB;AAgBnE,SAASC,gBAAgBA,CAACC,OAAgB,EAAEC,OAAgB,EAAE;EACnE,OAAO,IAAAC,oCAAU,EAAC,WAAW,EAAEF,OAAO,EAAE;IACtCG,UAAU,EAAE;MAAE,WAAW,EAAEF;IAAQ;EACrC,CAAC,CAAC;AACJ;AAEA,SAASG,mBAAmBA,CAC1BC,IAAoC,EACpCC,SAAsB,EACtB;EACA,OAAOC,MAAM,CAACC,IAAI,CAACH,IAAI,CAAC,CAACI,MAAM,CAAC,CAACC,MAAM,EAAEC,IAAI,KAAK;IAChD,IAAI,CAACL,SAAS,CAACM,GAAG,CAACD,IAAI,CAAC,EAAE;MAExBD,MAAM,CAACC,IAAI,CAAC,GAAGN,IAAI,CAACM,IAAI,CAAC;IAC3B;IAEA,OAAOD,MAAM;EACf,CAAC,EAAE,CAAC,CAAC,CAAC;AACR;AAEA,MAAMG,WAAW,GAAG;EAClBC,aAAa,EAAE;IACbC,eAAe,EAAEC,0BAAW;IAC5BC,YAAY,EAAEV,MAAM,CAACW,MAAM,CAAC,CAAC,CAAC,EAAEF,0BAAW,EAAEG,kCAAmB;EAClE,CAAC;EACDC,gBAAgB,EAAE;IAChBL,eAAe,EAAEX,mBAAmB,CAACY,0BAAW,EAAEK,iCAAe,CAAC;IAClEJ,YAAY,EAAEb,mBAAmB,CAC/BG,MAAM,CAACW,MAAM,CAAC,CAAC,CAAC,EAAEF,0BAAW,EAAEG,kCAAmB,CAAC,EACnDE,iCACF;EACF;AACF,CAAC;AAED,SAASC,aAAaA,CAACC,SAAkB,EAAEC,QAAiB,EAAE;EAC5D,IAAID,SAAS,EAAE;IACb,IAAIC,QAAQ,EAAE,OAAOX,WAAW,CAACC,aAAa,CAACG,YAAY,CAAC,KACvD,OAAOJ,WAAW,CAACC,aAAa,CAACC,eAAe;EACvD,CAAC,MAAM;IACL,IAAIS,QAAQ,EAAE,OAAOX,WAAW,CAACO,gBAAgB,CAACH,YAAY,CAAC,KAC1D,OAAOJ,WAAW,CAACO,gBAAgB,CAACL,eAAe;EAC1D;AACF;AAEA,MAAMU,SAAS,GAAIC,UAAkB,IAAK;EACxC,MAAMC,MAAM,GAEVC,yBAAgB,CAACF,UAAU,CAAC,CAAC,CAAC;EAEhC,IAAI,CAACC,MAAM,EAAE;IACX,MAAM,IAAIE,KAAK,CACZ,0BAAyBH,UAAW,+DACvC,CAAC;EACH;EAEA,OAAOC,MAAM;AACf,CAAC;AAEM,MAAMG,4BAA4B,GAAIC,IAAmB,IAAU;EACxE,OAAOA,IAAI,CAACtB,MAAM,CAChB,CAACC,MAAM,EAAEsB,GAAG,KAAK;IACf,MAAMC,MAAM,GAAGD,GAAG,CAACE,KAAK,CAAC,4BAA4B,CAAC,GAClD,UAAU,GACV,SAAS;IACbxB,MAAM,CAACuB,MAAM,CAAC,CAACE,GAAG,CAACH,GAAG,CAAC;IACvB,OAAOtB,MAAM;EACf,CAAC,EACD;IACE0B,GAAG,EAAEL,IAAI;IACTM,OAAO,EAAE,IAAIC,GAAG,CAAC,CAAC;IAClBC,QAAQ,EAAE,IAAID,GAAG,CAAC;EACpB,CACF,CAAC;AACH,CAAC;AAACE,OAAA,CAAAV,4BAAA,GAAAA,4BAAA;AAEK,MAAMW,qBAAqB,GAAGA,CAAC;EACpCC,OAAO;EACPC,eAAe;EACfC,kBAAkB;EAClBC,4BAA4B;EAC5BC;AAOF,CAAC,KAAK;EACJ,MAAMC,kBAAkB,GAAG,EAAE;EAC7B,IAAIL,OAAO,KAAK,KAAK,IAAIC,eAAe,CAACD,OAAO,CAAC,EAAE;IACjD,IAAIE,kBAAkB,EAAE;MACtBG,kBAAkB,CAACC,IAAI,CAACL,eAAe,CAACD,OAAO,CAAC,CAAC;IACnD;IAEA,IAAIG,4BAA4B,EAAE;MAChC,IAAID,kBAAkB,IAAIF,OAAO,KAAK,KAAK,EAAE;QAC3CK,kBAAkB,CAACC,IAAI,CAAC,0BAA0B,CAAC;MACrD,CAAC,MAAM;QACLC,OAAO,CAACC,IAAI,CACV,6DAA6D,GAC3D,wCACJ,CAAC;MACH;IACF;EACF;EAEA,IAAIJ,kCAAkC,EAAE;IACtCC,kBAAkB,CAACC,IAAI,CAAC,iCAAiC,CAAC;EAC5D;EAEmC;IAEjC,IAAI,CAACH,4BAA4B,EAAE;MACjCE,kBAAkB,CAACC,IAAI,CAAC,uBAAuB,CAAC;IAClD;IACA,IAAI,CAACF,kCAAkC,EAAE;MACvCC,kBAAkB,CAACC,IAAI,CAAC,8BAA8B,CAAC;IACzD;IACAD,kBAAkB,CAACC,IAAI,CAAC,wBAAwB,CAAC;IACjDD,kBAAkB,CAACC,IAAI,CAAC,oBAAoB,CAAC;EAC/C;EAEA,OAAOD,kBAAkB;AAC3B,CAAC;AAACP,OAAA,CAAAC,qBAAA,GAAAA,qBAAA;AAEK,MAAMU,kBAAkB,GAAGA,CAAC;EACjCC,WAAW;EACXC,MAAM;EACNC,eAAe;EACfC,OAAO;EACPC,OAAO;EACPjC,SAAS;EACTkC,gBAAgB;EAChBC,WAAW;EACXC;AAWF,CAAC,KAAK;EACJ,MAAMC,eAAe,GAAG,EAAE;EAC1B,IAAIR,WAAW,KAAK,OAAO,IAAIA,WAAW,KAAK,OAAO,EAAE;IACtD,MAAMS,aAAa,GAAG;MACpBC,MAAM,EAAG,GAAEV,WAAY,SAAQ;MAC/BW,OAAO,EAAEV,MAAM,GAAGA,MAAM,CAACW,QAAQ,CAAC,CAAC,GAAGC,SAAS;MAC/CjE,OAAO,EAAEsD,eAAe;MACxBC,OAAO;MACPC,OAAO;MACPjC,SAAS;MACTkC,gBAAgB;MAChBE,KAAK;MACL,iDAAiD,EAAE;QACjDO,aAAa,EAAE;MACjB;IACF,CAAC;IAED,IAAIb,MAAM,EAAE;MACV,IAAID,WAAW,KAAK,OAAO,EAAE;QAC3B,IAAIC,MAAM,CAACc,KAAK,KAAK,CAAC,EAAE;UACtBP,eAAe,CAACZ,IAAI,CAClB,CAACxD,aAAa,EAAEqE,aAAa,CAAC,EAC9B,CAACO,sBAAyB,EAAE;YAAEC,KAAK,EAAE;UAAK,CAAC,CAC7C,CAAC;QACH,CAAC,MAAM;UACLT,eAAe,CAACZ,IAAI,CAClB,CAACrD,aAAa,EAAEkE,aAAa,CAAC,EAC9B,CAACO,sBAAyB,EAAE;YAAEC,KAAK,EAAE,IAAI;YAAEC,UAAU,EAAE;UAAK,CAAC,CAC/D,CAAC;QACH;QACA,IAAIZ,WAAW,EAAE;UACfE,eAAe,CAACZ,IAAI,CAAC,CACnBnD,iBAAiB,EACjB;YAAEiE,MAAM,EAAE,cAAc;YAAEH;UAAM,CAAC,CAClC,CAAC;QACJ;MACF,CAAC,MAAM;QACL,IAAIN,MAAM,CAACc,KAAK,KAAK,CAAC,EAAE;UACtBP,eAAe,CAACZ,IAAI,CAClB,CAACoB,sBAAyB,EAAE;YAAEV;UAAY,CAAC,CAAC,EAC5C,CAAClE,aAAa,EAAEqE,aAAa,CAC/B,CAAC;QACH,CAAC,MAAM;UACLD,eAAe,CAACZ,IAAI,CAClB,CAACrD,aAAa,EAAEkE,aAAa,CAAC,EAC9B,CAACO,sBAAyB,EAAE;YAAEE,UAAU,EAAE;UAAK,CAAC,CAClD,CAAC;UACD,IAAI,CAACZ,WAAW,EAAE;YAChBE,eAAe,CAACZ,IAAI,CAAC,CAACuB,oBAA4B,EAAEV,aAAa,CAAC,CAAC;UACrE;QACF;MACF;IACF;EACF;EACA,OAAOD,eAAe;AACxB,CAAC;AAACpB,OAAA,CAAAW,kBAAA,GAAAA,kBAAA;AAEF,SAASqB,eAAeA,CACtBC,cAAkC,EAClCC,wBAAiC,EACjCC,UAAkB,EAClBC,eAAuB,EACvB;EACA,IAAIH,cAAc,YAAdA,cAAc,CAAEI,SAAS,IAAIJ,cAAc,CAACK,QAAQ,EAAE;IACxD7B,OAAO,CAACC,IAAI,CAAE;AAClB;AACA,yBAAyBuB,cAAc,CAACK,QAAQ,CAACd,QAAQ,CAAC,CAAE;AAC5D,CAAC,CAAC;EACA;EAEA,OAAO,IAAAe,iCAAU,EAACN,cAAc,EAAkB;IAChDC,wBAAwB;IACxBC,UAAU;IACVC;EACF,CAAC,CAAC;AACJ;AAEA,SAASI,iBAAiBA,CAACC,MAAkC,EAAE;EAE7D,OAAO,CAAC,EAACA,MAAM,YAANA,MAAM,CAAED,iBAAiB;AACpC;AAEA,SAASE,qBAAqBA,CAACD,MAAkC,EAAE;EAEjE,OAAO,CAAC,EAACA,MAAM,YAANA,MAAM,CAAEC,qBAAqB;AACxC;AAEA,SAASC,2BAA2BA,CAACF,MAAkC,EAAE;EAEvE,OAAO,CAAC,EAACA,MAAM,YAANA,MAAM,CAAEE,2BAA2B;AAC9C;AAAC,IAAAC,QAAA,GAEc,IAAAC,gCAAa,EAAC,CAACC,GAAG,EAAEvD,IAAa,KAAK;EACnDuD,GAAG,CAACC,aAAa,CAAC,CAAC,CAAC;EAEpB,MAAMC,YAAY,GAAGF,GAAG,CAACtF,OAAO,CAAC,CAAC;EAElC,MAAM;IACJwB,QAAQ;IACRmD,UAAU;IACVhB,KAAK;IACLH,OAAO,EAAEiC,cAAc;IACvBC,kBAAkB;IAClBhB,wBAAwB;IACxBnB,OAAO,EAAEoC,cAAc;IACvBC,KAAK;IACLlD,OAAO;IACPe,gBAAgB;IAChBoC,IAAI;IACJ7F,OAAO,EAAEyE,cAAc;IACvBrB,WAAW;IACXC,MAAM,EAAE;MAAEU,OAAO,EAAEV,MAAM;MAAE9B;IAAU,CAAC;IACtCqD;EACF,CAAC,GAAG,IAAAkB,yBAAgB,EAAC/D,IAAI,CAAC;EAE1B,IAAI/B,OAAO,GAAGwF,YAAY;EAE1B,IAIEO,OAAM,CAACC,EAAE,CAACV,GAAG,CAACvB,OAAO,EAAE,QAAQ,CAAC,IAGhChC,IAAI,CAAC/B,OAAO,IACZ+B,IAAI,CAAC4C,UAAU,IACf5C,IAAI,CAAC6C,eAAe,IACpB7C,IAAI,CAAC2C,wBAAwB,EAC7B;IACmC;MAEjC,IAAIuB,eAAe,GAAG,KAAK;MAE3B,IAAIxB,cAAc,YAAdA,cAAc,CAAEyB,MAAM,EAAE;QAC1BD,eAAe,GAAG,IAAI;QACtB,OAAOxB,cAAc,CAACyB,MAAM;QAE5BjD,OAAO,CAACC,IAAI,CAAE;AACtB;AACA;AACA,CAAC,CAAC;MACI;IACF;IAEAlD,OAAO,GAAGwE,eAAe,CACvBC,cAAc,EACdC,wBAAwB,EACxBC,UAAU,EACVC,eACF,CAAC;EACH;EAEA,MAAMuB,gBAAgB,GAGhBT,kBAAkB,IAAIO,eAAe,GAEtC,CAAC,CAAC,GACHjG,OAAO;EAEX,MAAMuD,OAAO,GAAGzB,4BAA4B,CAAC6D,cAAc,CAAC;EAC5D,MAAMnC,OAAO,GAAG1B,4BAA4B,CAAC2D,cAAc,CAAC;EAE5D,MAAMtF,UAAU,GAAGmB,aAAa,CAACmC,gBAAgB,EAAEjC,QAAQ,CAAC;EAC5D,MAAM4E,6BAA6B,GAChC1D,OAAO,KAAK,MAAM,KAAI4C,GAAG,CAACL,MAAM,oBAAVK,GAAG,CAACL,MAAM,CAAGE,2BAA2B,CAAC,KAC/DzC,OAAO,KAAK,KAAK,IAChB,CAAC,IAAAxC,oCAAU,EAAC,iCAAiC,EAAEiG,gBAAgB,EAAE;IAC/DhG,UAAU;IACVkG,QAAQ,EAAE9C,OAAO,CAAClB,OAAO;IACzBiE,QAAQ,EAAE9C,OAAO,CAACnB;EACpB,CAAC,CAAE;EACP,MAAMU,kBAAkB,GAAGN,qBAAqB,CAAC;IAC/CC,OAAO;IACPC,eAAe,EAAE4D,8BAAqB;IAGtC3D,kBAAkB,EAAEF,OAAO,KAAK,MAAM,IAAI,EAAC4C,GAAG,CAACL,MAAM,YAAVK,GAAG,CAACL,MAAM,CAAGD,iBAAiB,CAAC;IAC1EnC,4BAA4B,EAC1BH,OAAO,KAAK,MAAM,IAAI,EAAC4C,GAAG,CAACL,MAAM,YAAVK,GAAG,CAACL,MAAM,CAAGC,qBAAqB,CAAC;IAC5DpC,kCAAkC,EAAE,CAACsD;EACvC,CAAC,CAAC;EAEF,MAAMI,WAAW,GAAG,IAAAC,qCAAW,EAC7BtG,UAAU,EACVoD,OAAO,CAAClB,OAAO,EACfmB,OAAO,CAACnB,OAAO,EACf8D,gBAAgB,EAChBpD,kBAAkB,EAClB,IAAA2D,kCAA4B,EAAC;IAAEd;EAAM,CAAC,CAAC,EACvCe,iCACF,CAAC;EACD,IAAIlD,gBAAgB,EAAE;IACpB,IAAAmD,qCAAwB,EAACJ,WAAW,EAAEK,uCAAqB,CAAC;EAC9D;EACA,IAAAC,mCAAsB,EAACN,WAAW,EAAElB,GAAG,CAACvB,OAAO,CAAC;EAChD,IAAAgD,mCAAsB,EAACP,WAAW,EAAEQ,qCAAkB,CAAC;EAEvD,MAAMpD,eAAe,GAAGT,kBAAkB,CAAC;IACzCC,WAAW;IACXC,MAAM;IACNC,eAAe,EAAEtD,OAAO;IACxBuD,OAAO,EAAEA,OAAO,CAAChB,QAAQ;IACzBiB,OAAO,EAAEA,OAAO,CAACjB,QAAQ;IACzBhB,SAAS;IACTkC,gBAAgB;IAChBC,WAAW,EAAE8C,WAAW,CAAC5F,GAAG,CAAC,uBAAuB,CAAC;IACrD+C;EACF,CAAC,CAAC;EAEF,MAAMsD,iBAAiB,GAAG7D,WAAW,KAAK,KAAK;EAC/C,MAAMf,OAAO,GAAG6E,KAAK,CAACC,IAAI,CAACX,WAAW,CAAC,CACpCY,GAAG,CAAC1F,UAAU,IAAI;IACjB,IACEA,UAAU,KAAK,4BAA4B,IAC3CA,UAAU,KAAK,2BAA2B,IAC1CA,UAAU,KAAK,sCAAsC,EACrD;MACA,OAAO,CACLD,SAAS,CAACC,UAAU,CAAC,EACrB;QACEkE,KAAK,EAAEA,KAAK,GACR,qFAAqF,GACrF;MACN,CAAC,CACF;IACH;IACA,IAAIlE,UAAU,KAAK,0BAA0B,EAAE;MAI7C,OAAO,CAACD,SAAS,CAACC,UAAU,CAAC,EAAE;QAAE2F,sBAAsB,EAAE;MAAK,CAAC,CAAC;IAClE;IACA,OAAO,CACL5F,SAAS,CAACC,UAAU,CAAC,EACrB;MAAEmE,IAAI;MAAED,KAAK;MAAExC,WAAW,EAAE6D;IAAkB,CAAC,CAChD;EACH,CAAC,CAAC,CACDK,MAAM,CAAC1D,eAAe,CAAC;EAE1B,IAAID,KAAK,EAAE;IACTV,OAAO,CAACsE,GAAG,CAAC,mCAAmC,CAAC;IAChDtE,OAAO,CAACsE,GAAG,CAAC,kBAAkB,CAAC;IAC/BtE,OAAO,CAACsE,GAAG,CAACC,IAAI,CAACC,SAAS,CAAC,IAAAC,yCAAe,EAAC1H,OAAO,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IAC9DiD,OAAO,CAACsE,GAAG,CAAE,8BAA6B7E,OAAO,CAACsB,QAAQ,CAAC,CAAE,EAAC,CAAC;IAC/Df,OAAO,CAACsE,GAAG,CAAC,kBAAkB,CAAC;IAC/Bf,WAAW,CAACmB,OAAO,CAACjG,UAAU,IAAI;MAChC,IAAAkG,gBAAS,EAAClG,UAAU,EAAE1B,OAAO,EAAEG,UAAU,CAAC;IAC5C,CAAC,CAAC;IAEF,IAAI,CAACiD,WAAW,EAAE;MAChBH,OAAO,CAACsE,GAAG,CACT,yFACF,CAAC;IACH;EACF;EAEA,OAAO;IAAElF;EAAQ,CAAC;AACpB,CAAC,CAAC;AAAAG,OAAA,CAAA9C,OAAA,GAAA0F,QAAA"}