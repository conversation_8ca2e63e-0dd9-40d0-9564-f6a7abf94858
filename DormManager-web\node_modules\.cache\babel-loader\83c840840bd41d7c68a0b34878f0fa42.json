{"remainingRequest": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\admin\\student\\StudentEdit.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\admin\\student\\StudentEdit.vue", "mtime": 1749045631741}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\babel.config.js", "mtime": 1749034452000}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749043011899}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749043012919}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749043011899}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749043013130}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkucHVzaC5qcyI7CmltcG9ydCByZXF1ZXN0LCB7IGJhc2UgfSBmcm9tICIuLi8uLi8uLi8uLi91dGlscy9odHRwIjsKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdTdHVkZW50RWRpdCcsCiAgY29tcG9uZW50czoge30sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGlkOiAnJywKICAgICAgaXNDbGVhcjogZmFsc2UsCiAgICAgIHVwbG9hZFZpc2libGU6IGZhbHNlLAogICAgICBidG5Mb2FkaW5nOiBmYWxzZSwKICAgICAgLy/kv53lrZjmjInpkq7liqDovb3nirbmgIEKICAgICAgZm9ybURhdGE6IHt9LAogICAgICAvL+ihqOWNleaVsOaNrgogICAgICBkb3JtYnVpbGRpbmdMaXN0OiBbXSwKICAgICAgLy/lrr/oiI3mpbzliJfooagKICAgICAgZG9ybWl0b3J5TGlzdDogW10sCiAgICAgIC8v5omA5pyJ5a6/6IiN5YiX6KGoCiAgICAgIGZpbHRlcmVkRG9ybWl0b3J5TGlzdDogW10sCiAgICAgIC8v6L+H5ruk5ZCO55qE5a6/6IiN5YiX6KGoCiAgICAgIGFkZHJ1bGVzOiB7CiAgICAgICAgc25vOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAn6K+36L6T5YWl5a2m5Y+3JywKICAgICAgICAgIHRyaWdnZXI6ICdibHVyJwogICAgICAgIH1dLAogICAgICAgIHBhc3N3b3JkOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAn6K+36L6T5YWl55m75b2V5a+G56CBJywKICAgICAgICAgIHRyaWdnZXI6ICdibHVyJwogICAgICAgIH1dLAogICAgICAgIHN0bmFtZTogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogJ+ivt+i+k+WFpeWnk+WQjScsCiAgICAgICAgICB0cmlnZ2VyOiAnYmx1cicKICAgICAgICB9XSwKICAgICAgICBzZXg6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICfor7fovpPlhaXmgKfliKsnLAogICAgICAgICAgdHJpZ2dlcjogJ2JsdXInCiAgICAgICAgfV0sCiAgICAgICAgcGhvbmU6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICfor7fovpPlhaXmiYvmnLrlj7fnoIEnLAogICAgICAgICAgdHJpZ2dlcjogJ2JsdXInCiAgICAgICAgfSwgewogICAgICAgICAgcGF0dGVybjogL14xWzM0NTY3ODldXGR7OX0kLywKICAgICAgICAgIG1lc3NhZ2U6ICfmiYvmnLrlj7fnoIHmoLzlvI/kuI3mraPnoa4nLAogICAgICAgICAgdHJpZ2dlcjogJ2JsdXInCiAgICAgICAgfV0sCiAgICAgICAgZG9ybzogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogJ+ivt+mAieaLqeWuv+iIjScsCiAgICAgICAgICB0cmlnZ2VyOiAnb25jaGFuZ2UnCiAgICAgICAgfV0sCiAgICAgICAgc3BlY2lhbHR5OiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAn6K+36L6T5YWl5LiT5LiaJywKICAgICAgICAgIHRyaWdnZXI6ICdibHVyJwogICAgICAgIH1dLAogICAgICAgIGNsc25hbWU6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICfor7fovpPlhaXnj63nuqcnLAogICAgICAgICAgdHJpZ2dlcjogJ2JsdXInCiAgICAgICAgfV0KICAgICAgfQogICAgfTsKICB9LAogIGNyZWF0ZWQoKSB7CiAgICB0aGlzLmlkID0gdGhpcy4kcm91dGUucXVlcnkuaWQ7CiAgICB0aGlzLmdldERhdGFzKCk7CiAgICB0aGlzLmdldGRvcm1idWlsZGluZ0xpc3QoKTsKICAgIHRoaXMuZ2V0ZG9ybWl0b3J5TGlzdCgpOwogIH0sCiAgbWV0aG9kczogewogICAgLy/ojrflj5bliJfooajmlbDmja4KICAgIGdldERhdGFzKCkgewogICAgICBsZXQgcGFyYSA9IHt9OwogICAgICB0aGlzLmxpc3RMb2FkaW5nID0gdHJ1ZTsKICAgICAgbGV0IHVybCA9IGJhc2UgKyAiL3N0dWRlbnQvZ2V0P2lkPSIgKyB0aGlzLmlkOwogICAgICByZXF1ZXN0LnBvc3QodXJsLCBwYXJhKS50aGVuKHJlcyA9PiB7CiAgICAgICAgdGhpcy5mb3JtRGF0YSA9IEpTT04ucGFyc2UoSlNPTi5zdHJpbmdpZnkocmVzLnJlc2RhdGEpKTsKICAgICAgICB0aGlzLmxpc3RMb2FkaW5nID0gZmFsc2U7CiAgICAgICAgdGhpcy5kYmlkID0gdGhpcy5mb3JtRGF0YS5kYmlkOwogICAgICAgIHRoaXMuZm9ybURhdGEuZGJpZCA9IHRoaXMuZm9ybURhdGEuZGJuYW1lOwogICAgICAgIHRoaXMuZG9ybyA9IHRoaXMuZm9ybURhdGEuZG9ybzsKICAgICAgICB0aGlzLmZvcm1EYXRhLmRvcm8gPSB0aGlzLmZvcm1EYXRhLmRvcm87CgogICAgICAgIC8vIOWKoOi9veWujOaVsOaNruWQjuabtOaWsOWuv+iIjeWIl+ihqAogICAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsKICAgICAgICAgIHRoaXMudXBkYXRlRG9ybWl0b3J5TGlzdCgpOwogICAgICAgIH0pOwogICAgICB9KTsKICAgIH0sCiAgICAvLyDmt7vliqAKICAgIHNhdmUoKSB7CiAgICAgIHRoaXMuJHJlZnNbImZvcm1EYXRhUmVmIl0udmFsaWRhdGUodmFsaWQgPT4gewogICAgICAgIC8v6aqM6K+B6KGo5Y2VCiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICBsZXQgdXJsID0gYmFzZSArICIvc3R1ZGVudC91cGRhdGUiOwogICAgICAgICAgdGhpcy5idG5Mb2FkaW5nID0gdHJ1ZTsKICAgICAgICAgIHRoaXMuZm9ybURhdGEuZGJpZCA9IHRoaXMuZm9ybURhdGEuZGJpZCA9PSB0aGlzLmZvcm1EYXRhLmRibmFtZSA/IHRoaXMuZGJpZCA6IHRoaXMuZm9ybURhdGEuZGJpZDsKICAgICAgICAgIHRoaXMuZm9ybURhdGEuZG9ybyA9IHRoaXMuZm9ybURhdGEuZG9ybyA9PSB0aGlzLmZvcm1EYXRhLmRvcm8gPyB0aGlzLmRvcm8gOiB0aGlzLmZvcm1EYXRhLmRvcm87CiAgICAgICAgICByZXF1ZXN0LnBvc3QodXJsLCB0aGlzLmZvcm1EYXRhKS50aGVuKHJlcyA9PiB7CiAgICAgICAgICAgIC8v5Y+R6YCB6K+35rGCICAgICAgICAgCiAgICAgICAgICAgIGlmIChyZXMuY29kZSA9PSAyMDApIHsKICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICLmk43kvZzmiJDlip8iLAogICAgICAgICAgICAgICAgdHlwZTogInN1Y2Nlc3MiLAogICAgICAgICAgICAgICAgb2Zmc2V0OiAzMjAKICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICB0aGlzLiRyb3V0ZXIucHVzaCh7CiAgICAgICAgICAgICAgICBwYXRoOiAiL1N0dWRlbnRNYW5hZ2UiCiAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgICBtZXNzYWdlOiByZXMubXNnLAogICAgICAgICAgICAgICAgdHlwZTogImVycm9yIiwKICAgICAgICAgICAgICAgIG9mZnNldDogMzIwCiAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIH0KICAgICAgICAgICAgdGhpcy5idG5Mb2FkaW5nID0gZmFsc2U7CiAgICAgICAgICB9KTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIC8vIOi/lOWbngogICAgZ29CYWNrKCkgewogICAgICB0aGlzLiRyb3V0ZXIucHVzaCh7CiAgICAgICAgcGF0aDogIi9TdHVkZW50TWFuYWdlIgogICAgICB9KTsKICAgIH0sCiAgICBnZXRkb3JtYnVpbGRpbmdMaXN0KCkgewogICAgICBsZXQgcGFyYSA9IHt9OwogICAgICB0aGlzLmxpc3RMb2FkaW5nID0gdHJ1ZTsKICAgICAgbGV0IHVybCA9IGJhc2UgKyAiL2Rvcm1idWlsZGluZy9saXN0P2N1cnJlbnRQYWdlPTEmcGFnZVNpemU9MTAwMCI7CiAgICAgIHJlcXVlc3QucG9zdCh1cmwsIHBhcmEpLnRoZW4ocmVzID0+IHsKICAgICAgICB0aGlzLmRvcm1idWlsZGluZ0xpc3QgPSByZXMucmVzZGF0YTsKICAgICAgfSk7CiAgICB9LAogICAgZ2V0ZG9ybWl0b3J5TGlzdCgpIHsKICAgICAgbGV0IHBhcmEgPSB7fTsKICAgICAgdGhpcy5saXN0TG9hZGluZyA9IHRydWU7CiAgICAgIGxldCB1cmwgPSBiYXNlICsgIi9kb3JtaXRvcnkvbGlzdD9jdXJyZW50UGFnZT0xJnBhZ2VTaXplPTEwMDAiOwogICAgICByZXF1ZXN0LnBvc3QodXJsLCBwYXJhKS50aGVuKHJlcyA9PiB7CiAgICAgICAgdGhpcy5kb3JtaXRvcnlMaXN0ID0gcmVzLnJlc2RhdGE7CiAgICAgIH0pOwogICAgfSwKICAgIC8vIOWuv+iIjealvOWPmOWMluaXtueahOWkhOeQhgogICAgb25CdWlsZGluZ0NoYW5nZSgpIHsKICAgICAgdGhpcy5mb3JtRGF0YS5kb3JvID0gJyc7IC8vIOa4heepuuWuv+iIjemAieaLqQogICAgICB0aGlzLnVwZGF0ZURvcm1pdG9yeUxpc3QoKTsKICAgIH0sCiAgICAvLyDmgKfliKvlj5jljJbml7bnmoTlpITnkIYKICAgIG9uR2VuZGVyQ2hhbmdlKCkgewogICAgICB0aGlzLmZvcm1EYXRhLmRvcm8gPSAnJzsgLy8g5riF56m65a6/6IiN6YCJ5oupCiAgICAgIHRoaXMudXBkYXRlRG9ybWl0b3J5TGlzdCgpOwogICAgfSwKICAgIC8vIOabtOaWsOWuv+iIjeWIl+ihqAogICAgdXBkYXRlRG9ybWl0b3J5TGlzdCgpIHsKICAgICAgaWYgKCF0aGlzLmZvcm1EYXRhLmRiaWQgfHwgIXRoaXMuZm9ybURhdGEuc2V4KSB7CiAgICAgICAgdGhpcy5maWx0ZXJlZERvcm1pdG9yeUxpc3QgPSBbXTsKICAgICAgICByZXR1cm47CiAgICAgIH0KICAgICAgbGV0IHBhcmEgPSB7CiAgICAgICAgZGJpZDogdGhpcy5mb3JtRGF0YS5kYmlkLAogICAgICAgIGRvcmdlbmRlcjogdGhpcy5mb3JtRGF0YS5zZXgKICAgICAgfTsKICAgICAgbGV0IHVybCA9IGJhc2UgKyAiL2Rvcm1pdG9yeS9saXN0P2N1IjsKICAgICAgcmVxdWVzdC5wb3N0KHVybCwgcGFyYSkudGhlbihyZXMgPT4gewogICAgICAgIGlmIChyZXMuY29kZSA9PSAyMDApIHsKICAgICAgICAgIHRoaXMuZmlsdGVyZWREb3JtaXRvcnlMaXN0ID0gcmVzLnJlc2RhdGE7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuZmlsdGVyZWREb3JtaXRvcnlMaXN0ID0gW107CiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgbWVzc2FnZTogcmVzLm1zZyB8fCAi6I635Y+W5a6/6IiN5YiX6KGo5aSx6LSlIiwKICAgICAgICAgICAgdHlwZTogImVycm9yIiwKICAgICAgICAgICAgb2Zmc2V0OiAzMjAKICAgICAgICAgIH0pOwogICAgICAgIH0KICAgICAgfSkuY2F0Y2goKCkgPT4gewogICAgICAgIHRoaXMuZmlsdGVyZWREb3JtaXRvcnlMaXN0ID0gW107CiAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICBtZXNzYWdlOiAi6I635Y+W5a6/6IiN5YiX6KGo5aSx6LSlIiwKICAgICAgICAgIHR5cGU6ICJlcnJvciIsCiAgICAgICAgICBvZmZzZXQ6IDMyMAogICAgICAgIH0pOwogICAgICB9KTsKICAgIH0KICB9Cn07"}, {"version": 3, "names": ["request", "base", "name", "components", "data", "id", "isClear", "uploadVisible", "btnLoading", "formData", "dormbuildingList", "dormitoryList", "filteredDormitoryList", "add<PERSON><PERSON>", "sno", "required", "message", "trigger", "password", "stname", "sex", "phone", "pattern", "doro", "specialty", "clsname", "created", "$route", "query", "getDatas", "getdormbuildingList", "getdormitoryList", "methods", "para", "listLoading", "url", "post", "then", "res", "JSON", "parse", "stringify", "resdata", "dbid", "dbname", "$nextTick", "updateDormitoryList", "save", "$refs", "validate", "valid", "code", "$message", "type", "offset", "$router", "push", "path", "msg", "goBack", "onBuildingChange", "onGenderChange", "<PERSON><PERSON><PERSON>", "catch"], "sources": ["I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\admin\\student\\StudentEdit.vue"], "sourcesContent": ["<template>\r\n    <div style=\"width: 100%;line-height: 30px;text-align: left;\">\r\n       <el-form :model=\"formData\" label-width=\"20%\" ref=\"formDataRef\" :rules=\"addrules\"  align=\"left\">\r\n<el-form-item label=\"学号\" prop=\"sno\">\r\n<el-input v-model=\"formData.sno\" placeholder=\"学号\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item label=\"登录密码\" prop=\"password\">\r\n<el-input v-model=\"formData.password\" placeholder=\"登录密码\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item label=\"姓名\" prop=\"stname\">\r\n<el-input v-model=\"formData.stname\" placeholder=\"姓名\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item label=\"性别\" prop=\"sex\">\r\n<el-radio-group v-model=\"formData.sex\" @change=\"onGenderChange\">\r\n<el-radio label=\"男\">\r\n男\r\n</el-radio>\r\n<el-radio label=\"女\">\r\n女\r\n</el-radio>\r\n</el-radio-group>\r\n</el-form-item>\r\n<el-form-item label=\"手机号码\" prop=\"phone\">\r\n<el-input v-model=\"formData.phone\" placeholder=\"手机号码\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item label=\"宿舍楼\" prop=\"dbid\">\r\n<el-select v-model=\"formData.dbid\" placeholder=\"请选择\"  size=\"small\" @change=\"onBuildingChange\">\r\n<el-option v-for=\"item in dormbuildingList\" :key=\"item.dbid\" :label=\"item.dbname\" :value=\"item.dbid\"></el-option>\r\n</el-select>\r\n</el-form-item>\r\n<el-form-item label=\"宿舍\" prop=\"doro\">\r\n<el-select v-model=\"formData.doro\" placeholder=\"请先选择宿舍楼和性别\"  size=\"small\" :disabled=\"!formData.dbid || !formData.sex\">\r\n<el-option v-for=\"item in filteredDormitoryList\" :key=\"item.doro\" :label=\"item.doro\" :value=\"item.doro\"></el-option>\r\n</el-select>\r\n</el-form-item>\r\n<el-form-item label=\"专业\" prop=\"specialty\">\r\n<el-input v-model=\"formData.specialty\" placeholder=\"专业\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item label=\"班级\" prop=\"clsname\">\r\n<el-input v-model=\"formData.clsname\" placeholder=\"班级\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item>\r\n<el-button type=\"primary\" size=\"small\" @click=\"save\" :loading=\"btnLoading\" icon=\"el-icon-upload\">提 交</el-button>\r\n<el-button type=\"info\" size=\"small\" @click=\"goBack\" icon=\"el-icon-back\">返 回</el-button>\r\n</el-form-item>\r\n</el-form>\r\n\r\n\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport request, { base } from \"../../../../utils/http\";\r\n\r\nexport default {\r\n  name: 'StudentEdit',\r\n  components: {\r\n    \r\n  },  \r\n    data() {\r\n      return {\r\n        id: '',\r\n        isClear: false,\r\n        uploadVisible: false,\r\n        btnLoading: false, //保存按钮加载状态\r\n        formData: {}, //表单数据\r\n        dormbuildingList: [], //宿舍楼列表\r\n        dormitoryList: [], //所有宿舍列表\r\n        filteredDormitoryList: [], //过滤后的宿舍列表\r\n        addrules: {\r\n          sno: [{ required: true, message: '请输入学号', trigger: 'blur' },\r\n],          password: [{ required: true, message: '请输入登录密码', trigger: 'blur' },\r\n],          stname: [{ required: true, message: '请输入姓名', trigger: 'blur' },\r\n],          sex: [{ required: true, message: '请输入性别', trigger: 'blur' },\r\n],          phone: [{ required: true, message: '请输入手机号码', trigger: 'blur' },\r\n        { pattern: /^1[3456789]\\d{9}$/, message: '手机号码格式不正确', trigger: 'blur' },\r\n],          doro: [{ required: true, message: '请选择宿舍', trigger: 'onchange' }],\r\n          specialty: [{ required: true, message: '请输入专业', trigger: 'blur' },\r\n],          clsname: [{ required: true, message: '请输入班级', trigger: 'blur' },\r\n],        },\r\n\r\n      };\r\n    },\r\n    created() {\r\n    this.id = this.$route.query.id;\r\n      this.getDatas();\r\n      this.getdormbuildingList();\r\n      this.getdormitoryList();\r\n    },\r\n\r\n \r\n    methods: {    \r\n\r\n//获取列表数据\r\n        getDatas() {\r\n          let para = {\r\n          };\r\n          this.listLoading = true;\r\n          let url = base + \"/student/get?id=\" + this.id;\r\n          request.post(url, para).then((res) => {\r\n            this.formData = JSON.parse(JSON.stringify(res.resdata));\r\n            this.listLoading = false;\r\n\r\n            this.dbid = this.formData.dbid;\r\n            this.formData.dbid = this.formData.dbname;\r\n            this.doro = this.formData.doro;\r\n            this.formData.doro = this.formData.doro;\r\n\r\n            // 加载完数据后更新宿舍列表\r\n            this.$nextTick(() => {\r\n              this.updateDormitoryList();\r\n            });\r\n          });\r\n        },\r\n    \r\n        // 添加\r\n        save() {\r\n          this.$refs[\"formDataRef\"].validate((valid) => { //验证表单\r\n            if (valid) {\r\n              let url = base + \"/student/update\";\r\n              this.btnLoading = true;\r\n                        this.formData.dbid = this.formData.dbid==this.formData.dbname?this.dbid:this.formData.dbid;\r\n          this.formData.doro = this.formData.doro==this.formData.doro?this.doro:this.formData.doro;\r\n\r\n              request.post(url, this.formData).then((res) => { //发送请求         \r\n                if (res.code == 200) {\r\n                  this.$message({\r\n                    message: \"操作成功\",\r\n                    type: \"success\",\r\n                    offset: 320,\r\n                  });\r\n                  this.$router.push({\r\n                    path: \"/StudentManage\",\r\n                  });\r\n                } else {\r\n                  this.$message({\r\n                    message:res.msg,\r\n                    type: \"error\",\r\n                    offset: 320,\r\n                  });\r\n                }\r\n                this.btnLoading = false;\r\n              });\r\n            }\r\n    \r\n          });\r\n        },\r\n        \r\n       // 返回\r\n        goBack() {\r\n          this.$router.push({\r\n            path: \"/StudentManage\",\r\n          });\r\n        },       \r\n              \r\n            \r\n    getdormbuildingList() {\r\n      let para = {};\r\n      this.listLoading = true;\r\n      let url = base + \"/dormbuilding/list?currentPage=1&pageSize=1000\";\r\n      request.post(url, para).then((res) => {\r\n        this.dormbuildingList = res.resdata;\r\n      });\r\n    },\r\n    \r\n    getdormitoryList() {\r\n      let para = {};\r\n      this.listLoading = true;\r\n      let url = base + \"/dormitory/list?currentPage=1&pageSize=1000\";\r\n      request.post(url, para).then((res) => {\r\n        this.dormitoryList = res.resdata;\r\n      });\r\n    },\r\n\r\n    // 宿舍楼变化时的处理\r\n    onBuildingChange() {\r\n      this.formData.doro = ''; // 清空宿舍选择\r\n      this.updateDormitoryList();\r\n    },\r\n\r\n    // 性别变化时的处理\r\n    onGenderChange() {\r\n      this.formData.doro = ''; // 清空宿舍选择\r\n      this.updateDormitoryList();\r\n    },\r\n\r\n    // 更新宿舍列表\r\n    updateDormitoryList() {\r\n      if (!this.formData.dbid || !this.formData.sex) {\r\n        this.filteredDormitoryList = [];\r\n        return;\r\n      }\r\n\r\n      let para = {\r\n        dbid: this.formData.dbid,\r\n        dorgender: this.formData.sex\r\n      };\r\n      let url = base + \"/dormitory/list?cu\";\r\n      request.post(url, para).then((res) => {\r\n        if (res.code == 200) {\r\n          this.filteredDormitoryList = res.resdata;\r\n        } else {\r\n          this.filteredDormitoryList = [];\r\n          this.$message({\r\n            message: res.msg || \"获取宿舍列表失败\",\r\n            type: \"error\",\r\n            offset: 320,\r\n          });\r\n        }\r\n      }).catch(() => {\r\n        this.filteredDormitoryList = [];\r\n        this.$message({\r\n          message: \"获取宿舍列表失败\",\r\n          type: \"error\",\r\n          offset: 320,\r\n        });\r\n      });\r\n    },\r\n  \r\n           \r\n           \r\n      },\r\n}\r\n\r\n</script>\r\n<style scoped>\r\n</style>\r\n \r\n\r\n"], "mappings": ";AAoDA,OAAOA,OAAO,IAAIC,IAAG,QAAS,wBAAwB;AAEtD,eAAe;EACbC,IAAI,EAAE,aAAa;EACnBC,UAAU,EAAE,CAEZ,CAAC;EACCC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,EAAE,EAAE,EAAE;MACNC,OAAO,EAAE,KAAK;MACdC,aAAa,EAAE,KAAK;MACpBC,UAAU,EAAE,KAAK;MAAE;MACnBC,QAAQ,EAAE,CAAC,CAAC;MAAE;MACdC,gBAAgB,EAAE,EAAE;MAAE;MACtBC,aAAa,EAAE,EAAE;MAAE;MACnBC,qBAAqB,EAAE,EAAE;MAAE;MAC3BC,QAAQ,EAAE;QACRC,GAAG,EAAE,CAAC;UAAEC,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,OAAO;UAAEC,OAAO,EAAE;QAAO,CAAC,CACpE;QAAWC,QAAQ,EAAE,CAAC;UAAEH,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAO,CAAC,CAC7E;QAAWE,MAAM,EAAE,CAAC;UAAEJ,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,OAAO;UAAEC,OAAO,EAAE;QAAO,CAAC,CACzE;QAAWG,GAAG,EAAE,CAAC;UAAEL,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,OAAO;UAAEC,OAAO,EAAE;QAAO,CAAC,CACtE;QAAWI,KAAK,EAAE,CAAC;UAAEN,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAO,CAAC,EACnE;UAAEK,OAAO,EAAE,mBAAmB;UAAEN,OAAO,EAAE,WAAW;UAAEC,OAAO,EAAE;QAAO,CAAC,CAC9E;QAAWM,IAAI,EAAE,CAAC;UAAER,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,OAAO;UAAEC,OAAO,EAAE;QAAW,CAAC,CAAC;QACnEO,SAAS,EAAE,CAAC;UAAET,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,OAAO;UAAEC,OAAO,EAAE;QAAO,CAAC,CAC1E;QAAWQ,OAAO,EAAE,CAAC;UAAEV,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,OAAO;UAAEC,OAAO,EAAE;QAAO,CAAC;MACjE;IAEJ,CAAC;EACH,CAAC;EACDS,OAAOA,CAAA,EAAG;IACV,IAAI,CAACrB,EAAC,GAAI,IAAI,CAACsB,MAAM,CAACC,KAAK,CAACvB,EAAE;IAC5B,IAAI,CAACwB,QAAQ,CAAC,CAAC;IACf,IAAI,CAACC,mBAAmB,CAAC,CAAC;IAC1B,IAAI,CAACC,gBAAgB,CAAC,CAAC;EACzB,CAAC;EAGDC,OAAO,EAAE;IAEb;IACQH,QAAQA,CAAA,EAAG;MACT,IAAII,IAAG,GAAI,CACX,CAAC;MACD,IAAI,CAACC,WAAU,GAAI,IAAI;MACvB,IAAIC,GAAE,GAAIlC,IAAG,GAAI,kBAAiB,GAAI,IAAI,CAACI,EAAE;MAC7CL,OAAO,CAACoC,IAAI,CAACD,GAAG,EAAEF,IAAI,CAAC,CAACI,IAAI,CAAEC,GAAG,IAAK;QACpC,IAAI,CAAC7B,QAAO,GAAI8B,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAACH,GAAG,CAACI,OAAO,CAAC,CAAC;QACvD,IAAI,CAACR,WAAU,GAAI,KAAK;QAExB,IAAI,CAACS,IAAG,GAAI,IAAI,CAAClC,QAAQ,CAACkC,IAAI;QAC9B,IAAI,CAAClC,QAAQ,CAACkC,IAAG,GAAI,IAAI,CAAClC,QAAQ,CAACmC,MAAM;QACzC,IAAI,CAACrB,IAAG,GAAI,IAAI,CAACd,QAAQ,CAACc,IAAI;QAC9B,IAAI,CAACd,QAAQ,CAACc,IAAG,GAAI,IAAI,CAACd,QAAQ,CAACc,IAAI;;QAEvC;QACA,IAAI,CAACsB,SAAS,CAAC,MAAM;UACnB,IAAI,CAACC,mBAAmB,CAAC,CAAC;QAC5B,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC;IAED;IACAC,IAAIA,CAAA,EAAG;MACL,IAAI,CAACC,KAAK,CAAC,aAAa,CAAC,CAACC,QAAQ,CAAEC,KAAK,IAAK;QAAE;QAC9C,IAAIA,KAAK,EAAE;UACT,IAAIf,GAAE,GAAIlC,IAAG,GAAI,iBAAiB;UAClC,IAAI,CAACO,UAAS,GAAI,IAAI;UACZ,IAAI,CAACC,QAAQ,CAACkC,IAAG,GAAI,IAAI,CAAClC,QAAQ,CAACkC,IAAI,IAAE,IAAI,CAAClC,QAAQ,CAACmC,MAAM,GAAC,IAAI,CAACD,IAAI,GAAC,IAAI,CAAClC,QAAQ,CAACkC,IAAI;UACxG,IAAI,CAAClC,QAAQ,CAACc,IAAG,GAAI,IAAI,CAACd,QAAQ,CAACc,IAAI,IAAE,IAAI,CAACd,QAAQ,CAACc,IAAI,GAAC,IAAI,CAACA,IAAI,GAAC,IAAI,CAACd,QAAQ,CAACc,IAAI;UAEpFvB,OAAO,CAACoC,IAAI,CAACD,GAAG,EAAE,IAAI,CAAC1B,QAAQ,CAAC,CAAC4B,IAAI,CAAEC,GAAG,IAAK;YAAE;YAC/C,IAAIA,GAAG,CAACa,IAAG,IAAK,GAAG,EAAE;cACnB,IAAI,CAACC,QAAQ,CAAC;gBACZpC,OAAO,EAAE,MAAM;gBACfqC,IAAI,EAAE,SAAS;gBACfC,MAAM,EAAE;cACV,CAAC,CAAC;cACF,IAAI,CAACC,OAAO,CAACC,IAAI,CAAC;gBAChBC,IAAI,EAAE;cACR,CAAC,CAAC;YACJ,OAAO;cACL,IAAI,CAACL,QAAQ,CAAC;gBACZpC,OAAO,EAACsB,GAAG,CAACoB,GAAG;gBACfL,IAAI,EAAE,OAAO;gBACbC,MAAM,EAAE;cACV,CAAC,CAAC;YACJ;YACA,IAAI,CAAC9C,UAAS,GAAI,KAAK;UACzB,CAAC,CAAC;QACJ;MAEF,CAAC,CAAC;IACJ,CAAC;IAEF;IACCmD,MAAMA,CAAA,EAAG;MACP,IAAI,CAACJ,OAAO,CAACC,IAAI,CAAC;QAChBC,IAAI,EAAE;MACR,CAAC,CAAC;IACJ,CAAC;IAGL3B,mBAAmBA,CAAA,EAAG;MACpB,IAAIG,IAAG,GAAI,CAAC,CAAC;MACb,IAAI,CAACC,WAAU,GAAI,IAAI;MACvB,IAAIC,GAAE,GAAIlC,IAAG,GAAI,gDAAgD;MACjED,OAAO,CAACoC,IAAI,CAACD,GAAG,EAAEF,IAAI,CAAC,CAACI,IAAI,CAAEC,GAAG,IAAK;QACpC,IAAI,CAAC5B,gBAAe,GAAI4B,GAAG,CAACI,OAAO;MACrC,CAAC,CAAC;IACJ,CAAC;IAEDX,gBAAgBA,CAAA,EAAG;MACjB,IAAIE,IAAG,GAAI,CAAC,CAAC;MACb,IAAI,CAACC,WAAU,GAAI,IAAI;MACvB,IAAIC,GAAE,GAAIlC,IAAG,GAAI,6CAA6C;MAC9DD,OAAO,CAACoC,IAAI,CAACD,GAAG,EAAEF,IAAI,CAAC,CAACI,IAAI,CAAEC,GAAG,IAAK;QACpC,IAAI,CAAC3B,aAAY,GAAI2B,GAAG,CAACI,OAAO;MAClC,CAAC,CAAC;IACJ,CAAC;IAED;IACAkB,gBAAgBA,CAAA,EAAG;MACjB,IAAI,CAACnD,QAAQ,CAACc,IAAG,GAAI,EAAE,EAAE;MACzB,IAAI,CAACuB,mBAAmB,CAAC,CAAC;IAC5B,CAAC;IAED;IACAe,cAAcA,CAAA,EAAG;MACf,IAAI,CAACpD,QAAQ,CAACc,IAAG,GAAI,EAAE,EAAE;MACzB,IAAI,CAACuB,mBAAmB,CAAC,CAAC;IAC5B,CAAC;IAED;IACAA,mBAAmBA,CAAA,EAAG;MACpB,IAAI,CAAC,IAAI,CAACrC,QAAQ,CAACkC,IAAG,IAAK,CAAC,IAAI,CAAClC,QAAQ,CAACW,GAAG,EAAE;QAC7C,IAAI,CAACR,qBAAoB,GAAI,EAAE;QAC/B;MACF;MAEA,IAAIqB,IAAG,GAAI;QACTU,IAAI,EAAE,IAAI,CAAClC,QAAQ,CAACkC,IAAI;QACxBmB,SAAS,EAAE,IAAI,CAACrD,QAAQ,CAACW;MAC3B,CAAC;MACD,IAAIe,GAAE,GAAIlC,IAAG,GAAI,oBAAoB;MACrCD,OAAO,CAACoC,IAAI,CAACD,GAAG,EAAEF,IAAI,CAAC,CAACI,IAAI,CAAEC,GAAG,IAAK;QACpC,IAAIA,GAAG,CAACa,IAAG,IAAK,GAAG,EAAE;UACnB,IAAI,CAACvC,qBAAoB,GAAI0B,GAAG,CAACI,OAAO;QAC1C,OAAO;UACL,IAAI,CAAC9B,qBAAoB,GAAI,EAAE;UAC/B,IAAI,CAACwC,QAAQ,CAAC;YACZpC,OAAO,EAAEsB,GAAG,CAACoB,GAAE,IAAK,UAAU;YAC9BL,IAAI,EAAE,OAAO;YACbC,MAAM,EAAE;UACV,CAAC,CAAC;QACJ;MACF,CAAC,CAAC,CAACS,KAAK,CAAC,MAAM;QACb,IAAI,CAACnD,qBAAoB,GAAI,EAAE;QAC/B,IAAI,CAACwC,QAAQ,CAAC;UACZpC,OAAO,EAAE,UAAU;UACnBqC,IAAI,EAAE,OAAO;UACbC,MAAM,EAAE;QACV,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;EAIE;AACN"}]}