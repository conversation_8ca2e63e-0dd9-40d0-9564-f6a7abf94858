{"remainingRequest": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\admin\\total\\Total3.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\admin\\total\\Total3.vue", "mtime": 1749044639949}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749043011899}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749043012919}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749043011899}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749043013130}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\admin\\total\\Total3.vue"], "names": [], "mappings": ";;CASC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CAClC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;CAEtD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;GACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;KACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;OACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;SACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;OACd,CAAC;OACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;OACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;KACb,CAAC;GACH,CAAC;GACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;KACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;GACpB,CAAC;GACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;KAEP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;OACR,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;OAE3C,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;;OAEb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;SACpC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;WACnB,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;WACpB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;WACjB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;WACjB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;aAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;WACzB;WACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;WACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;WACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SACpB,EAAE,CAAC,CAAC,CAAC,EAAE;WACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SAC9B;OACF,CAAC,CAAC;KACJ,CAAC;;KAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;OACZ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;OACnD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;OACtC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;SACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;WACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;WAChB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SACnB,CAAC;SACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SACP;WACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;WAEf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;aAEX,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;eACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aAC3B;WACF;SACF,CAAC;SACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;WACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SACd,CAAC;SACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;WACN;aACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aAClB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;WACb;SACF;OACF,CAAC;OACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KAC3B;GACF;CACF,CAAC", "file": "I:/product4/B7839DormManager/DormManager-web/src/views/admin/total/Total3.vue", "sourceRoot": "", "sourcesContent": ["<template>\n    <div style=\"width: 100%;line-height: 30px;text-align: left;\">\n        <div class=\"echart\" id=\"mychart\" :style=\"myChartStyle\"></div>\n \n    </div>\n</template>\n\n<script>\n\n import * as echarts from \"echarts\";\n import request, { base } from \"../../../../utils/http\";\n \n export default {\n   data() {\n     return {\n       myChartStyle: {\n         height: \"500px\",\n         width: \"100%\"\n       },\n       pieData: [],\n       pieName: [],\n     };\n   },\n   mounted() {\n     this.getdata();\n     this.initEcharts();\n   },\n   methods: {\n \n     //数据初始化\n     getdata() {\n       let url = base + \"/ReportData/queryReport4\";\n \n       let para = {};\n \n       request.post(url, para).then((res) => {\n         if (res.code == 200) {\n           var ss = res.resdata;\n           var pieName2 = [];\n           var pieData2 = [];\n           for (let i = 0; i < ss.length; i++) {\n             pieName2[i] = ss[i].name;\n             pieData2[i] = ss[i].num;\n           }\n           this.pieName = pieName2;\n           this.pieData = pieData2;\n           this.initEcharts();\n         } else {\n           this.$message.error(res.msg);\n         }\n       });\n     },\n \n     initEcharts() {\n       const chartDom = document.getElementById(\"mychart\");\n       const myChart = echarts.init(chartDom);\n       const option = {\n         xAxis: {\n           type: \"category\",\n           data: this.pieName\n         },\n         tooltip:\n         {\n           trigger: 'axis',\n \n           axisPointer: {\n \n             type: 'cross',\n             label: {\n               backgroundColor: '#6a7985'\n             }\n           }\n         },\n         yAxis: {\n           type: \"value\"\n         },\n         series: [\n           {\n             data: this.pieData,\n             type: \"line\",\n             smooth: true\n           }\n         ]\n       };\n       myChart.setOption(option);\n     }\n   }\n };\n</script>\n<style scoped>\n</style>\n \n\n"]}]}