{"remainingRequest": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\Login.vue?vue&type=template&id=26084dc2&scoped=true", "dependencies": [{"path": "I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\Login.vue", "mtime": 1749045164779}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\babel.config.js", "mtime": 1749034452000}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749043011899}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749043012919}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749043013784}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749043011899}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749043013130}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["class", "style", "action", "_createElementVNode", "_createElementBlock", "_hoisted_1", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_hoisted_5", "_hoisted_6", "_hoisted_7", "_hoisted_8", "type", "placeholder", "$data", "loginModel", "username", "$event", "_hoisted_9", "password", "_hoisted_10", "_hoisted_11", "_createVNode", "_component_el_radio", "label", "radio", "_component_el_button", "onClick", "$options", "login", "href", "_cache", "args", "toreg", "_component_el_dialog", "title", "formVisible", "width", "_component_el_form", "model", "formData", "ref", "rules", "_ctx", "align", "_component_el_form_item", "prop", "_component_el_input", "sno", "password2", "stname", "_component_el_radio_group", "sex", "onChange", "onGenderChange", "phone", "_component_el_select", "dbid", "size", "onBuildingChange", "_Fragment", "_renderList", "dormbuildingList", "item", "_createBlock", "_component_el_option", "key", "dbname", "value", "doro", "disabled", "filteredDormitoryList", "specialty", "clsname", "reg", "loading", "btnLoading"], "sources": ["I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\Login.vue"], "sourcesContent": ["<template>\n\n  <div class=\"auth-wrapper aut-bg-img\">\n        <div class=\"auth-content\"  style=\"width: 450px;\">\n             <form action=\"#\">\n            <div class=\"card\" style=\"padding-top: 30px; padding-bottom: 30px; \">\n                <div class=\"card-body text-center\">\n                    <div class=\"mb-4\">\n                        <i class=\"feather icon-unlock auth-icon\"></i>\n                    </div>\n                    <h3 class=\"mb-4\">宿舍管理系统</h3>\n                    <div class=\"input-group mb-3\">\n                        <input type=\"text\" class=\"form-control\" placeholder=\"用户名\" v-model=\"loginModel.username\">\n                    </div>\n                    <div class=\"input-group mb-4\">\n                        <input type=\"password\" class=\"form-control\" placeholder=\"密码\" v-model=\"loginModel.password\">\n                    </div>\n                       <div class=\"form-group text-left\">\n                        <div class=\"checkbox checkbox-fill d-inline\">\n                          <el-radio label=\"管理员\" v-model=\"loginModel.radio\">管理员</el-radio>\n                                   <el-radio label=\"学生\" v-model=\"loginModel.radio\">学生</el-radio>\n      <el-radio label=\"维修员\" v-model=\"loginModel.radio\">维修员</el-radio>\n      <el-radio label=\"宿管阿姨\" v-model=\"loginModel.radio\">宿管阿姨</el-radio>\n\n\n                        </div>\n                    </div>\n\n\n            <el-button class=\"btn btn-primary shadow-2 mb-4\"  @click=\"login\">登录</el-button>\n                     <a href=\"#\"  @click=\"toreg\"> 新学生注册 </a>\n <el-dialog title=\"学生注册\"  v-model=\"formVisible\" width=\"40%\" :close-on-click-modal=\"false\">\n <el-form :model=\"formData\" label-width=\"20%\" ref=\"formDataRef\" :rules=\"rules\"  align=\"left\">\n<el-form-item label=\"学号\" prop=\"sno\">\n<el-input v-model=\"formData.sno\" placeholder=\"学号\"  style=\"width:50%;\" ></el-input>\n</el-form-item>\n<el-form-item label=\"登录密码\" prop=\"password\">\n<el-input type=\"password\" v-model=\"formData.password\" placeholder=\"登录密码\"  style=\"width:50%;\" ></el-input>\n</el-form-item>\n<el-form-item label=\"确认密码\" prop=\"password2\">\n<el-input type=\"password\" v-model=\"formData.password2\" placeholder=\"确认密码\"  style=\"width:50%;\" ></el-input>\n</el-form-item>\n<el-form-item label=\"姓名\" prop=\"stname\">\n<el-input v-model=\"formData.stname\" placeholder=\"姓名\"  style=\"width:50%;\" ></el-input>\n</el-form-item>\n<el-form-item label=\"性别\" prop=\"sex\">\n<el-radio-group v-model=\"formData.sex\" @change=\"onGenderChange\">\n<el-radio label=\"男\">\n男\n</el-radio>\n<el-radio label=\"女\">\n女\n</el-radio>\n</el-radio-group>\n</el-form-item>\n<el-form-item label=\"手机号码\" prop=\"phone\">\n<el-input v-model=\"formData.phone\" placeholder=\"手机号码\"  style=\"width:50%;\" ></el-input>\n</el-form-item>\n<el-form-item label=\"宿舍楼\" prop=\"dbid\">\n<el-select v-model=\"formData.dbid\" placeholder=\"请选择\"  size=\"small\" @change=\"onBuildingChange\">\n<el-option v-for=\"item in dormbuildingList\" :key=\"item.dbid\" :label=\"item.dbname\" :value=\"item.dbid\"></el-option>\n</el-select>\n</el-form-item>\n<el-form-item label=\"宿舍\" prop=\"doro\">\n<el-select v-model=\"formData.doro\" placeholder=\"请先选择宿舍楼和性别\"  size=\"small\" :disabled=\"!formData.dbid || !formData.sex\">\n<el-option v-for=\"item in filteredDormitoryList\" :key=\"item.doro\" :label=\"item.doro\" :value=\"item.doro\"></el-option>\n</el-select>\n</el-form-item>\n<el-form-item label=\"专业\" prop=\"specialty\">\n<el-input v-model=\"formData.specialty\" placeholder=\"专业\"  style=\"width:50%;\" ></el-input>\n</el-form-item>\n<el-form-item label=\"班级\" prop=\"clsname\">\n<el-input v-model=\"formData.clsname\" placeholder=\"班级\"  style=\"width:50%;\" ></el-input>\n</el-form-item>\n<el-form-item>\n<el-button type=\"primary\" @click=\"reg\" :loading=\"btnLoading\" >注 册</el-button>\n</el-form-item>\n</el-form>\n</el-dialog>\n\n\n                </div>\n            </div>\n            </form>\n\n        </div>\n    </div>\n\n\n\n</template>\n<script>\nimport request, { base } from \"../../utils/http\";\nexport default {\n  name: \"Login\",\n  data() {\n    return {\n      year: new Date().getFullYear(),\n      loginModel: {\n        username: \"\",\n        password: \"\",\n        radio: \"管理员\",\n      },\n      loginModel2: {},\n     add: true, //是否是添加\n      formVisible: false,\n      formData:{},\n      dormbuildingList: [], //宿舍楼列表\n      dormitoryList: [], //所有宿舍列表\n      filteredDormitoryList: [], //过滤后的宿舍列表\n\n      addrules: {\n          sno: [{ required: true, message: '请输入学号', trigger: 'blur' },],\n          password: [{ required: true, message: '请输入登录密码', trigger: 'blur' }],\n          password2: [{ required: true, message: '请输入登录密码', trigger: 'blur' },{ validator: (rule, value, callback) => { if (value !== this.formData.password) { callback(new Error('两次输入密码不一致!')); } else { callback(); } }, trigger: 'blur' },],\n          stname: [{ required: true, message: '请输入姓名', trigger: 'blur' },],\n          sex: [{ required: true, message: '请输入性别', trigger: 'blur' },],\n          phone: [{ required: true, message: '请输入手机号码', trigger: 'blur' },        { pattern: /^1[3456789]\\d{9}$/, message: '手机号码格式不正确', trigger: 'blur' },],\n          doro: [{ required: true, message: '请选择宿舍', trigger: 'onchange' }],\n          specialty: [{ required: true, message: '请输入专业', trigger: 'blur' },],\n          clsname: [{ required: true, message: '请输入班级', trigger: 'blur' },],\n      },\n\n\n      btnLoading: false, //按钮是否在加载中\n\n\n    };\n  },\n  mounted() {},\n  created() {\n\n  },\n  methods: {\n    login() {\n      let that = this;\n\n      if (that.loginModel.username == \"\") {\n        that.$message({\n          message: \"请输入账号\",\n          type: \"warning\",\n        });\n        return;\n      }\n      if (that.loginModel.password == \"\") {\n        that.$message({\n          message: \"请输入密码\",\n          type: \"warning\",\n        });\n        return;\n      }\n\n      this.loading = true;\n     var role = that.loginModel.radio; //获取身份\nif (role == '管理员') {\n      let url = base + \"/admin/login\";\n      this.loginModel2.aname = this.loginModel.username;\n      this.loginModel2.loginpassword = this.loginModel.password;\n      request.post(url, this.loginModel2).then((res) => {\n        this.loading = false;\n        if (res.code == 200) {\n          console.log(JSON.stringify(res.resdata));\n          sessionStorage.setItem(\"user\", JSON.stringify(res.resdata));\n          sessionStorage.setItem(\"userLname\", res.resdata.aname);\n          sessionStorage.setItem(\"role\", \"管理员\");\n          this.$router.push(\"/main\");\n        } else {\n          this.$message({\n            message: res.msg,\n            type: \"error\",\n          });\n        }\n      });\n          }\nelse if (role == '宿管阿姨') {\n      let url = base + \"/hostess/login\";\n      this.loginModel2.hno = this.loginModel.username;\n      this.loginModel2.password = this.loginModel.password;\n      request.post(url, this.loginModel2).then((res) => {\n        this.loading = false;\n        if (res.code == 200) {\n          console.log(JSON.stringify(res.resdata));\n          sessionStorage.setItem(\"user\", JSON.stringify(res.resdata));\n          sessionStorage.setItem(\"userLname\", res.resdata.hno);\n          sessionStorage.setItem(\"role\", \"宿管阿姨\");\n          this.$router.push(\"/main\");\n        } else {\n          this.$message({\n            message: res.msg,\n            type: \"error\",\n          });\n        }\n      });\n          }\nelse if (role == '维修员') {\n      let url = base + \"/repairmen/login\";\n      this.loginModel2.rno = this.loginModel.username;\n      this.loginModel2.password = this.loginModel.password;\n      request.post(url, this.loginModel2).then((res) => {\n        this.loading = false;\n        if (res.code == 200) {\n          console.log(JSON.stringify(res.resdata));\n          sessionStorage.setItem(\"user\", JSON.stringify(res.resdata));\n          sessionStorage.setItem(\"userLname\", res.resdata.rno);\n          sessionStorage.setItem(\"role\", \"维修员\");\n          this.$router.push(\"/main\");\n        } else {\n          this.$message({\n            message: res.msg,\n            type: \"error\",\n          });\n        }\n      });\n          }\nelse if (role == '学生') {\n      let url = base + \"/student/login\";\n      this.loginModel2.sno = this.loginModel.username;\n      this.loginModel2.password = this.loginModel.password;\n      request.post(url, this.loginModel2).then((res) => {\n        this.loading = false;\n        if (res.code == 200) {\n          console.log(JSON.stringify(res.resdata));\n          sessionStorage.setItem(\"user\", JSON.stringify(res.resdata));\n          sessionStorage.setItem(\"userLname\", res.resdata.sno);\n          sessionStorage.setItem(\"role\", \"学生\");\n          this.$router.push(\"/main\");\n        } else {\n          this.$message({\n            message: res.msg,\n            type: \"error\",\n          });\n        }\n      });\n          }\n\n\n    },\n\n    toreg() {\n    this.formVisible = true;\n    this.add = true;\n    this.isClear = true;\n    this.rules = this.addrules;\n    this.getdormbuildingList(); // 加载宿舍楼列表\n    this.$nextTick(() => {\n        this.$refs[\"formDataRef\"].resetFields();\n    });\n},\n\n//注册\nreg() {\n    //表单验证\n    this.$refs[\"formDataRef\"].validate((valid) => {\n\n        if (valid) {\n            let url = base + \"/student/add\"; //请求地址\n            this.btnLoading = true; //按钮加载状态\n            request.post(url, this.formData).then((res) => { //请求接口\n                if (res.code == 200) {\n                    this.$message({\n                        message: \"恭喜您，注册成功，请登录！\",\n                        type: \"success\",\n                        offset: 320,\n                    });\n                    this.formVisible = false; //关闭表单\n                    this.btnLoading = false; //按钮加载状态\n                    this.$refs[\"formDataRef\"].resetFields(); //重置表单\n                    this.$refs[\"formDataRef\"].clearValidate();\n                }\n                else if (res.code == 201) {\n                    this.$message({\n                        message: res.msg,\n                        type: \"error\",\n                        offset: 320,\n                    });\n                }\n                else {\n                    this.$message({\n                        message: \"服务器错误\",\n                        type: \"error\",\n                        offset: 320,\n                    });\n                }\n            });\n        }\n    });\n},\n// 获取宿舍楼列表\ngetdormbuildingList() {\n  let para = {};\n  let url = base + \"/dormbuilding/list?currentPage=1&pageSize=1000\";\n  request.post(url, para).then((res) => {\n    this.dormbuildingList = res.resdata;\n  });\n},\n\n// 宿舍楼变化时的处理\nonBuildingChange() {\n  this.formData.doro = ''; // 清空宿舍选择\n  this.updateDormitoryList();\n},\n\n// 性别变化时的处理\nonGenderChange() {\n  this.formData.doro = ''; // 清空宿舍选择\n  this.updateDormitoryList();\n},\n\n// 更新宿舍列表\nupdateDormitoryList() {\n  if (!this.formData.dbid || !this.formData.sex) {\n    this.filteredDormitoryList = [];\n    return;\n  }\n\n  let para = {\n    dbid: this.formData.dbid,\n    dorgender: this.formData.sex\n  };\n  let url = base + \"/dormitory/listByBuildingAndGender\";\n  request.post(url, para).then((res) => {\n    if (res.code == 200) {\n      this.filteredDormitoryList = res.resdata;\n    } else {\n      this.filteredDormitoryList = [];\n      this.$message({\n        message: res.msg || \"获取宿舍列表失败\",\n        type: \"error\",\n        offset: 320,\n      });\n    }\n  }).catch(() => {\n    this.filteredDormitoryList = [];\n    this.$message({\n      message: \"获取宿舍列表失败\",\n      type: \"error\",\n      offset: 320,\n    });\n  });\n},\n\n\n\n\n\n  },\n};\n</script>\n\n<style scoped>\n@import url(../assets/css/htstyle.css);\n\n</style>\n\n\n"], "mappings": ";;;EAEOA,KAAK,EAAC;AAAyB;;EACzBA,KAAK,EAAC,cAAc;EAAEC,KAAqB,EAArB;IAAA;EAAA;;;EAChBC,MAAM,EAAC;AAAG;;EACZF,KAAK,EAAC,MAAM;EAACC,KAAiD,EAAjD;IAAA;IAAA;EAAA;;;EACTD,KAAK,EAAC;AAAuB;gEAC9BG,mBAAA,CAEM;EAFDH,KAAK,EAAC;AAAM,I,aACbG,mBAAA,CAA6C;EAA1CH,KAAK,EAAC;AAA+B,G;gEAE5CG,mBAAA,CAA4B;EAAxBH,KAAK,EAAC;AAAM,GAAC,QAAM;;EAClBA,KAAK,EAAC;AAAkB;;EAGxBA,KAAK,EAAC;AAAkB;;EAGrBA,KAAK,EAAC;AAAsB;;EAC3BA,KAAK,EAAC;AAAiC;kDACO,KAAG;kDACK,IAAE;kDAC9B,KAAG;kDACF,MAAI;kDAOiB,IAAE;kDAkB3D,KAEpB;kDACoB,KAEpB;kDAuB8D,KAAG;;;;;;;;;;;uBAzE/DI,mBAAA,CAoFQ,OApFRC,UAoFQ,GAnFFF,mBAAA,CAkFM,OAlFNG,UAkFM,GAjFDH,mBAAA,CA+EM,QA/ENI,UA+EM,GA9EPJ,mBAAA,CA6EM,OA7ENK,UA6EM,GA5EFL,mBAAA,CA2EM,OA3ENM,UA2EM,GA1EFC,UAEM,EACNC,UAA4B,EAC5BR,mBAAA,CAEM,OAFNS,UAEM,G,gBADFT,mBAAA,CAAwF;IAAjFU,IAAI,EAAC,MAAM;IAACb,KAAK,EAAC,cAAc;IAACc,WAAW,EAAC,KAAK;+DAAUC,KAAA,CAAAC,UAAU,CAACC,QAAQ,GAAAC,MAAA;iDAAnBH,KAAA,CAAAC,UAAU,CAACC,QAAQ,E,KAE1Fd,mBAAA,CAEM,OAFNgB,UAEM,G,gBADFhB,mBAAA,CAA2F;IAApFU,IAAI,EAAC,UAAU;IAACb,KAAK,EAAC,cAAc;IAACc,WAAW,EAAC,IAAI;+DAAUC,KAAA,CAAAC,UAAU,CAACI,QAAQ,GAAAF,MAAA;iDAAnBH,KAAA,CAAAC,UAAU,CAACI,QAAQ,E,KAE1FjB,mBAAA,CASG,OATHkB,WASG,GARFlB,mBAAA,CAOM,OAPNmB,WAOM,GANJC,YAAA,CAA+DC,mBAAA;IAArDC,KAAK,EAAC,KAAK;gBAAUV,KAAA,CAAAC,UAAU,CAACU,KAAK;+DAAhBX,KAAA,CAAAC,UAAU,CAACU,KAAK,GAAAR,MAAA;;sBAAE,MAAG,C;;qCAC3CK,YAAA,CAA6DC,mBAAA;IAAnDC,KAAK,EAAC,IAAI;gBAAUV,KAAA,CAAAC,UAAU,CAACU,KAAK;+DAAhBX,KAAA,CAAAC,UAAU,CAACU,KAAK,GAAAR,MAAA;;sBAAE,MAAE,C;;qCAC/EK,YAAA,CAA+DC,mBAAA;IAArDC,KAAK,EAAC,KAAK;gBAAUV,KAAA,CAAAC,UAAU,CAACU,KAAK;+DAAhBX,KAAA,CAAAC,UAAU,CAACU,KAAK,GAAAR,MAAA;;sBAAE,MAAG,C;;qCACpDK,YAAA,CAAiEC,mBAAA;IAAvDC,KAAK,EAAC,MAAM;gBAAUV,KAAA,CAAAC,UAAU,CAACU,KAAK;+DAAhBX,KAAA,CAAAC,UAAU,CAACU,KAAK,GAAAR,MAAA;;sBAAE,MAAI,C;;yCAOhDK,YAAA,CAA+EI,oBAAA;IAApE3B,KAAK,EAAC,+BAA+B;IAAG4B,OAAK,EAAEC,QAAA,CAAAC;;sBAAO,MAAE,C;;kCAC1D3B,mBAAA,CAAuC;IAApC4B,IAAI,EAAC,GAAG;IAAGH,OAAK,EAAAI,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEJ,QAAA,CAAAK,KAAA,IAAAL,QAAA,CAAAK,KAAA,IAAAD,IAAA,CAAK;KAAE,SAAO,GACvDV,YAAA,CA+CWY,oBAAA;IA/CAC,KAAK,EAAC,MAAM;gBAAWrB,KAAA,CAAAsB,WAAW;iEAAXtB,KAAA,CAAAsB,WAAW,GAAAnB,MAAA;IAAEoB,KAAK,EAAC,KAAK;IAAE,sBAAoB,EAAE;;sBAClF,MA6CS,CA7CTf,YAAA,CA6CSgB,kBAAA;MA7CCC,KAAK,EAAEzB,KAAA,CAAA0B,QAAQ;MAAE,aAAW,EAAC,KAAK;MAACC,GAAG,EAAC,aAAa;MAAEC,KAAK,EAAEC,IAAA,CAAAD,KAAK;MAAGE,KAAK,EAAC;;wBACtF,MAEe,CAFftB,YAAA,CAEeuB,uBAAA;QAFDrB,KAAK,EAAC,IAAI;QAACsB,IAAI,EAAC;;0BAC9B,MAAkF,CAAlFxB,YAAA,CAAkFyB,mBAAA;sBAA/DjC,KAAA,CAAA0B,QAAQ,CAACQ,GAAG;qEAAZlC,KAAA,CAAA0B,QAAQ,CAACQ,GAAG,GAAA/B,MAAA;UAAEJ,WAAW,EAAC,IAAI;UAAEb,KAAkB,EAAlB;YAAA;UAAA;;;UAEnDsB,YAAA,CAEeuB,uBAAA;QAFDrB,KAAK,EAAC,MAAM;QAACsB,IAAI,EAAC;;0BAChC,MAAyG,CAAzGxB,YAAA,CAAyGyB,mBAAA;UAA/FnC,IAAI,EAAC,UAAU;sBAAUE,KAAA,CAAA0B,QAAQ,CAACrB,QAAQ;qEAAjBL,KAAA,CAAA0B,QAAQ,CAACrB,QAAQ,GAAAF,MAAA;UAAEJ,WAAW,EAAC,MAAM;UAAEb,KAAkB,EAAlB;YAAA;UAAA;;;UAE1EsB,YAAA,CAEeuB,uBAAA;QAFDrB,KAAK,EAAC,MAAM;QAACsB,IAAI,EAAC;;0BAChC,MAA0G,CAA1GxB,YAAA,CAA0GyB,mBAAA;UAAhGnC,IAAI,EAAC,UAAU;sBAAUE,KAAA,CAAA0B,QAAQ,CAACS,SAAS;qEAAlBnC,KAAA,CAAA0B,QAAQ,CAACS,SAAS,GAAAhC,MAAA;UAAEJ,WAAW,EAAC,MAAM;UAAEb,KAAkB,EAAlB;YAAA;UAAA;;;UAE3EsB,YAAA,CAEeuB,uBAAA;QAFDrB,KAAK,EAAC,IAAI;QAACsB,IAAI,EAAC;;0BAC9B,MAAqF,CAArFxB,YAAA,CAAqFyB,mBAAA;sBAAlEjC,KAAA,CAAA0B,QAAQ,CAACU,MAAM;uEAAfpC,KAAA,CAAA0B,QAAQ,CAACU,MAAM,GAAAjC,MAAA;UAAEJ,WAAW,EAAC,IAAI;UAAEb,KAAkB,EAAlB;YAAA;UAAA;;;UAEtDsB,YAAA,CASeuB,uBAAA;QATDrB,KAAK,EAAC,IAAI;QAACsB,IAAI,EAAC;;0BAC9B,MAOiB,CAPjBxB,YAAA,CAOiB6B,yBAAA;sBAPQrC,KAAA,CAAA0B,QAAQ,CAACY,GAAG;uEAAZtC,KAAA,CAAA0B,QAAQ,CAACY,GAAG,GAAAnC,MAAA;UAAGoC,QAAM,EAAEzB,QAAA,CAAA0B;;4BAChD,MAEW,CAFXhC,YAAA,CAEWC,mBAAA;YAFDC,KAAK,EAAC;UAAG;8BAAC,MAEpB,C;;cACAF,YAAA,CAEWC,mBAAA;YAFDC,KAAK,EAAC;UAAG;8BAAC,MAEpB,C;;;;;;;UAGAF,YAAA,CAEeuB,uBAAA;QAFDrB,KAAK,EAAC,MAAM;QAACsB,IAAI,EAAC;;0BAChC,MAAsF,CAAtFxB,YAAA,CAAsFyB,mBAAA;sBAAnEjC,KAAA,CAAA0B,QAAQ,CAACe,KAAK;uEAAdzC,KAAA,CAAA0B,QAAQ,CAACe,KAAK,GAAAtC,MAAA;UAAEJ,WAAW,EAAC,MAAM;UAAEb,KAAkB,EAAlB;YAAA;UAAA;;;UAEvDsB,YAAA,CAIeuB,uBAAA;QAJDrB,KAAK,EAAC,KAAK;QAACsB,IAAI,EAAC;;0BAC/B,MAEY,CAFZxB,YAAA,CAEYkC,oBAAA;sBAFQ1C,KAAA,CAAA0B,QAAQ,CAACiB,IAAI;uEAAb3C,KAAA,CAAA0B,QAAQ,CAACiB,IAAI,GAAAxC,MAAA;UAAEJ,WAAW,EAAC,KAAK;UAAE6C,IAAI,EAAC,OAAO;UAAEL,QAAM,EAAEzB,QAAA,CAAA+B;;4BACjE,MAAgC,E,kBAA3CxD,mBAAA,CAAiHyD,SAAA,QAAAC,WAAA,CAAvF/C,KAAA,CAAAgD,gBAAgB,EAAxBC,IAAI;iCAAtBC,YAAA,CAAiHC,oBAAA;cAApEC,GAAG,EAAEH,IAAI,CAACN,IAAI;cAAGjC,KAAK,EAAEuC,IAAI,CAACI,MAAM;cAAGC,KAAK,EAAEL,IAAI,CAACN;;;;;;;UAG/FnC,YAAA,CAIeuB,uBAAA;QAJDrB,KAAK,EAAC,IAAI;QAACsB,IAAI,EAAC;;0BAC9B,MAEY,CAFZxB,YAAA,CAEYkC,oBAAA;sBAFQ1C,KAAA,CAAA0B,QAAQ,CAAC6B,IAAI;uEAAbvD,KAAA,CAAA0B,QAAQ,CAAC6B,IAAI,GAAApD,MAAA;UAAEJ,WAAW,EAAC,YAAY;UAAE6C,IAAI,EAAC,OAAO;UAAEY,QAAQ,GAAGxD,KAAA,CAAA0B,QAAQ,CAACiB,IAAI,KAAK3C,KAAA,CAAA0B,QAAQ,CAACY;;4BACtG,MAAqC,E,kBAAhDjD,mBAAA,CAAoHyD,SAAA,QAAAC,WAAA,CAA1F/C,KAAA,CAAAyD,qBAAqB,EAA7BR,IAAI;iCAAtBC,YAAA,CAAoHC,oBAAA;cAAlEC,GAAG,EAAEH,IAAI,CAACM,IAAI;cAAG7C,KAAK,EAAEuC,IAAI,CAACM,IAAI;cAAGD,KAAK,EAAEL,IAAI,CAACM;;;;;;;UAGlG/C,YAAA,CAEeuB,uBAAA;QAFDrB,KAAK,EAAC,IAAI;QAACsB,IAAI,EAAC;;0BAC9B,MAAwF,CAAxFxB,YAAA,CAAwFyB,mBAAA;sBAArEjC,KAAA,CAAA0B,QAAQ,CAACgC,SAAS;uEAAlB1D,KAAA,CAAA0B,QAAQ,CAACgC,SAAS,GAAAvD,MAAA;UAAEJ,WAAW,EAAC,IAAI;UAAEb,KAAkB,EAAlB;YAAA;UAAA;;;UAEzDsB,YAAA,CAEeuB,uBAAA;QAFDrB,KAAK,EAAC,IAAI;QAACsB,IAAI,EAAC;;0BAC9B,MAAsF,CAAtFxB,YAAA,CAAsFyB,mBAAA;sBAAnEjC,KAAA,CAAA0B,QAAQ,CAACiC,OAAO;uEAAhB3D,KAAA,CAAA0B,QAAQ,CAACiC,OAAO,GAAAxD,MAAA;UAAEJ,WAAW,EAAC,IAAI;UAAEb,KAAkB,EAAlB;YAAA;UAAA;;;UAEvDsB,YAAA,CAEeuB,uBAAA;0BADf,MAA6E,CAA7EvB,YAAA,CAA6EI,oBAAA;UAAlEd,IAAI,EAAC,SAAS;UAAEe,OAAK,EAAEC,QAAA,CAAA8C,GAAG;UAAGC,OAAO,EAAE7D,KAAA,CAAA8D;;4BAAa,MAAG,C"}]}