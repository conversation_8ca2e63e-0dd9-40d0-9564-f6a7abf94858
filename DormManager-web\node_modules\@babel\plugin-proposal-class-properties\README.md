# @babel/plugin-proposal-class-properties

> This plugin transforms static class properties as well as properties declared with the property initializer syntax

See our website [@babel/plugin-proposal-class-properties](https://babeljs.io/docs/en/babel-plugin-proposal-class-properties) for more information.

## Install

Using npm:

```sh
npm install --save-dev @babel/plugin-proposal-class-properties
```

or using yarn:

```sh
yarn add @babel/plugin-proposal-class-properties --dev
```
