{"version": 3, "names": ["_utils", "require", "isRegeneratorSource", "source", "_default", "visitor", "ImportDeclaration", "path", "getImportSource", "regeneratorImportExcluded", "remove", "Program", "get", "for<PERSON>ach", "bodyPath", "getRequireSource", "name", "pre", "post", "opts", "debug", "filename", "file", "process", "env", "BABEL_ENV", "replace", "console", "log"], "sources": ["../../src/polyfills/regenerator.ts"], "sourcesContent": ["import { getImportSource, getRequireSource } from \"./utils.ts\";\nimport type { Visitor } from \"@babel/traverse\";\nimport type { PluginObject, PluginPass } from \"@babel/core\";\nimport type { Options } from \"../types.ts\";\n\nfunction isRegeneratorSource(source: string) {\n  return (\n    source === \"regenerator-runtime/runtime\" ||\n    source === \"regenerator-runtime/runtime.js\"\n  );\n}\n\ntype State = {\n  regeneratorImportExcluded: boolean;\n};\n\nexport default function (): PluginObject<State & PluginPass<Options>> {\n  const visitor: Visitor<State & PluginPass> = {\n    ImportDeclaration(path) {\n      if (isRegeneratorSource(getImportSource(path))) {\n        this.regeneratorImportExcluded = true;\n        path.remove();\n      }\n    },\n    Program(path) {\n      path.get(\"body\").forEach(bodyPath => {\n        if (isRegeneratorSource(getRequireSource(bodyPath))) {\n          this.regeneratorImportExcluded = true;\n          bodyPath.remove();\n        }\n      });\n    },\n  };\n\n  return {\n    name: \"preset-env/remove-regenerator\",\n    visitor,\n    pre() {\n      this.regeneratorImportExcluded = false;\n    },\n    post() {\n      if (this.opts.debug && this.regeneratorImportExcluded) {\n        let filename = this.file.opts.filename;\n        // normalize filename to generate consistent preset-env test fixtures\n        if (process.env.BABEL_ENV === \"test\") {\n          filename = filename.replace(/\\\\/g, \"/\");\n        }\n        console.log(\n          `\\n[${filename}] Based on your targets, regenerator-runtime import excluded.`,\n        );\n      }\n    },\n  };\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AAKA,SAASC,mBAAmBA,CAACC,MAAc,EAAE;EAC3C,OACEA,MAAM,KAAK,6BAA6B,IACxCA,MAAM,KAAK,gCAAgC;AAE/C;AAMe,SAAAC,SAAA,EAAuD;EACpE,MAAMC,OAAoC,GAAG;IAC3CC,iBAAiBA,CAACC,IAAI,EAAE;MACtB,IAAIL,mBAAmB,CAAC,IAAAM,sBAAe,EAACD,IAAI,CAAC,CAAC,EAAE;QAC9C,IAAI,CAACE,yBAAyB,GAAG,IAAI;QACrCF,IAAI,CAACG,MAAM,CAAC,CAAC;MACf;IACF,CAAC;IACDC,OAAOA,CAACJ,IAAI,EAAE;MACZA,IAAI,CAACK,GAAG,CAAC,MAAM,CAAC,CAACC,OAAO,CAACC,QAAQ,IAAI;QACnC,IAAIZ,mBAAmB,CAAC,IAAAa,uBAAgB,EAACD,QAAQ,CAAC,CAAC,EAAE;UACnD,IAAI,CAACL,yBAAyB,GAAG,IAAI;UACrCK,QAAQ,CAACJ,MAAM,CAAC,CAAC;QACnB;MACF,CAAC,CAAC;IACJ;EACF,CAAC;EAED,OAAO;IACLM,IAAI,EAAE,+BAA+B;IACrCX,OAAO;IACPY,GAAGA,CAAA,EAAG;MACJ,IAAI,CAACR,yBAAyB,GAAG,KAAK;IACxC,CAAC;IACDS,IAAIA,CAAA,EAAG;MACL,IAAI,IAAI,CAACC,IAAI,CAACC,KAAK,IAAI,IAAI,CAACX,yBAAyB,EAAE;QACrD,IAAIY,QAAQ,GAAG,IAAI,CAACC,IAAI,CAACH,IAAI,CAACE,QAAQ;QAEtC,IAAIE,OAAO,CAACC,GAAG,CAACC,SAAS,KAAK,MAAM,EAAE;UACpCJ,QAAQ,GAAGA,QAAQ,CAACK,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;QACzC;QACAC,OAAO,CAACC,GAAG,CACR,MAAKP,QAAS,+DACjB,CAAC;MACH;IACF;EACF,CAAC;AACH"}