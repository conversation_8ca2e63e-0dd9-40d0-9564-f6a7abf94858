<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mapper.DormitorychangeMapper">
	<select id="findDormitorychangeList"  resultType="Dormitorychange">
		select * from dormitorychange 
	</select>
	
	<select id="query" parameterType="java.util.Map" resultType="Dormitorychange">
	    select  a.*,b.dbname,c.dbname as dbname2  
        from dormitorychange a  left join dormbuilding b on a.dbid=b.dbid	
		left join dormbuilding c on a.dbid2=c.dbid
		<where>
      		<if test="id != null and id !=0 ">
		    and a.id = #{id}
		</if>
		<if test="sno != null and sno != ''">
		    and a.sno = #{sno}
		</if>
		<if test="dbid != null and dbid !=0 ">
		    and a.dbid = #{dbid}
		</if>
		<if test="doro != null and doro != ''">
		    and a.doro = #{doro}
		</if>
		<if test="dbid2 != null and dbid2 !=0 ">
		    and a.dbid2 = #{dbid2}
		</if>
		<if test="doro2 != null and doro2 != ''">
		    and a.doro2 = #{doro2}
		</if>
		<if test="submissiontime != null and submissiontime != ''">
		    and a.submissiontime = #{submissiontime}
		</if>
		<if test="reviewstatus != null and reviewstatus != ''">
		    and a.reviewstatus = #{reviewstatus}
		</if>
		<if test="condition != null and condition != ''">
		    ${condition} 
		</if>

    </where>

    order by ${sort} id desc

    <if test="page">
			limit #{offset} ,#{pageSize}
		</if>
	</select>	
	
	<select id="getCount" parameterType="java.util.Map" resultType="Int">
		select count(0) from dormitorychange a  
		<where>
      		<if test="id != null and id !=0 ">
		    and a.id = #{id}
		</if>
		<if test="sno != null and sno != ''">
		    and a.sno = #{sno}
		</if>
		<if test="dbid != null and dbid !=0 ">
		    and a.dbid = #{dbid}
		</if>
		<if test="doro != null and doro != ''">
		    and a.doro = #{doro}
		</if>
		<if test="dbid2 != null and dbid2 !=0 ">
		    and a.dbid2 = #{dbid2}
		</if>
		<if test="doro2 != null and doro2 != ''">
		    and a.doro2 = #{doro2}
		</if>
		<if test="submissiontime != null and submissiontime != ''">
		    and a.submissiontime = #{submissiontime}
		</if>
		<if test="reviewstatus != null and reviewstatus != ''">
		    and a.reviewstatus = #{reviewstatus}
		</if>
		<if test="condition != null and condition != ''">
		    ${condition} 
		</if>

    </where>
	</select>	
	
	<select id="queryDormitorychangeById" parameterType="int" resultType="Dormitorychange">
    select  a.*,b.dbname,c.dbname as dbname2  
        from dormitorychange a  left join dormbuilding b on a.dbid=b.dbid	
		left join dormbuilding c on a.dbid2=c.dbid 	 where a.id=#{value}
  </select>
 
	<insert id="insertDormitorychange" useGeneratedKeys="true" keyProperty="id" parameterType="Dormitorychange">
    insert into dormitorychange
    (sno,dbid,doro,dbid2,doro2,applicationreason,submissiontime,reviewstatus,reviewresponse)
    values
    (#{sno},#{dbid},#{doro},#{dbid2},#{doro2},#{applicationreason},now(),#{reviewstatus},#{reviewresponse});
  </insert>
	
	<update id="updateDormitorychange" parameterType="Dormitorychange" >
    update dormitorychange 
    <set>
		<if test="sno != null and sno != ''">
		    sno = #{sno},
		</if>
		<if test="dbid != null ">
		    dbid = #{dbid},
		</if>
		<if test="doro != null and doro != ''">
		    doro = #{doro},
		</if>
		<if test="dbid2 != null ">
		    dbid2 = #{dbid2},
		</if>
		<if test="doro2 != null and doro2 != ''">
		    doro2 = #{doro2},
		</if>
		<if test="applicationreason != null and applicationreason != ''">
		    applicationreason = #{applicationreason},
		</if>
		<if test="submissiontime != null and submissiontime != ''">
		    submissiontime = #{submissiontime},
		</if>
		<if test="reviewstatus != null and reviewstatus != ''">
		    reviewstatus = #{reviewstatus},
		</if>
		<if test="reviewresponse != null and reviewresponse != ''">
		    reviewresponse = #{reviewresponse},
		</if>

    </set>
   <where> 
    <if test="condition != null and condition != ''">
      ${condition}
    </if>
    <if test="id != null or id != ''">
      id=#{id}
    </if>
   </where>     
  </update>	
 
	
	<delete id="deleteDormitorychange" parameterType="int">
    delete from  dormitorychange where id=#{value}
  </delete>

	
	
</mapper>

 
