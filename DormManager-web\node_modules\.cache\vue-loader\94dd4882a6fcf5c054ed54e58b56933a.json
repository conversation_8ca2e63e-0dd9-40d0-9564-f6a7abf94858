{"remainingRequest": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\admin\\dormitorychange\\DormitorychangeManage.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\admin\\dormitorychange\\DormitorychangeManage.vue", "mtime": 1749046404836}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749043011899}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749043012919}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749043011899}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749043013130}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\admin\\dormitorychange\\DormitorychangeManage.vue"], "names": [], "mappings": ";AAyFA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACtD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;EAEZ,CAAC;IACC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;eACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QAClB,CAAC;;QAED,CAAC,CAAC,CAAC,CAAC,EAAE;UACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACvB,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEnB,CAAC,EAAE,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACV,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACR,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACnB,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACzE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC1E;;MAEF,CAAC;IACH,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC;;;IAGD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;;OAGN,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;UACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;YAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC;YACC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;cACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;cACvB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACpD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;gBAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;gBAExB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACf,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBACb,CAAC,CAAC;gBACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACjB,CAAC,CAAC;YACJ,CAAC;YACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;QACrB,CAAC;;QAED,CAAC,EAAE,CAAC;QACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;UAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC;;QAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACT,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE;eACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;eACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAE3C,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;UACvB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;YACpC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE;cAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;YACpB,EAAE,CAAC,CAAC,CAAC,EAAE;cACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YACrB;YACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UAC1B,CAAC,CAAC;QACJ,CAAC;iBACQ,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC;;QAED,CAAC,EAAE,CAAC;QACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;UACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;gBACJ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACZ,CAAC;UACL,CAAC,CAAC;QACJ,CAAC;;QAED,CAAC,EAAE,CAAC;QACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;UACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;gBACJ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACZ,CAAC;UACL,CAAC,CAAC;QACJ,CAAC;;QAED,CAAC,EAAE,CAAC;QACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;UACvB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACR,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;YAC9B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;cACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;gBAChB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAClB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACtD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACzD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAChD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;gBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cACnB,CAAC;cACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;YAC3B;UACF,CAAC,CAAC;QACJ,CAAC;;QAED,CAAC,EAAE,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;YAC3C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;cACtB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC1C,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE;gBACT,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC/C,CAAC;cACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;gBACpC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;kBACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACf,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAChB,CAAC,CAAC;kBACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;kBAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;gBACzB,EAAE,CAAC,CAAC,CAAC,EAAE;kBACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC1B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACd,CAAC,CAAC;gBACJ;gBACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;cACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;gBACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACf,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACd,CAAC,CAAC;gBACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;cACzB,CAAC,CAAC;YACJ;UACF,CAAC,CAAC;QACJ,CAAC;;QAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAChB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxC;UACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;YAChB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACR,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YACV,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACnB,CAAC;QACH,CAAC;;QAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACpB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACd,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;cACR,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjB;QACF;MACF,CAAC;AACP", "file": "I:/product4/B7839DormManager/DormManager-web/src/views/admin/dormitorychange/DormitorychangeManage.vue", "sourceRoot": "", "sourcesContent": ["<template>\n    <div style=\"width: 100%;line-height: 30px;text-align: left;\">\n      <el-col  :span=\"24\"  style=\"padding-bottom: 0px; margin-left: 10px\">\n<el-form :inline=\"true\" :model=\"filters\" >\n<el-form-item>\n<el-input v-model=\"filters.sno\" placeholder=\"学号\"  size=\"small\"></el-input>\n</el-form-item>\n<el-form-item>\n<el-select v-model=\"filters.reviewstatus\" placeholder=\"审核状态\" size=\"small\" clearable>\n<el-option label=\"待审核\" value=\"待审核\"></el-option>\n<el-option label=\"审核通过\" value=\"审核通过\"></el-option>\n<el-option label=\"审核不通过\" value=\"审核不通过\"></el-option>\n</el-select>\n</el-form-item>\n<el-form-item>\n<el-button type=\"primary\" size=\"small\" @click=\"query\" icon=\"el-icon-search\">搜索</el-button>\n</el-form-item>\n </el-form>\n</el-col>\n\n<el-table :data=\"datalist\" border stripe style=\"width: 100%\"  v-loading=\"listLoading\"   highlight-current-row   max-height=\"600\"     size=\"small\">\n<el-table-column prop=\"sno\" label=\"学号\"  align=\"center\"></el-table-column>\n<el-table-column prop=\"dbid\" label=\"原宿舍楼id\"  align=\"center\"></el-table-column>\n<el-table-column prop=\"doro\" label=\"原宿舍编号\"  align=\"center\"></el-table-column>\n<el-table-column prop=\"dbid2\" label=\"更换宿舍楼id\"  align=\"center\"></el-table-column>\n<el-table-column prop=\"doro2\" label=\"更换宿舍编号\"  align=\"center\"></el-table-column>\n<el-table-column prop=\"applicationreason\" label=\"申请原因\"  align=\"center\">\n<template #default=\"scope\">\n<span v-if=\"scope.row.applicationreason != null\">{{scope.row.applicationreason.substring(0,20)}}</span>\n</template>\n</el-table-column>\n<el-table-column prop=\"submissiontime\" label=\"提交时间\"  align=\"center\"></el-table-column>\n<el-table-column prop=\"reviewstatus\" label=\"审核状态\"  align=\"center\">\n<template #default=\"scope\">\n<el-tag :type=\"getStatusType(scope.row.reviewstatus)\">{{ scope.row.reviewstatus }}</el-tag>\n</template>\n</el-table-column>\n<el-table-column prop=\"reviewresponse\" label=\"审核回复\"  align=\"center\">\n<template #default=\"scope\">\n<span v-if=\"scope.row.reviewresponse != null\">{{scope.row.reviewresponse.substring(0,20)}}</span>\n</template>\n</el-table-column>\n<el-table-column label=\"操作\" min-width=\"250\" align=\"center\">\n<template #default=\"scope\">\n<el-button v-if=\"scope.row.reviewstatus === '待审核'\" type=\"success\" size=\"mini\" @click=\"handleReview(scope.$index, scope.row)\" icon=\"el-icon-check\" style=\" padding: 3px 6px 3px 6px;\">审核</el-button>\n<el-button type=\"primary\" size=\"mini\" @click=\"handleShow(scope.$index, scope.row)\" icon=\"el-icon-zoom-in\" style=\" padding: 3px 6px 3px 6px;\">详情</el-button>\n<el-button type=\"danger\" size=\"mini\" @click=\"handleDelete(scope.$index, scope.row)\" icon=\"el-icon-delete\" style=\" padding: 3px 6px 3px 6px;\">删除</el-button>\n</template>\n</el-table-column>\n</el-table>\n<el-pagination  @current-change=\"handleCurrentChange\" :current-page=\"page.currentPage\" :page-size=\"page.pageSize\"\n background layout=\"total, prev, pager, next, jumper\" :total=\"page.totalCount\"\n style=\"float: right; margin: 10px 20px 0 0\"></el-pagination>\n\n<!-- 审核对话框 -->\n<el-dialog title=\"审核宿舍更换申请\" :visible.sync=\"reviewVisible\" width=\"600px\" @close=\"resetReviewForm\">\n  <el-form :model=\"reviewForm\" :rules=\"reviewRules\" ref=\"reviewFormRef\" label-width=\"120px\">\n    <el-form-item label=\"学号\">\n      <el-input v-model=\"reviewForm.sno\" disabled></el-input>\n    </el-form-item>\n    <el-form-item label=\"原宿舍\">\n      <el-input :value=\"reviewForm.dbname + ' - ' + reviewForm.doro\" disabled></el-input>\n    </el-form-item>\n    <el-form-item label=\"目标宿舍\">\n      <el-input :value=\"reviewForm.dbname2 + ' - ' + reviewForm.doro2\" disabled></el-input>\n    </el-form-item>\n    <el-form-item label=\"申请原因\">\n      <el-input type=\"textarea\" v-model=\"reviewForm.applicationreason\" :rows=\"3\" disabled></el-input>\n    </el-form-item>\n    <el-form-item label=\"审核结果\" prop=\"reviewstatus\">\n      <el-radio-group v-model=\"reviewForm.reviewstatus\">\n        <el-radio label=\"审核通过\">审核通过</el-radio>\n        <el-radio label=\"审核不通过\">审核不通过</el-radio>\n      </el-radio-group>\n    </el-form-item>\n    <el-form-item label=\"审核回复\" prop=\"reviewresponse\">\n      <el-input type=\"textarea\" v-model=\"reviewForm.reviewresponse\" placeholder=\"请输入审核回复\" :rows=\"4\"></el-input>\n    </el-form-item>\n  </el-form>\n  <div slot=\"footer\" class=\"dialog-footer\">\n    <el-button @click=\"reviewVisible = false\">取消</el-button>\n    <el-button type=\"primary\" @click=\"submitReview\" :loading=\"btnLoading\">确定</el-button>\n  </div>\n</el-dialog>\n\n    </div>\n</template>\n\n<script>\nimport request, { base } from \"../../../../utils/http\";\nexport default {\n  name: 'dormitorychange',\n  components: {\n    \n  },  \n    data() {\n      return {\n               filters: {\n          //列表查询参数\n          sno: '',\n          reviewstatus: '',\n        },\n\n        page: {\n          currentPage: 1, // 当前页\n          pageSize: 10, // 每页显示条目个数\n          totalCount: 0, // 总条目数\n        },\n        isClear: false,\n\n        listLoading: false, //列表加载状态\n        btnLoading: false, //保存按钮加载状态\n        datalist: [], //表格数据\n\n        // 审核相关\n        reviewVisible: false, // 审核对话框显示状态\n        reviewForm: {\n          id: null,\n          sno: '',\n          dbname: '',\n          doro: '',\n          dbname2: '',\n          doro2: '',\n          applicationreason: '',\n          reviewstatus: '',\n          reviewresponse: ''\n        },\n        reviewRules: {\n          reviewstatus: [{ required: true, message: '请选择审核结果', trigger: 'change' }],\n          reviewresponse: [{ required: true, message: '请输入审核回复', trigger: 'blur' }]\n        }\n\n      };\n    },\n    created() {\n      this.getDatas();\n    },\n\n \n    methods: {    \n\n              \n       // 删除宿舍更换\n        handleDelete(index, row) {\n          this.$confirm(\"确认删除该记录吗?\", \"提示\", {\n            confirmButtonText: \"确定\",\n            cancelButtonText: \"取消\",\n            type: \"warning\",\n          })\n            .then(() => {\n              this.listLoading = true;\n              let url = base + \"/dormitorychange/del?id=\" + row.id;\n              request.post(url).then((res) => {\n                this.listLoading = false;\n             \n                this.$message({\n                  message: \"删除成功\",\n                  type: \"success\",\n                  offset: 320,\n                });\n                this.getDatas();\n              });\n            })\n            .catch(() => { });\n        },\n                \n        // 分页\n        handleCurrentChange(val) {\n          this.page.currentPage = val;\n          this.getDatas();\n        },     \n     \n        //获取列表数据\n        getDatas() {\n          let para = {\n               sno:this.filters.sno,\n               reviewstatus:this.filters.reviewstatus,\n\n          };\n          this.listLoading = true;\n          let url = base + \"/dormitorychange/list?currentPage=\" + this.page.currentPage+ \"&pageSize=\" + this.page.pageSize;\n          request.post(url, para).then((res) => {\n            if (res.resdata.length > 0) {\n              this.isPage = true;\n            } else {\n              this.isPage = false;\n            }\n            this.page.totalCount = res.count;\n            this.datalist = res.resdata;\n            this.listLoading = false;\n          });\n        },\n                 //查询\n        query() {\n          this.getDatas();\n        },  \n           \n        // 查看\n        handleShow(index, row) {\n          this.$router.push({\n            path: \"/DormitorychangeDetail\",\n             query: {\n                id: row.id,\n              },\n          });\n        },\n    \n        // 编辑\n        handleEdit(index, row) {\n          this.$router.push({\n            path: \"/DormitorychangeEdit\",\n             query: {\n                id: row.id,\n              },\n          });\n        },\n\n        // 审核\n        handleReview(index, row) {\n          // 获取详细信息\n          let url = base + \"/dormitorychange/get?id=\" + row.id;\n          request.post(url).then((res) => {\n            if (res.code == 200) {\n              this.reviewForm = {\n                id: res.resdata.id,\n                sno: res.resdata.sno,\n                dbname: res.resdata.dbname || '宿舍楼' + res.resdata.dbid,\n                doro: res.resdata.doro,\n                dbname2: res.resdata.dbname2 || '宿舍楼' + res.resdata.dbid2,\n                doro2: res.resdata.doro2,\n                applicationreason: res.resdata.applicationreason,\n                reviewstatus: '',\n                reviewresponse: ''\n              };\n              this.reviewVisible = true;\n            }\n          });\n        },\n\n        // 提交审核\n        submitReview() {\n          this.$refs.reviewFormRef.validate((valid) => {\n            if (valid) {\n              this.btnLoading = true;\n              let url = base + \"/dormitorychange/review\";\n              let para = {\n                id: this.reviewForm.id,\n                reviewstatus: this.reviewForm.reviewstatus,\n                reviewresponse: this.reviewForm.reviewresponse\n              };\n              request.post(url, para).then((res) => {\n                if (res.code == 200) {\n                  this.$message({\n                    message: \"审核成功\",\n                    type: \"success\"\n                  });\n                  this.reviewVisible = false;\n                  this.getDatas(); // 刷新列表\n                } else {\n                  this.$message({\n                    message: res.msg || \"审核失败\",\n                    type: \"error\"\n                  });\n                }\n                this.btnLoading = false;\n              }).catch(() => {\n                this.$message({\n                  message: \"审核失败\",\n                  type: \"error\"\n                });\n                this.btnLoading = false;\n              });\n            }\n          });\n        },\n\n        // 重置审核表单\n        resetReviewForm() {\n          if (this.$refs.reviewFormRef) {\n            this.$refs.reviewFormRef.resetFields();\n          }\n          this.reviewForm = {\n            id: null,\n            sno: '',\n            dbname: '',\n            doro: '',\n            dbname2: '',\n            doro2: '',\n            applicationreason: '',\n            reviewstatus: '',\n            reviewresponse: ''\n          };\n        },\n\n        // 获取状态标签类型\n        getStatusType(status) {\n          switch (status) {\n            case '待审核':\n              return 'warning';\n            case '审核通过':\n              return 'success';\n            case '审核不通过':\n              return 'danger';\n            default:\n              return 'info';\n          }\n        }\n      },\n}\n\n</script>\n<style scoped>\n</style>\n \n\n"]}]}