{"version": 3, "names": ["_t", "require", "isCallExpression", "isExpressionStatement", "isIdentifier", "isStringLiteral", "getImportSource", "node", "specifiers", "length", "source", "value", "getRequireSource", "expression", "callee", "name", "arguments", "isPolyfillSource"], "sources": ["../../src/polyfills/utils.ts"], "sourcesContent": ["import {\n  isCallExpression,\n  isExpressionStatement,\n  isIdentifier,\n  isStringLiteral,\n} from \"@babel/types\";\nimport type * as t from \"@babel/types\";\nimport type { NodePath } from \"@babel/traverse\";\n\nexport function getImportSource({ node }: NodePath<t.ImportDeclaration>) {\n  if (node.specifiers.length === 0) return node.source.value;\n}\n\nexport function getRequireSource({ node }: NodePath) {\n  if (!isExpressionStatement(node)) return;\n  const { expression } = node;\n  if (\n    isCallExpression(expression) &&\n    isIdentifier(expression.callee) &&\n    expression.callee.name === \"require\" &&\n    expression.arguments.length === 1 &&\n    isStringLiteral(expression.arguments[0])\n  ) {\n    return expression.arguments[0].value;\n  }\n}\n\nexport function isPolyfillSource(source?: string | null): boolean {\n  return source === \"@babel/polyfill\" || source === \"core-js\";\n}\n"], "mappings": ";;;;;;;;AAAA,IAAAA,EAAA,GAAAC,OAAA;AAKsB;EAJpBC,gBAAgB;EAChBC,qBAAqB;EACrBC,YAAY;EACZC;AAAe,IAAAL,EAAA;AAKV,SAASM,eAAeA,CAAC;EAAEC;AAAoC,CAAC,EAAE;EACvE,IAAIA,IAAI,CAACC,UAAU,CAACC,MAAM,KAAK,CAAC,EAAE,OAAOF,IAAI,CAACG,MAAM,CAACC,KAAK;AAC5D;AAEO,SAASC,gBAAgBA,CAAC;EAAEL;AAAe,CAAC,EAAE;EACnD,IAAI,CAACJ,qBAAqB,CAACI,IAAI,CAAC,EAAE;EAClC,MAAM;IAAEM;EAAW,CAAC,GAAGN,IAAI;EAC3B,IACEL,gBAAgB,CAACW,UAAU,CAAC,IAC5BT,YAAY,CAACS,UAAU,CAACC,MAAM,CAAC,IAC/BD,UAAU,CAACC,MAAM,CAACC,IAAI,KAAK,SAAS,IACpCF,UAAU,CAACG,SAAS,CAACP,MAAM,KAAK,CAAC,IACjCJ,eAAe,CAACQ,UAAU,CAACG,SAAS,CAAC,CAAC,CAAC,CAAC,EACxC;IACA,OAAOH,UAAU,CAACG,SAAS,CAAC,CAAC,CAAC,CAACL,KAAK;EACtC;AACF;AAEO,SAASM,gBAAgBA,CAACP,MAAsB,EAAW;EAChE,OAAOA,MAAM,KAAK,iBAAiB,IAAIA,MAAM,KAAK,SAAS;AAC7D"}