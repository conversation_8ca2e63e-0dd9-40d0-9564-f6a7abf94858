﻿<template>
    <div style="width: 100%;line-height: 30px;text-align: left;">
       <el-form :model="formData" label-width="20%" ref="formDataRef" :rules="addrules"  align="left">
<el-form-item label="学号" prop="sno">
<el-input v-model="formData.sno" placeholder="学号"  style="width:50%;" ></el-input>
</el-form-item>
<el-form-item label="登录密码" prop="password">
<el-input v-model="formData.password" placeholder="登录密码"  style="width:50%;" ></el-input>
</el-form-item>
<el-form-item label="姓名" prop="stname">
<el-input v-model="formData.stname" placeholder="姓名"  style="width:50%;" ></el-input>
</el-form-item>
<el-form-item label="性别" prop="sex">
<el-radio-group v-model="formData.sex" @change="onGenderChange">
<el-radio label="男">
男
</el-radio>
<el-radio label="女">
女
</el-radio>
</el-radio-group>
</el-form-item>
<el-form-item label="手机号码" prop="phone">
<el-input v-model="formData.phone" placeholder="手机号码"  style="width:50%;" ></el-input>
</el-form-item>
<el-form-item label="宿舍楼" prop="dbid">
<el-select v-model="formData.dbid" placeholder="请选择"  size="small" @change="onBuildingChange">
<el-option v-for="item in dormbuildingList" :key="item.dbid" :label="item.dbname" :value="item.dbid"></el-option>
</el-select>
</el-form-item>
<el-form-item label="宿舍" prop="doro">
<el-select v-model="formData.doro" placeholder="请先选择宿舍楼和性别"  size="small" :disabled="!formData.dbid || !formData.sex">
<el-option v-for="item in filteredDormitoryList" :key="item.doro" :label="item.doro" :value="item.doro"></el-option>
</el-select>
</el-form-item>
<el-form-item label="专业" prop="specialty">
<el-input v-model="formData.specialty" placeholder="专业"  style="width:50%;" ></el-input>
</el-form-item>
<el-form-item label="班级" prop="clsname">
<el-input v-model="formData.clsname" placeholder="班级"  style="width:50%;" ></el-input>
</el-form-item>
<el-form-item>
<el-button type="primary" size="small" @click="save" :loading="btnLoading" icon="el-icon-upload">提 交</el-button>
<el-button type="info" size="small" @click="goBack" icon="el-icon-back">返 回</el-button>
</el-form-item>
</el-form>


    </div>
</template>

<script>
import request, { base } from "../../../../utils/http";

export default {
  name: 'StudentAdd',
  components: {
    
  },  
    data() {
      return {
        uploadVisible: false,
        btnLoading: false, //保存按钮加载状态
        formData: {}, //表单数据
        dormbuildingList: [], //宿舍楼列表
        dormitoryList: [], //所有宿舍列表
        filteredDormitoryList: [], //过滤后的宿舍列表
        addrules: {
          sno: [{ required: true, message: '请输入学号', trigger: 'blur' },
],          password: [{ required: true, message: '请输入登录密码', trigger: 'blur' },
],          stname: [{ required: true, message: '请输入姓名', trigger: 'blur' },
],          sex: [{ required: true, message: '请输入性别', trigger: 'blur' },
],          phone: [{ required: true, message: '请输入手机号码', trigger: 'blur' },
        { pattern: /^1[3456789]\d{9}$/, message: '手机号码格式不正确', trigger: 'blur' },
],          doro: [{ required: true, message: '请选择宿舍', trigger: 'onchange' }],
          specialty: [{ required: true, message: '请输入专业', trigger: 'blur' },
],          clsname: [{ required: true, message: '请输入班级', trigger: 'blur' },
],        },

      };
    },
    mounted() {
    
      this.getdormbuildingList();
      this.getdormitoryList();
    },

 
    methods: {    
   // 添加
    save() {       
         this.$refs["formDataRef"].validate((valid) => { //验证表单
           if (valid) {
             let url = base + "/student/add";
             this.btnLoading = true;
             request.post(url, this.formData).then((res) => { //发送请求         
               if (res.code == 200) {
                 this.$message({
                   message: "操作成功",
                   type: "success",
                   offset: 320,
                 });              
                this.$router.push({
                path: "/StudentManage",
                });
               } else {
                 this.$message({
                   message: res.msg,
                   type: "error",
                   offset: 320,
                 });
               }
               this.btnLoading=false;
             });
           }        
           
         });
    },
    
       // 返回
        goBack() {
          this.$router.push({
            path: "/StudentManage",
          });
        },       
              
            
    getdormbuildingList() {
      let para = {};
      this.listLoading = true;
      let url = base + "/dormbuilding/list?currentPage=1&pageSize=1000";
      request.post(url, para).then((res) => {
        this.dormbuildingList = res.resdata;
      });
    },
    
    getdormitoryList() {
      let para = {};
      this.listLoading = true;
      let url = base + "/dormitory/list?currentPage=1&pageSize=1000";
      request.post(url, para).then((res) => {
        this.dormitoryList = res.resdata;
      });
    },

    // 宿舍楼变化时的处理
    onBuildingChange() {
      this.formData.doro = ''; // 清空宿舍选择
      this.updateDormitoryList();
    },

    // 性别变化时的处理
    onGenderChange() {
      this.formData.doro = ''; // 清空宿舍选择
      this.updateDormitoryList();
    },

    // 更新宿舍列表
    updateDormitoryList() {
      if (!this.formData.dbid || !this.formData.sex) {
        this.filteredDormitoryList = [];
        return;
      }

      let para = {
        dbid: this.formData.dbid,
        dorgender: this.formData.sex
      };
      let url = base + "/dormitory/listByBuildingAndGender";
      request.post(url, para).then((res) => {
        if (res.code == 200) {
          this.filteredDormitoryList = res.resdata;
        } else {
          this.filteredDormitoryList = [];
          this.$message({
            message: res.msg || "获取宿舍列表失败",
            type: "error",
            offset: 320,
          });
        }
      }).catch(() => {
        this.filteredDormitoryList = [];
        this.$message({
          message: "获取宿舍列表失败",
          type: "error",
          offset: 320,
        });
      });
    },
  
           
           
      },
}

</script>
<style scoped>
</style>
 

