{"version": 3, "names": ["_iterableToArrayLimit", "arr", "i", "_i", "Symbol", "iterator", "_arr", "_n", "_d", "_s", "_e", "_x", "_r", "call", "next", "Object", "done", "push", "value", "length", "err"], "sources": ["../../src/helpers/iterableToArrayLimit.js"], "sourcesContent": ["/* @minVersion 7.0.0-beta.0 */\n\nexport default function _iterableToArrayLimit(arr, i) {\n  // this is an expanded form of \\`for...of\\` that properly supports abrupt completions of\n  // iterators etc. variable names have been minimised to reduce the size of this massive\n  // helper. sometimes spec compliance is annoying :(\n  //\n  // _n = _iteratorNormalCompletion\n  // _d = _didIteratorError\n  // _e = _iteratorError\n  // _i = _iterator\n  // _s = _step\n  // _x = _next\n  // _r = _return\n\n  var _i =\n    arr == null\n      ? null\n      : (typeof Symbol !== \"undefined\" && arr[Symbol.iterator]) ||\n        arr[\"@@iterator\"];\n  if (_i == null) return;\n\n  var _arr = [];\n  var _n = true;\n  var _d = false;\n  var _s, _e, _x, _r;\n  try {\n    _x = (_i = _i.call(arr)).next;\n    if (i === 0) {\n      if (Object(_i) !== _i) return;\n      _n = false;\n    } else {\n      for (; !(_n = (_s = _x.call(_i)).done); _n = true) {\n        _arr.push(_s.value);\n        if (_arr.length === i) break;\n      }\n    }\n  } catch (err) {\n    _d = true;\n    _e = err;\n  } finally {\n    try {\n      if (!_n && _i[\"return\"] != null) {\n        _r = _i[\"return\"]();\n        // eslint-disable-next-line no-unsafe-finally\n        if (Object(_r) !== _r) return;\n      }\n    } finally {\n      // eslint-disable-next-line no-unsafe-finally\n      if (_d) throw _e;\n    }\n  }\n  return _arr;\n}\n"], "mappings": ";;;;;;AAEe,SAASA,qBAAqBA,CAACC,GAAG,EAAEC,CAAC,EAAE;EAapD,IAAIC,EAAE,GACJF,GAAG,IAAI,IAAI,GACP,IAAI,GACH,OAAOG,MAAM,KAAK,WAAW,IAAIH,GAAG,CAACG,MAAM,CAACC,QAAQ,CAAC,IACtDJ,GAAG,CAAC,YAAY,CAAC;EACvB,IAAIE,EAAE,IAAI,IAAI,EAAE;EAEhB,IAAIG,IAAI,GAAG,EAAE;EACb,IAAIC,EAAE,GAAG,IAAI;EACb,IAAIC,EAAE,GAAG,KAAK;EACd,IAAIC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;EAClB,IAAI;IACFD,EAAE,GAAG,CAACR,EAAE,GAAGA,EAAE,CAACU,IAAI,CAACZ,GAAG,CAAC,EAAEa,IAAI;IAC7B,IAAIZ,CAAC,KAAK,CAAC,EAAE;MACX,IAAIa,MAAM,CAACZ,EAAE,CAAC,KAAKA,EAAE,EAAE;MACvBI,EAAE,GAAG,KAAK;IACZ,CAAC,MAAM;MACL,OAAO,EAAEA,EAAE,GAAG,CAACE,EAAE,GAAGE,EAAE,CAACE,IAAI,CAACV,EAAE,CAAC,EAAEa,IAAI,CAAC,EAAET,EAAE,GAAG,IAAI,EAAE;QACjDD,IAAI,CAACW,IAAI,CAACR,EAAE,CAACS,KAAK,CAAC;QACnB,IAAIZ,IAAI,CAACa,MAAM,KAAKjB,CAAC,EAAE;MACzB;IACF;EACF,CAAC,CAAC,OAAOkB,GAAG,EAAE;IACZZ,EAAE,GAAG,IAAI;IACTE,EAAE,GAAGU,GAAG;EACV,CAAC,SAAS;IACR,IAAI;MACF,IAAI,CAACb,EAAE,IAAIJ,EAAE,CAAC,QAAQ,CAAC,IAAI,IAAI,EAAE;QAC/BS,EAAE,GAAGT,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;QAEnB,IAAIY,MAAM,CAACH,EAAE,CAAC,KAAKA,EAAE,EAAE;MACzB;IACF,CAAC,SAAS;MAER,IAAIJ,EAAE,EAAE,MAAME,EAAE;IAClB;EACF;EACA,OAAOJ,IAAI;AACb"}