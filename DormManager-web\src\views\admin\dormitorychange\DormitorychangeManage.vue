﻿<template>
    <div style="width: 100%;line-height: 30px;text-align: left;">
      <el-col  :span="24"  style="padding-bottom: 0px; margin-left: 10px">
<el-form :inline="true" :model="filters" >
<el-form-item>
<el-input v-model="filters.sno" placeholder="学号"  size="small"></el-input>
</el-form-item>
<el-form-item>
<el-select v-model="filters.reviewstatus" placeholder="审核状态" size="small" clearable>
<el-option label="待审核" value="待审核"></el-option>
<el-option label="审核通过" value="审核通过"></el-option>
<el-option label="审核不通过" value="审核不通过"></el-option>
</el-select>
</el-form-item>
<el-form-item>
<el-button type="primary" size="small" @click="query" icon="el-icon-search">搜索</el-button>
</el-form-item>
 </el-form>
</el-col>

<el-table :data="datalist" border stripe style="width: 100%"  v-loading="listLoading"   highlight-current-row   max-height="600"     size="small">
<el-table-column prop="sno" label="学号"  align="center"></el-table-column>
<el-table-column prop="dbid" label="原宿舍楼id"  align="center"></el-table-column>
<el-table-column prop="doro" label="原宿舍编号"  align="center"></el-table-column>
<el-table-column prop="dbid2" label="更换宿舍楼id"  align="center"></el-table-column>
<el-table-column prop="doro2" label="更换宿舍编号"  align="center"></el-table-column>
<el-table-column prop="applicationreason" label="申请原因"  align="center">
<template #default="scope">
<span v-if="scope.row.applicationreason != null">{{scope.row.applicationreason.substring(0,20)}}</span>
</template>
</el-table-column>
<el-table-column prop="submissiontime" label="提交时间"  align="center"></el-table-column>
<el-table-column prop="reviewstatus" label="审核状态"  align="center">
<template #default="scope">
<el-tag :type="getStatusType(scope.row.reviewstatus)">{{ scope.row.reviewstatus }}</el-tag>
</template>
</el-table-column>
<el-table-column prop="reviewresponse" label="审核回复"  align="center">
<template #default="scope">
<span v-if="scope.row.reviewresponse != null">{{scope.row.reviewresponse.substring(0,20)}}</span>
</template>
</el-table-column>
<el-table-column label="操作" min-width="250" align="center">
<template #default="scope">
<el-button v-if="scope.row.reviewstatus === '待审核'" type="success" size="mini" @click="handleReview(scope.$index, scope.row)" icon="el-icon-check" style=" padding: 3px 6px 3px 6px;">审核</el-button>
<el-button type="primary" size="mini" @click="handleShow(scope.$index, scope.row)" icon="el-icon-zoom-in" style=" padding: 3px 6px 3px 6px;">详情</el-button>
<el-button type="danger" size="mini" @click="handleDelete(scope.$index, scope.row)" icon="el-icon-delete" style=" padding: 3px 6px 3px 6px;">删除</el-button>
</template>
</el-table-column>
</el-table>
<el-pagination  @current-change="handleCurrentChange" :current-page="page.currentPage" :page-size="page.pageSize"
 background layout="total, prev, pager, next, jumper" :total="page.totalCount"
 style="float: right; margin: 10px 20px 0 0"></el-pagination>

<!-- 审核对话框 -->
<el-dialog title="审核宿舍更换申请" :visible.sync="reviewVisible" width="600px" @close="resetReviewForm">
  <el-form :model="reviewForm" :rules="reviewRules" ref="reviewFormRef" label-width="120px">
    <el-form-item label="学号">
      <el-input v-model="reviewForm.sno" disabled></el-input>
    </el-form-item>
    <el-form-item label="原宿舍">
      <el-input :value="reviewForm.dbname + ' - ' + reviewForm.doro" disabled></el-input>
    </el-form-item>
    <el-form-item label="目标宿舍">
      <el-input :value="reviewForm.dbname2 + ' - ' + reviewForm.doro2" disabled></el-input>
    </el-form-item>
    <el-form-item label="申请原因">
      <el-input type="textarea" v-model="reviewForm.applicationreason" :rows="3" disabled></el-input>
    </el-form-item>
    <el-form-item label="审核结果" prop="reviewstatus">
      <el-radio-group v-model="reviewForm.reviewstatus">
        <el-radio label="审核通过">审核通过</el-radio>
        <el-radio label="审核不通过">审核不通过</el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item label="审核回复" prop="reviewresponse">
      <el-input type="textarea" v-model="reviewForm.reviewresponse" placeholder="请输入审核回复" :rows="4"></el-input>
    </el-form-item>
  </el-form>
  <div slot="footer" class="dialog-footer">
    <el-button @click="reviewVisible = false">取消</el-button>
    <el-button type="primary" @click="submitReview" :loading="btnLoading">确定</el-button>
  </div>
</el-dialog>

    </div>
</template>

<script>
import request, { base } from "../../../../utils/http";
export default {
  name: 'dormitorychange',
  components: {
    
  },  
    data() {
      return {
               filters: {
          //列表查询参数
          sno: '',
          reviewstatus: '',
        },

        page: {
          currentPage: 1, // 当前页
          pageSize: 10, // 每页显示条目个数
          totalCount: 0, // 总条目数
        },
        isClear: false,

        listLoading: false, //列表加载状态
        btnLoading: false, //保存按钮加载状态
        datalist: [], //表格数据

        // 审核相关
        reviewVisible: false, // 审核对话框显示状态
        reviewForm: {
          id: null,
          sno: '',
          dbname: '',
          doro: '',
          dbname2: '',
          doro2: '',
          applicationreason: '',
          reviewstatus: '',
          reviewresponse: ''
        },
        reviewRules: {
          reviewstatus: [{ required: true, message: '请选择审核结果', trigger: 'change' }],
          reviewresponse: [{ required: true, message: '请输入审核回复', trigger: 'blur' }]
        }

      };
    },
    created() {
      this.getDatas();
    },

 
    methods: {    

              
       // 删除宿舍更换
        handleDelete(index, row) {
          this.$confirm("确认删除该记录吗?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          })
            .then(() => {
              this.listLoading = true;
              let url = base + "/dormitorychange/del?id=" + row.id;
              request.post(url).then((res) => {
                this.listLoading = false;
             
                this.$message({
                  message: "删除成功",
                  type: "success",
                  offset: 320,
                });
                this.getDatas();
              });
            })
            .catch(() => { });
        },
                
        // 分页
        handleCurrentChange(val) {
          this.page.currentPage = val;
          this.getDatas();
        },     
     
        //获取列表数据
        getDatas() {
          let para = {
               sno:this.filters.sno,
               reviewstatus:this.filters.reviewstatus,

          };
          this.listLoading = true;
          let url = base + "/dormitorychange/list?currentPage=" + this.page.currentPage+ "&pageSize=" + this.page.pageSize;
          request.post(url, para).then((res) => {
            if (res.resdata.length > 0) {
              this.isPage = true;
            } else {
              this.isPage = false;
            }
            this.page.totalCount = res.count;
            this.datalist = res.resdata;
            this.listLoading = false;
          });
        },
                 //查询
        query() {
          this.getDatas();
        },  
           
        // 查看
        handleShow(index, row) {
          this.$router.push({
            path: "/DormitorychangeDetail",
             query: {
                id: row.id,
              },
          });
        },
    
        // 编辑
        handleEdit(index, row) {
          this.$router.push({
            path: "/DormitorychangeEdit",
             query: {
                id: row.id,
              },
          });
        },

        // 审核
        handleReview(index, row) {
          // 获取详细信息
          let url = base + "/dormitorychange/get?id=" + row.id;
          request.post(url).then((res) => {
            if (res.code == 200) {
              this.reviewForm = {
                id: res.resdata.id,
                sno: res.resdata.sno,
                dbname: res.resdata.dbname || '宿舍楼' + res.resdata.dbid,
                doro: res.resdata.doro,
                dbname2: res.resdata.dbname2 || '宿舍楼' + res.resdata.dbid2,
                doro2: res.resdata.doro2,
                applicationreason: res.resdata.applicationreason,
                reviewstatus: '',
                reviewresponse: ''
              };
              this.reviewVisible = true;
            }
          });
        },

        // 提交审核
        submitReview() {
          this.$refs.reviewFormRef.validate((valid) => {
            if (valid) {
              this.btnLoading = true;
              let url = base + "/dormitorychange/review";
              let para = {
                id: this.reviewForm.id,
                reviewstatus: this.reviewForm.reviewstatus,
                reviewresponse: this.reviewForm.reviewresponse
              };
              request.post(url, para).then((res) => {
                if (res.code == 200) {
                  this.$message({
                    message: "审核成功",
                    type: "success"
                  });
                  this.reviewVisible = false;
                  this.getDatas(); // 刷新列表
                } else {
                  this.$message({
                    message: res.msg || "审核失败",
                    type: "error"
                  });
                }
                this.btnLoading = false;
              }).catch(() => {
                this.$message({
                  message: "审核失败",
                  type: "error"
                });
                this.btnLoading = false;
              });
            }
          });
        },

        // 重置审核表单
        resetReviewForm() {
          if (this.$refs.reviewFormRef) {
            this.$refs.reviewFormRef.resetFields();
          }
          this.reviewForm = {
            id: null,
            sno: '',
            dbname: '',
            doro: '',
            dbname2: '',
            doro2: '',
            applicationreason: '',
            reviewstatus: '',
            reviewresponse: ''
          };
        },

        // 获取状态标签类型
        getStatusType(status) {
          switch (status) {
            case '待审核':
              return 'warning';
            case '审核通过':
              return 'success';
            case '审核不通过':
              return 'danger';
            default:
              return 'info';
          }
        }
      },
}

</script>
<style scoped>
</style>
 

