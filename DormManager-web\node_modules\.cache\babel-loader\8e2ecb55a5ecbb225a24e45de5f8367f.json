{"remainingRequest": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\admin\\dormitorychange\\DormitorychangeManage.vue?vue&type=template&id=23f57154", "dependencies": [{"path": "I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\admin\\dormitorychange\\DormitorychangeManage.vue", "mtime": 1749046404836}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\babel.config.js", "mtime": 1749034452000}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749043011899}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749043012919}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749043013784}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749043011899}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749043013130}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["style", "slot", "class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_col", "span", "_component_el_form", "inline", "model", "$data", "filters", "_component_el_form_item", "_component_el_input", "sno", "$event", "placeholder", "size", "_component_el_select", "reviewstatus", "clearable", "_component_el_option", "label", "value", "_component_el_button", "type", "onClick", "$options", "query", "icon", "_component_el_table", "data", "datalist", "border", "stripe", "_component_el_table_column", "prop", "align", "default", "_withCtx", "scope", "row", "<PERSON><PERSON><PERSON>", "_hoisted_3", "_toDisplayString", "substring", "_component_el_tag", "getStatusType", "reviewresponse", "_hoisted_4", "_createBlock", "handleReview", "$index", "handleShow", "handleDelete", "listLoading", "_component_el_pagination", "onCurrentChange", "handleCurrentChange", "page", "currentPage", "pageSize", "background", "layout", "total", "totalCount", "_createCommentVNode", "_component_el_dialog", "title", "visible", "reviewVisible", "width", "onClose", "resetReviewForm", "reviewForm", "rules", "reviewRules", "ref", "disabled", "dbname", "doro", "dbname2", "doro2", "rows", "_component_el_radio_group", "_component_el_radio", "_createElementVNode", "_hoisted_10", "_cache", "submitReview", "loading", "btnLoading"], "sources": ["I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\admin\\dormitorychange\\DormitorychangeManage.vue"], "sourcesContent": ["<template>\n    <div style=\"width: 100%;line-height: 30px;text-align: left;\">\n      <el-col  :span=\"24\"  style=\"padding-bottom: 0px; margin-left: 10px\">\n<el-form :inline=\"true\" :model=\"filters\" >\n<el-form-item>\n<el-input v-model=\"filters.sno\" placeholder=\"学号\"  size=\"small\"></el-input>\n</el-form-item>\n<el-form-item>\n<el-select v-model=\"filters.reviewstatus\" placeholder=\"审核状态\" size=\"small\" clearable>\n<el-option label=\"待审核\" value=\"待审核\"></el-option>\n<el-option label=\"审核通过\" value=\"审核通过\"></el-option>\n<el-option label=\"审核不通过\" value=\"审核不通过\"></el-option>\n</el-select>\n</el-form-item>\n<el-form-item>\n<el-button type=\"primary\" size=\"small\" @click=\"query\" icon=\"el-icon-search\">搜索</el-button>\n</el-form-item>\n </el-form>\n</el-col>\n\n<el-table :data=\"datalist\" border stripe style=\"width: 100%\"  v-loading=\"listLoading\"   highlight-current-row   max-height=\"600\"     size=\"small\">\n<el-table-column prop=\"sno\" label=\"学号\"  align=\"center\"></el-table-column>\n<el-table-column prop=\"dbid\" label=\"原宿舍楼id\"  align=\"center\"></el-table-column>\n<el-table-column prop=\"doro\" label=\"原宿舍编号\"  align=\"center\"></el-table-column>\n<el-table-column prop=\"dbid2\" label=\"更换宿舍楼id\"  align=\"center\"></el-table-column>\n<el-table-column prop=\"doro2\" label=\"更换宿舍编号\"  align=\"center\"></el-table-column>\n<el-table-column prop=\"applicationreason\" label=\"申请原因\"  align=\"center\">\n<template #default=\"scope\">\n<span v-if=\"scope.row.applicationreason != null\">{{scope.row.applicationreason.substring(0,20)}}</span>\n</template>\n</el-table-column>\n<el-table-column prop=\"submissiontime\" label=\"提交时间\"  align=\"center\"></el-table-column>\n<el-table-column prop=\"reviewstatus\" label=\"审核状态\"  align=\"center\">\n<template #default=\"scope\">\n<el-tag :type=\"getStatusType(scope.row.reviewstatus)\">{{ scope.row.reviewstatus }}</el-tag>\n</template>\n</el-table-column>\n<el-table-column prop=\"reviewresponse\" label=\"审核回复\"  align=\"center\">\n<template #default=\"scope\">\n<span v-if=\"scope.row.reviewresponse != null\">{{scope.row.reviewresponse.substring(0,20)}}</span>\n</template>\n</el-table-column>\n<el-table-column label=\"操作\" min-width=\"250\" align=\"center\">\n<template #default=\"scope\">\n<el-button v-if=\"scope.row.reviewstatus === '待审核'\" type=\"success\" size=\"mini\" @click=\"handleReview(scope.$index, scope.row)\" icon=\"el-icon-check\" style=\" padding: 3px 6px 3px 6px;\">审核</el-button>\n<el-button type=\"primary\" size=\"mini\" @click=\"handleShow(scope.$index, scope.row)\" icon=\"el-icon-zoom-in\" style=\" padding: 3px 6px 3px 6px;\">详情</el-button>\n<el-button type=\"danger\" size=\"mini\" @click=\"handleDelete(scope.$index, scope.row)\" icon=\"el-icon-delete\" style=\" padding: 3px 6px 3px 6px;\">删除</el-button>\n</template>\n</el-table-column>\n</el-table>\n<el-pagination  @current-change=\"handleCurrentChange\" :current-page=\"page.currentPage\" :page-size=\"page.pageSize\"\n background layout=\"total, prev, pager, next, jumper\" :total=\"page.totalCount\"\n style=\"float: right; margin: 10px 20px 0 0\"></el-pagination>\n\n<!-- 审核对话框 -->\n<el-dialog title=\"审核宿舍更换申请\" :visible.sync=\"reviewVisible\" width=\"600px\" @close=\"resetReviewForm\">\n  <el-form :model=\"reviewForm\" :rules=\"reviewRules\" ref=\"reviewFormRef\" label-width=\"120px\">\n    <el-form-item label=\"学号\">\n      <el-input v-model=\"reviewForm.sno\" disabled></el-input>\n    </el-form-item>\n    <el-form-item label=\"原宿舍\">\n      <el-input :value=\"reviewForm.dbname + ' - ' + reviewForm.doro\" disabled></el-input>\n    </el-form-item>\n    <el-form-item label=\"目标宿舍\">\n      <el-input :value=\"reviewForm.dbname2 + ' - ' + reviewForm.doro2\" disabled></el-input>\n    </el-form-item>\n    <el-form-item label=\"申请原因\">\n      <el-input type=\"textarea\" v-model=\"reviewForm.applicationreason\" :rows=\"3\" disabled></el-input>\n    </el-form-item>\n    <el-form-item label=\"审核结果\" prop=\"reviewstatus\">\n      <el-radio-group v-model=\"reviewForm.reviewstatus\">\n        <el-radio label=\"审核通过\">审核通过</el-radio>\n        <el-radio label=\"审核不通过\">审核不通过</el-radio>\n      </el-radio-group>\n    </el-form-item>\n    <el-form-item label=\"审核回复\" prop=\"reviewresponse\">\n      <el-input type=\"textarea\" v-model=\"reviewForm.reviewresponse\" placeholder=\"请输入审核回复\" :rows=\"4\"></el-input>\n    </el-form-item>\n  </el-form>\n  <div slot=\"footer\" class=\"dialog-footer\">\n    <el-button @click=\"reviewVisible = false\">取消</el-button>\n    <el-button type=\"primary\" @click=\"submitReview\" :loading=\"btnLoading\">确定</el-button>\n  </div>\n</el-dialog>\n\n    </div>\n</template>\n\n<script>\nimport request, { base } from \"../../../../utils/http\";\nexport default {\n  name: 'dormitorychange',\n  components: {\n    \n  },  \n    data() {\n      return {\n               filters: {\n          //列表查询参数\n          sno: '',\n          reviewstatus: '',\n        },\n\n        page: {\n          currentPage: 1, // 当前页\n          pageSize: 10, // 每页显示条目个数\n          totalCount: 0, // 总条目数\n        },\n        isClear: false,\n\n        listLoading: false, //列表加载状态\n        btnLoading: false, //保存按钮加载状态\n        datalist: [], //表格数据\n\n        // 审核相关\n        reviewVisible: false, // 审核对话框显示状态\n        reviewForm: {\n          id: null,\n          sno: '',\n          dbname: '',\n          doro: '',\n          dbname2: '',\n          doro2: '',\n          applicationreason: '',\n          reviewstatus: '',\n          reviewresponse: ''\n        },\n        reviewRules: {\n          reviewstatus: [{ required: true, message: '请选择审核结果', trigger: 'change' }],\n          reviewresponse: [{ required: true, message: '请输入审核回复', trigger: 'blur' }]\n        }\n\n      };\n    },\n    created() {\n      this.getDatas();\n    },\n\n \n    methods: {    \n\n              \n       // 删除宿舍更换\n        handleDelete(index, row) {\n          this.$confirm(\"确认删除该记录吗?\", \"提示\", {\n            confirmButtonText: \"确定\",\n            cancelButtonText: \"取消\",\n            type: \"warning\",\n          })\n            .then(() => {\n              this.listLoading = true;\n              let url = base + \"/dormitorychange/del?id=\" + row.id;\n              request.post(url).then((res) => {\n                this.listLoading = false;\n             \n                this.$message({\n                  message: \"删除成功\",\n                  type: \"success\",\n                  offset: 320,\n                });\n                this.getDatas();\n              });\n            })\n            .catch(() => { });\n        },\n                \n        // 分页\n        handleCurrentChange(val) {\n          this.page.currentPage = val;\n          this.getDatas();\n        },     \n     \n        //获取列表数据\n        getDatas() {\n          let para = {\n               sno:this.filters.sno,\n               reviewstatus:this.filters.reviewstatus,\n\n          };\n          this.listLoading = true;\n          let url = base + \"/dormitorychange/list?currentPage=\" + this.page.currentPage+ \"&pageSize=\" + this.page.pageSize;\n          request.post(url, para).then((res) => {\n            if (res.resdata.length > 0) {\n              this.isPage = true;\n            } else {\n              this.isPage = false;\n            }\n            this.page.totalCount = res.count;\n            this.datalist = res.resdata;\n            this.listLoading = false;\n          });\n        },\n                 //查询\n        query() {\n          this.getDatas();\n        },  \n           \n        // 查看\n        handleShow(index, row) {\n          this.$router.push({\n            path: \"/DormitorychangeDetail\",\n             query: {\n                id: row.id,\n              },\n          });\n        },\n    \n        // 编辑\n        handleEdit(index, row) {\n          this.$router.push({\n            path: \"/DormitorychangeEdit\",\n             query: {\n                id: row.id,\n              },\n          });\n        },\n\n        // 审核\n        handleReview(index, row) {\n          // 获取详细信息\n          let url = base + \"/dormitorychange/get?id=\" + row.id;\n          request.post(url).then((res) => {\n            if (res.code == 200) {\n              this.reviewForm = {\n                id: res.resdata.id,\n                sno: res.resdata.sno,\n                dbname: res.resdata.dbname || '宿舍楼' + res.resdata.dbid,\n                doro: res.resdata.doro,\n                dbname2: res.resdata.dbname2 || '宿舍楼' + res.resdata.dbid2,\n                doro2: res.resdata.doro2,\n                applicationreason: res.resdata.applicationreason,\n                reviewstatus: '',\n                reviewresponse: ''\n              };\n              this.reviewVisible = true;\n            }\n          });\n        },\n\n        // 提交审核\n        submitReview() {\n          this.$refs.reviewFormRef.validate((valid) => {\n            if (valid) {\n              this.btnLoading = true;\n              let url = base + \"/dormitorychange/review\";\n              let para = {\n                id: this.reviewForm.id,\n                reviewstatus: this.reviewForm.reviewstatus,\n                reviewresponse: this.reviewForm.reviewresponse\n              };\n              request.post(url, para).then((res) => {\n                if (res.code == 200) {\n                  this.$message({\n                    message: \"审核成功\",\n                    type: \"success\"\n                  });\n                  this.reviewVisible = false;\n                  this.getDatas(); // 刷新列表\n                } else {\n                  this.$message({\n                    message: res.msg || \"审核失败\",\n                    type: \"error\"\n                  });\n                }\n                this.btnLoading = false;\n              }).catch(() => {\n                this.$message({\n                  message: \"审核失败\",\n                  type: \"error\"\n                });\n                this.btnLoading = false;\n              });\n            }\n          });\n        },\n\n        // 重置审核表单\n        resetReviewForm() {\n          if (this.$refs.reviewFormRef) {\n            this.$refs.reviewFormRef.resetFields();\n          }\n          this.reviewForm = {\n            id: null,\n            sno: '',\n            dbname: '',\n            doro: '',\n            dbname2: '',\n            doro2: '',\n            applicationreason: '',\n            reviewstatus: '',\n            reviewresponse: ''\n          };\n        },\n\n        // 获取状态标签类型\n        getStatusType(status) {\n          switch (status) {\n            case '待审核':\n              return 'warning';\n            case '审核通过':\n              return 'success';\n            case '审核不通过':\n              return 'danger';\n            default:\n              return 'info';\n          }\n        }\n      },\n}\n\n</script>\n<style scoped>\n</style>\n \n\n"], "mappings": ";;EACSA,KAAuD,EAAvD;IAAA;IAAA;IAAA;EAAA;AAAuD;iDAcY,IAAE;;;;;;;iDA6BuG,IAAE;iDAC1C,IAAE;iDACF,IAAE;iDAyBhH,MAAI;iDACH,OAAK;;EAO9BC,IAAI,EAAC,QAAQ;EAACC,KAAK,EAAC;;kDACmB,IAAE;kDAC0B,IAAE;;;;;;;;;;;;;;;;;uBAhFxEC,mBAAA,CAoFM,OApFNC,UAoFM,GAnFJC,YAAA,CAgBGC,iBAAA;IAhBOC,IAAI,EAAE,EAAE;IAAGP,KAA8C,EAA9C;MAAA;MAAA;IAAA;;sBAC3B,MAcW,CAdXK,YAAA,CAcWG,kBAAA;MAdDC,MAAM,EAAE,IAAI;MAAGC,KAAK,EAAEC,KAAA,CAAAC;;wBAChC,MAEe,CAFfP,YAAA,CAEeQ,uBAAA;0BADf,MAA0E,CAA1ER,YAAA,CAA0ES,mBAAA;sBAAvDH,KAAA,CAAAC,OAAO,CAACG,GAAG;qEAAXJ,KAAA,CAAAC,OAAO,CAACG,GAAG,GAAAC,MAAA;UAAEC,WAAW,EAAC,IAAI;UAAEC,IAAI,EAAC;;;UAEvDb,YAAA,CAMeQ,uBAAA;0BALf,MAIY,CAJZR,YAAA,CAIYc,oBAAA;sBAJQR,KAAA,CAAAC,OAAO,CAACQ,YAAY;qEAApBT,KAAA,CAAAC,OAAO,CAACQ,YAAY,GAAAJ,MAAA;UAAEC,WAAW,EAAC,MAAM;UAACC,IAAI,EAAC,OAAO;UAACG,SAAS,EAAT;;4BAC1E,MAA+C,CAA/ChB,YAAA,CAA+CiB,oBAAA;YAApCC,KAAK,EAAC,KAAK;YAACC,KAAK,EAAC;cAC7BnB,YAAA,CAAiDiB,oBAAA;YAAtCC,KAAK,EAAC,MAAM;YAACC,KAAK,EAAC;cAC9BnB,YAAA,CAAmDiB,oBAAA;YAAxCC,KAAK,EAAC,OAAO;YAACC,KAAK,EAAC;;;;;UAG/BnB,YAAA,CAEeQ,uBAAA;0BADf,MAA0F,CAA1FR,YAAA,CAA0FoB,oBAAA;UAA/EC,IAAI,EAAC,SAAS;UAACR,IAAI,EAAC,OAAO;UAAES,OAAK,EAAEC,QAAA,CAAAC,KAAK;UAAEC,IAAI,EAAC;;4BAAiB,MAAE,C;;;;;;;;;sBAK9EzB,YAAA,CA6BW0B,mBAAA;IA7BAC,IAAI,EAAErB,KAAA,CAAAsB,QAAQ;IAAEC,MAAM,EAAN,EAAM;IAACC,MAAM,EAAN,EAAM;IAACnC,KAAmB,EAAnB;MAAA;IAAA,CAAmB;IAA4B,uBAAqB,EAArB,EAAqB;IAAG,YAAU,EAAC,KAAK;IAAKkB,IAAI,EAAC;;sBAC1I,MAAyE,CAAzEb,YAAA,CAAyE+B,0BAAA;MAAxDC,IAAI,EAAC,KAAK;MAACd,KAAK,EAAC,IAAI;MAAEe,KAAK,EAAC;QAC9CjC,YAAA,CAA8E+B,0BAAA;MAA7DC,IAAI,EAAC,MAAM;MAACd,KAAK,EAAC,QAAQ;MAAEe,KAAK,EAAC;QACnDjC,YAAA,CAA6E+B,0BAAA;MAA5DC,IAAI,EAAC,MAAM;MAACd,KAAK,EAAC,OAAO;MAAEe,KAAK,EAAC;QAClDjC,YAAA,CAAgF+B,0BAAA;MAA/DC,IAAI,EAAC,OAAO;MAACd,KAAK,EAAC,SAAS;MAAEe,KAAK,EAAC;QACrDjC,YAAA,CAA+E+B,0BAAA;MAA9DC,IAAI,EAAC,OAAO;MAACd,KAAK,EAAC,QAAQ;MAAEe,KAAK,EAAC;QACpDjC,YAAA,CAIkB+B,0BAAA;MAJDC,IAAI,EAAC,mBAAmB;MAACd,KAAK,EAAC,MAAM;MAAEe,KAAK,EAAC;;MACnDC,OAAO,EAAAC,QAAA,CAAEC,KAAK,KACbA,KAAK,CAACC,GAAG,CAACC,iBAAiB,Y,cAAvCxC,mBAAA,CAAuG,QAAAyC,UAAA,EAAAC,gBAAA,CAApDJ,KAAK,CAACC,GAAG,CAACC,iBAAiB,CAACG,SAAS,2B;;QAGxFzC,YAAA,CAAsF+B,0BAAA;MAArEC,IAAI,EAAC,gBAAgB;MAACd,KAAK,EAAC,MAAM;MAAEe,KAAK,EAAC;QAC3DjC,YAAA,CAIkB+B,0BAAA;MAJDC,IAAI,EAAC,cAAc;MAACd,KAAK,EAAC,MAAM;MAAEe,KAAK,EAAC;;MAC9CC,OAAO,EAAAC,QAAA,CAAEC,KAAK,KACzBpC,YAAA,CAA2F0C,iBAAA;QAAlFrB,IAAI,EAAEE,QAAA,CAAAoB,aAAa,CAACP,KAAK,CAACC,GAAG,CAACtB,YAAY;;0BAAG,MAA4B,C,kCAAzBqB,KAAK,CAACC,GAAG,CAACtB,YAAY,iB;;;;;QAG/Ef,YAAA,CAIkB+B,0BAAA;MAJDC,IAAI,EAAC,gBAAgB;MAACd,KAAK,EAAC,MAAM;MAAEe,KAAK,EAAC;;MAChDC,OAAO,EAAAC,QAAA,CAAEC,KAAK,KACbA,KAAK,CAACC,GAAG,CAACO,cAAc,Y,cAApC9C,mBAAA,CAAiG,QAAA+C,UAAA,EAAAL,gBAAA,CAAjDJ,KAAK,CAACC,GAAG,CAACO,cAAc,CAACH,SAAS,2B;;QAGlFzC,YAAA,CAMkB+B,0BAAA;MANDb,KAAK,EAAC,IAAI;MAAC,WAAS,EAAC,KAAK;MAACe,KAAK,EAAC;;MACvCC,OAAO,EAAAC,QAAA,CAAEC,KAAK,KACRA,KAAK,CAACC,GAAG,CAACtB,YAAY,c,cAAvC+B,YAAA,CAAmM1B,oBAAA;;QAAhJC,IAAI,EAAC,SAAS;QAACR,IAAI,EAAC,MAAM;QAAES,OAAK,EAAAX,MAAA,IAAEY,QAAA,CAAAwB,YAAY,CAACX,KAAK,CAACY,MAAM,EAAEZ,KAAK,CAACC,GAAG;QAAGZ,IAAI,EAAC,eAAe;QAAC9B,KAAkC,EAAlC;UAAA;QAAA;;0BAAmC,MAAE,C;;6FACvLK,YAAA,CAA2JoB,oBAAA;QAAhJC,IAAI,EAAC,SAAS;QAACR,IAAI,EAAC,MAAM;QAAES,OAAK,EAAAX,MAAA,IAAEY,QAAA,CAAA0B,UAAU,CAACb,KAAK,CAACY,MAAM,EAAEZ,KAAK,CAACC,GAAG;QAAGZ,IAAI,EAAC,iBAAiB;QAAC9B,KAAkC,EAAlC;UAAA;QAAA;;0BAAmC,MAAE,C;;wDAC/IK,YAAA,CAA2JoB,oBAAA;QAAhJC,IAAI,EAAC,QAAQ;QAACR,IAAI,EAAC,MAAM;QAAES,OAAK,EAAAX,MAAA,IAAEY,QAAA,CAAA2B,YAAY,CAACd,KAAK,CAACY,MAAM,EAAEZ,KAAK,CAACC,GAAG;QAAGZ,IAAI,EAAC,gBAAgB;QAAC9B,KAAkC,EAAlC;UAAA;QAAA;;0BAAmC,MAAE,C;;;;;;;qDA1BtEW,KAAA,CAAA6C,WAAW,E,GA8BpFnD,YAAA,CAE6DoD,wBAAA;IAF5CC,eAAc,EAAE9B,QAAA,CAAA+B,mBAAmB;IAAG,cAAY,EAAEhD,KAAA,CAAAiD,IAAI,CAACC,WAAW;IAAG,WAAS,EAAElD,KAAA,CAAAiD,IAAI,CAACE,QAAQ;IAC/GC,UAAU,EAAV,EAAU;IAACC,MAAM,EAAC,kCAAkC;IAAEC,KAAK,EAAEtD,KAAA,CAAAiD,IAAI,CAACM,UAAU;IAC5ElE,KAA2C,EAA3C;MAAA;MAAA;IAAA;sFAEDmE,mBAAA,WAAc,EACd9D,YAAA,CA4BY+D,oBAAA;IA5BDC,KAAK,EAAC,UAAU;IAAEC,OAAO,EAAO3D,KAAA,CAAA4D,aAAa;IAAEC,KAAK,EAAC,OAAO;IAAEC,OAAK,EAAE7C,QAAA,CAAA8C;;sBAC9E,MAsBU,CAtBVrE,YAAA,CAsBUG,kBAAA;MAtBAE,KAAK,EAAEC,KAAA,CAAAgE,UAAU;MAAGC,KAAK,EAAEjE,KAAA,CAAAkE,WAAW;MAAEC,GAAG,EAAC,eAAe;MAAC,aAAW,EAAC;;wBAChF,MAEe,CAFfzE,YAAA,CAEeQ,uBAAA;QAFDU,KAAK,EAAC;MAAI;0BACtB,MAAuD,CAAvDlB,YAAA,CAAuDS,mBAAA;sBAApCH,KAAA,CAAAgE,UAAU,CAAC5D,GAAG;qEAAdJ,KAAA,CAAAgE,UAAU,CAAC5D,GAAG,GAAAC,MAAA;UAAE+D,QAAQ,EAAR;;;UAErC1E,YAAA,CAEeQ,uBAAA;QAFDU,KAAK,EAAC;MAAK;0BACvB,MAAmF,CAAnFlB,YAAA,CAAmFS,mBAAA;UAAxEU,KAAK,EAAEb,KAAA,CAAAgE,UAAU,CAACK,MAAM,WAAWrE,KAAA,CAAAgE,UAAU,CAACM,IAAI;UAAEF,QAAQ,EAAR;;;UAEjE1E,YAAA,CAEeQ,uBAAA;QAFDU,KAAK,EAAC;MAAM;0BACxB,MAAqF,CAArFlB,YAAA,CAAqFS,mBAAA;UAA1EU,KAAK,EAAEb,KAAA,CAAAgE,UAAU,CAACO,OAAO,WAAWvE,KAAA,CAAAgE,UAAU,CAACQ,KAAK;UAAEJ,QAAQ,EAAR;;;UAEnE1E,YAAA,CAEeQ,uBAAA;QAFDU,KAAK,EAAC;MAAM;0BACxB,MAA+F,CAA/FlB,YAAA,CAA+FS,mBAAA;UAArFY,IAAI,EAAC,UAAU;sBAAUf,KAAA,CAAAgE,UAAU,CAAChC,iBAAiB;qEAA5BhC,KAAA,CAAAgE,UAAU,CAAChC,iBAAiB,GAAA3B,MAAA;UAAGoE,IAAI,EAAE,CAAC;UAAEL,QAAQ,EAAR;;;UAE7E1E,YAAA,CAKeQ,uBAAA;QALDU,KAAK,EAAC,MAAM;QAACc,IAAI,EAAC;;0BAC9B,MAGiB,CAHjBhC,YAAA,CAGiBgF,yBAAA;sBAHQ1E,KAAA,CAAAgE,UAAU,CAACvD,YAAY;qEAAvBT,KAAA,CAAAgE,UAAU,CAACvD,YAAY,GAAAJ,MAAA;;4BAC9C,MAAsC,CAAtCX,YAAA,CAAsCiF,mBAAA;YAA5B/D,KAAK,EAAC;UAAM;8BAAC,MAAI,C;;cAC3BlB,YAAA,CAAwCiF,mBAAA;YAA9B/D,KAAK,EAAC;UAAO;8BAAC,MAAK,C;;;;;;;UAGjClB,YAAA,CAEeQ,uBAAA;QAFDU,KAAK,EAAC,MAAM;QAACc,IAAI,EAAC;;0BAC9B,MAAyG,CAAzGhC,YAAA,CAAyGS,mBAAA;UAA/FY,IAAI,EAAC,UAAU;sBAAUf,KAAA,CAAAgE,UAAU,CAAC1B,cAAc;qEAAzBtC,KAAA,CAAAgE,UAAU,CAAC1B,cAAc,GAAAjC,MAAA;UAAEC,WAAW,EAAC,SAAS;UAAEmE,IAAI,EAAE;;;;;;2CAG/FG,mBAAA,CAGM,OAHNC,WAGM,GAFJnF,YAAA,CAAwDoB,oBAAA;MAA5CE,OAAK,EAAA8D,MAAA,QAAAA,MAAA,MAAAzE,MAAA,IAAEL,KAAA,CAAA4D,aAAa;;wBAAU,MAAE,C;;QAC5ClE,YAAA,CAAoFoB,oBAAA;MAAzEC,IAAI,EAAC,SAAS;MAAEC,OAAK,EAAEC,QAAA,CAAA8D,YAAY;MAAGC,OAAO,EAAEhF,KAAA,CAAAiF;;wBAAY,MAAE,C"}]}