package com.controller;

import com.model.*;
import com.response.Response;
import com.service.*;
import com.util.PageBean;
import com.util.removeHTML;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.text.SimpleDateFormat;
import java.util.Date;

@RestController
@RequestMapping("/api/dormitory")
public class DormitoryController{
	
	@Resource
	private DormitoryService dormitoryService;
	
	//宿舍列表
	@RequestMapping(value="/list")
	@CrossOrigin
	public Response<List<Dormitory>> list(@RequestBody Dormitory dormitory, @RequestParam Integer currentPage, HttpServletRequest req) throws Exception {
		int pageSize=Integer.parseInt(req.getParameter("pageSize")); //每页显示记录数
		int offset = (currentPage - 1) * pageSize; //当前页开始记录
		int counts = 0;  //总记录数
		PageBean page = new PageBean(offset, pageSize); //分页对象
		//查询记录总数
		counts = dormitoryService.getCount(dormitory);
		//获取当前页记录
		List<Dormitory> dormitoryList = dormitoryService.queryDormitoryList(dormitory, page);
        
		int page_count = counts % PageBean.PAGE_IETM == 0 ? counts / PageBean.PAGE_IETM : counts / PageBean.PAGE_IETM + 1; //总页数
		return Response.success(dormitoryList, counts, page_count);
	}
        
	//添加宿舍
	@ResponseBody
	@PostMapping(value = "/add")
	@CrossOrigin
	public Response add(@RequestBody Dormitory dormitory, HttpServletRequest req) throws Exception {
		try {
			//判断用户名是否存在
            Dormitory dormitory1 = new Dormitory();
            dormitory1.setDoro(dormitory.getDoro());
            List<Dormitory> dormitoryList = dormitoryService.queryDormitoryList(dormitory1, null);
            if (dormitoryList.size() > 0) {
                return Response.error(201, "宿舍编号已存在，请重新输入");
            }
            else
            {
                dormitoryService.insertDormitory(dormitory); //添加
            }
   
			} catch (Exception e) {
			return Response.error();
		}
		return Response.success();
	}
    
	//删除宿舍
	@ResponseBody
	@PostMapping(value = "/del")
	@CrossOrigin
	public Response del(HttpServletRequest req) throws Exception {
		try {
			String id = req.getParameter("id");
			dormitoryService.deleteDormitory(id); //删除
			} catch (Exception e) {
			return Response.error();
		}
		return Response.success();
	}
	
	//修改宿舍
	@ResponseBody
	@PostMapping(value = "/update")
	@CrossOrigin
	public Response update(@RequestBody Dormitory dormitory, HttpServletRequest req) throws Exception {
		try {
			dormitoryService.updateDormitory(dormitory); //修改
			} catch (Exception e) {
			return Response.error();
		}
		return Response.success();
	}
	
	//返回宿舍详情
	@ResponseBody
	@PostMapping(value = "/get")
	@CrossOrigin
	public Response get(HttpServletRequest req) throws Exception {
		try {
			String id = req.getParameter("id");
			Dormitory dormitory=dormitoryService.queryDormitoryById(id); //根据ID查询
			return Response.success(dormitory);
			} catch (Exception e) {
			return Response.error();
		}

	}

	//根据宿舍楼ID和性别查询宿舍列表
	@RequestMapping(value="/listByBuildingAndGender")
	@CrossOrigin
	@SuppressWarnings("unchecked")
	public Response<List<Dormitory>> listByBuildingAndGender(@RequestBody Dormitory dormitory, HttpServletRequest req) throws Exception {
		try {
			//设置查询条件：宿舍楼ID和性别
			List<Dormitory> dormitoryList = dormitoryService.queryDormitoryList(dormitory, null);
			return Response.success(dormitoryList);
		} catch (Exception e) {
			e.printStackTrace();
			return (Response<List<Dormitory>>) Response.error(500, "查询宿舍列表失败");
		}
	}
    
}

