{"remainingRequest": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\Login.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\Login.vue", "mtime": 1749045164779}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\babel.config.js", "mtime": 1749034452000}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749043011899}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749043012919}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749043011899}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749043013130}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["request", "base", "name", "data", "year", "Date", "getFullYear", "loginModel", "username", "password", "radio", "loginModel2", "add", "formVisible", "formData", "dormbuildingList", "dormitoryList", "filteredDormitoryList", "add<PERSON><PERSON>", "sno", "required", "message", "trigger", "password2", "validator", "rule", "value", "callback", "Error", "stname", "sex", "phone", "pattern", "doro", "specialty", "clsname", "btnLoading", "mounted", "created", "methods", "login", "that", "$message", "type", "loading", "role", "url", "aname", "loginpassword", "post", "then", "res", "code", "console", "log", "JSON", "stringify", "resdata", "sessionStorage", "setItem", "$router", "push", "msg", "hno", "rno", "toreg", "isClear", "rules", "getdormbuildingList", "$nextTick", "$refs", "resetFields", "reg", "validate", "valid", "offset", "clearValidate", "para", "onBuildingChange", "updateDormitoryList", "onGenderChange", "dbid", "<PERSON><PERSON><PERSON>", "catch"], "sources": ["I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\Login.vue"], "sourcesContent": ["<template>\n\n  <div class=\"auth-wrapper aut-bg-img\">\n        <div class=\"auth-content\"  style=\"width: 450px;\">\n             <form action=\"#\">\n            <div class=\"card\" style=\"padding-top: 30px; padding-bottom: 30px; \">\n                <div class=\"card-body text-center\">\n                    <div class=\"mb-4\">\n                        <i class=\"feather icon-unlock auth-icon\"></i>\n                    </div>\n                    <h3 class=\"mb-4\">宿舍管理系统</h3>\n                    <div class=\"input-group mb-3\">\n                        <input type=\"text\" class=\"form-control\" placeholder=\"用户名\" v-model=\"loginModel.username\">\n                    </div>\n                    <div class=\"input-group mb-4\">\n                        <input type=\"password\" class=\"form-control\" placeholder=\"密码\" v-model=\"loginModel.password\">\n                    </div>\n                       <div class=\"form-group text-left\">\n                        <div class=\"checkbox checkbox-fill d-inline\">\n                          <el-radio label=\"管理员\" v-model=\"loginModel.radio\">管理员</el-radio>\n                                   <el-radio label=\"学生\" v-model=\"loginModel.radio\">学生</el-radio>\n      <el-radio label=\"维修员\" v-model=\"loginModel.radio\">维修员</el-radio>\n      <el-radio label=\"宿管阿姨\" v-model=\"loginModel.radio\">宿管阿姨</el-radio>\n\n\n                        </div>\n                    </div>\n\n\n            <el-button class=\"btn btn-primary shadow-2 mb-4\"  @click=\"login\">登录</el-button>\n                     <a href=\"#\"  @click=\"toreg\"> 新学生注册 </a>\n <el-dialog title=\"学生注册\"  v-model=\"formVisible\" width=\"40%\" :close-on-click-modal=\"false\">\n <el-form :model=\"formData\" label-width=\"20%\" ref=\"formDataRef\" :rules=\"rules\"  align=\"left\">\n<el-form-item label=\"学号\" prop=\"sno\">\n<el-input v-model=\"formData.sno\" placeholder=\"学号\"  style=\"width:50%;\" ></el-input>\n</el-form-item>\n<el-form-item label=\"登录密码\" prop=\"password\">\n<el-input type=\"password\" v-model=\"formData.password\" placeholder=\"登录密码\"  style=\"width:50%;\" ></el-input>\n</el-form-item>\n<el-form-item label=\"确认密码\" prop=\"password2\">\n<el-input type=\"password\" v-model=\"formData.password2\" placeholder=\"确认密码\"  style=\"width:50%;\" ></el-input>\n</el-form-item>\n<el-form-item label=\"姓名\" prop=\"stname\">\n<el-input v-model=\"formData.stname\" placeholder=\"姓名\"  style=\"width:50%;\" ></el-input>\n</el-form-item>\n<el-form-item label=\"性别\" prop=\"sex\">\n<el-radio-group v-model=\"formData.sex\" @change=\"onGenderChange\">\n<el-radio label=\"男\">\n男\n</el-radio>\n<el-radio label=\"女\">\n女\n</el-radio>\n</el-radio-group>\n</el-form-item>\n<el-form-item label=\"手机号码\" prop=\"phone\">\n<el-input v-model=\"formData.phone\" placeholder=\"手机号码\"  style=\"width:50%;\" ></el-input>\n</el-form-item>\n<el-form-item label=\"宿舍楼\" prop=\"dbid\">\n<el-select v-model=\"formData.dbid\" placeholder=\"请选择\"  size=\"small\" @change=\"onBuildingChange\">\n<el-option v-for=\"item in dormbuildingList\" :key=\"item.dbid\" :label=\"item.dbname\" :value=\"item.dbid\"></el-option>\n</el-select>\n</el-form-item>\n<el-form-item label=\"宿舍\" prop=\"doro\">\n<el-select v-model=\"formData.doro\" placeholder=\"请先选择宿舍楼和性别\"  size=\"small\" :disabled=\"!formData.dbid || !formData.sex\">\n<el-option v-for=\"item in filteredDormitoryList\" :key=\"item.doro\" :label=\"item.doro\" :value=\"item.doro\"></el-option>\n</el-select>\n</el-form-item>\n<el-form-item label=\"专业\" prop=\"specialty\">\n<el-input v-model=\"formData.specialty\" placeholder=\"专业\"  style=\"width:50%;\" ></el-input>\n</el-form-item>\n<el-form-item label=\"班级\" prop=\"clsname\">\n<el-input v-model=\"formData.clsname\" placeholder=\"班级\"  style=\"width:50%;\" ></el-input>\n</el-form-item>\n<el-form-item>\n<el-button type=\"primary\" @click=\"reg\" :loading=\"btnLoading\" >注 册</el-button>\n</el-form-item>\n</el-form>\n</el-dialog>\n\n\n                </div>\n            </div>\n            </form>\n\n        </div>\n    </div>\n\n\n\n</template>\n<script>\nimport request, { base } from \"../../utils/http\";\nexport default {\n  name: \"Login\",\n  data() {\n    return {\n      year: new Date().getFullYear(),\n      loginModel: {\n        username: \"\",\n        password: \"\",\n        radio: \"管理员\",\n      },\n      loginModel2: {},\n     add: true, //是否是添加\n      formVisible: false,\n      formData:{},\n      dormbuildingList: [], //宿舍楼列表\n      dormitoryList: [], //所有宿舍列表\n      filteredDormitoryList: [], //过滤后的宿舍列表\n\n      addrules: {\n          sno: [{ required: true, message: '请输入学号', trigger: 'blur' },],\n          password: [{ required: true, message: '请输入登录密码', trigger: 'blur' }],\n          password2: [{ required: true, message: '请输入登录密码', trigger: 'blur' },{ validator: (rule, value, callback) => { if (value !== this.formData.password) { callback(new Error('两次输入密码不一致!')); } else { callback(); } }, trigger: 'blur' },],\n          stname: [{ required: true, message: '请输入姓名', trigger: 'blur' },],\n          sex: [{ required: true, message: '请输入性别', trigger: 'blur' },],\n          phone: [{ required: true, message: '请输入手机号码', trigger: 'blur' },        { pattern: /^1[3456789]\\d{9}$/, message: '手机号码格式不正确', trigger: 'blur' },],\n          doro: [{ required: true, message: '请选择宿舍', trigger: 'onchange' }],\n          specialty: [{ required: true, message: '请输入专业', trigger: 'blur' },],\n          clsname: [{ required: true, message: '请输入班级', trigger: 'blur' },],\n      },\n\n\n      btnLoading: false, //按钮是否在加载中\n\n\n    };\n  },\n  mounted() {},\n  created() {\n\n  },\n  methods: {\n    login() {\n      let that = this;\n\n      if (that.loginModel.username == \"\") {\n        that.$message({\n          message: \"请输入账号\",\n          type: \"warning\",\n        });\n        return;\n      }\n      if (that.loginModel.password == \"\") {\n        that.$message({\n          message: \"请输入密码\",\n          type: \"warning\",\n        });\n        return;\n      }\n\n      this.loading = true;\n     var role = that.loginModel.radio; //获取身份\nif (role == '管理员') {\n      let url = base + \"/admin/login\";\n      this.loginModel2.aname = this.loginModel.username;\n      this.loginModel2.loginpassword = this.loginModel.password;\n      request.post(url, this.loginModel2).then((res) => {\n        this.loading = false;\n        if (res.code == 200) {\n          console.log(JSON.stringify(res.resdata));\n          sessionStorage.setItem(\"user\", JSON.stringify(res.resdata));\n          sessionStorage.setItem(\"userLname\", res.resdata.aname);\n          sessionStorage.setItem(\"role\", \"管理员\");\n          this.$router.push(\"/main\");\n        } else {\n          this.$message({\n            message: res.msg,\n            type: \"error\",\n          });\n        }\n      });\n          }\nelse if (role == '宿管阿姨') {\n      let url = base + \"/hostess/login\";\n      this.loginModel2.hno = this.loginModel.username;\n      this.loginModel2.password = this.loginModel.password;\n      request.post(url, this.loginModel2).then((res) => {\n        this.loading = false;\n        if (res.code == 200) {\n          console.log(JSON.stringify(res.resdata));\n          sessionStorage.setItem(\"user\", JSON.stringify(res.resdata));\n          sessionStorage.setItem(\"userLname\", res.resdata.hno);\n          sessionStorage.setItem(\"role\", \"宿管阿姨\");\n          this.$router.push(\"/main\");\n        } else {\n          this.$message({\n            message: res.msg,\n            type: \"error\",\n          });\n        }\n      });\n          }\nelse if (role == '维修员') {\n      let url = base + \"/repairmen/login\";\n      this.loginModel2.rno = this.loginModel.username;\n      this.loginModel2.password = this.loginModel.password;\n      request.post(url, this.loginModel2).then((res) => {\n        this.loading = false;\n        if (res.code == 200) {\n          console.log(JSON.stringify(res.resdata));\n          sessionStorage.setItem(\"user\", JSON.stringify(res.resdata));\n          sessionStorage.setItem(\"userLname\", res.resdata.rno);\n          sessionStorage.setItem(\"role\", \"维修员\");\n          this.$router.push(\"/main\");\n        } else {\n          this.$message({\n            message: res.msg,\n            type: \"error\",\n          });\n        }\n      });\n          }\nelse if (role == '学生') {\n      let url = base + \"/student/login\";\n      this.loginModel2.sno = this.loginModel.username;\n      this.loginModel2.password = this.loginModel.password;\n      request.post(url, this.loginModel2).then((res) => {\n        this.loading = false;\n        if (res.code == 200) {\n          console.log(JSON.stringify(res.resdata));\n          sessionStorage.setItem(\"user\", JSON.stringify(res.resdata));\n          sessionStorage.setItem(\"userLname\", res.resdata.sno);\n          sessionStorage.setItem(\"role\", \"学生\");\n          this.$router.push(\"/main\");\n        } else {\n          this.$message({\n            message: res.msg,\n            type: \"error\",\n          });\n        }\n      });\n          }\n\n\n    },\n\n    toreg() {\n    this.formVisible = true;\n    this.add = true;\n    this.isClear = true;\n    this.rules = this.addrules;\n    this.getdormbuildingList(); // 加载宿舍楼列表\n    this.$nextTick(() => {\n        this.$refs[\"formDataRef\"].resetFields();\n    });\n},\n\n//注册\nreg() {\n    //表单验证\n    this.$refs[\"formDataRef\"].validate((valid) => {\n\n        if (valid) {\n            let url = base + \"/student/add\"; //请求地址\n            this.btnLoading = true; //按钮加载状态\n            request.post(url, this.formData).then((res) => { //请求接口\n                if (res.code == 200) {\n                    this.$message({\n                        message: \"恭喜您，注册成功，请登录！\",\n                        type: \"success\",\n                        offset: 320,\n                    });\n                    this.formVisible = false; //关闭表单\n                    this.btnLoading = false; //按钮加载状态\n                    this.$refs[\"formDataRef\"].resetFields(); //重置表单\n                    this.$refs[\"formDataRef\"].clearValidate();\n                }\n                else if (res.code == 201) {\n                    this.$message({\n                        message: res.msg,\n                        type: \"error\",\n                        offset: 320,\n                    });\n                }\n                else {\n                    this.$message({\n                        message: \"服务器错误\",\n                        type: \"error\",\n                        offset: 320,\n                    });\n                }\n            });\n        }\n    });\n},\n// 获取宿舍楼列表\ngetdormbuildingList() {\n  let para = {};\n  let url = base + \"/dormbuilding/list?currentPage=1&pageSize=1000\";\n  request.post(url, para).then((res) => {\n    this.dormbuildingList = res.resdata;\n  });\n},\n\n// 宿舍楼变化时的处理\nonBuildingChange() {\n  this.formData.doro = ''; // 清空宿舍选择\n  this.updateDormitoryList();\n},\n\n// 性别变化时的处理\nonGenderChange() {\n  this.formData.doro = ''; // 清空宿舍选择\n  this.updateDormitoryList();\n},\n\n// 更新宿舍列表\nupdateDormitoryList() {\n  if (!this.formData.dbid || !this.formData.sex) {\n    this.filteredDormitoryList = [];\n    return;\n  }\n\n  let para = {\n    dbid: this.formData.dbid,\n    dorgender: this.formData.sex\n  };\n  let url = base + \"/dormitory/listByBuildingAndGender\";\n  request.post(url, para).then((res) => {\n    if (res.code == 200) {\n      this.filteredDormitoryList = res.resdata;\n    } else {\n      this.filteredDormitoryList = [];\n      this.$message({\n        message: res.msg || \"获取宿舍列表失败\",\n        type: \"error\",\n        offset: 320,\n      });\n    }\n  }).catch(() => {\n    this.filteredDormitoryList = [];\n    this.$message({\n      message: \"获取宿舍列表失败\",\n      type: \"error\",\n      offset: 320,\n    });\n  });\n},\n\n\n\n\n\n  },\n};\n</script>\n\n<style scoped>\n@import url(../assets/css/htstyle.css);\n\n</style>\n\n\n"], "mappings": ";;AA4FA,OAAOA,OAAO,IAAIC,IAAG,QAAS,kBAAkB;AAChD,eAAe;EACbC,IAAI,EAAE,OAAO;EACbC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,IAAI,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MAC9BC,UAAU,EAAE;QACVC,QAAQ,EAAE,EAAE;QACZC,QAAQ,EAAE,EAAE;QACZC,KAAK,EAAE;MACT,CAAC;MACDC,WAAW,EAAE,CAAC,CAAC;MAChBC,GAAG,EAAE,IAAI;MAAE;MACVC,WAAW,EAAE,KAAK;MAClBC,QAAQ,EAAC,CAAC,CAAC;MACXC,gBAAgB,EAAE,EAAE;MAAE;MACtBC,aAAa,EAAE,EAAE;MAAE;MACnBC,qBAAqB,EAAE,EAAE;MAAE;;MAE3BC,QAAQ,EAAE;QACNC,GAAG,EAAE,CAAC;UAAEC,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,OAAO;UAAEC,OAAO,EAAE;QAAO,CAAC,CAAE;QAC7Db,QAAQ,EAAE,CAAC;UAAEW,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAO,CAAC,CAAC;QACnEC,SAAS,EAAE,CAAC;UAAEH,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAO,CAAC,EAAC;UAAEE,SAAS,EAAEA,CAACC,IAAI,EAAEC,KAAK,EAAEC,QAAQ,KAAK;YAAE,IAAID,KAAI,KAAM,IAAI,CAACZ,QAAQ,CAACL,QAAQ,EAAE;cAAEkB,QAAQ,CAAC,IAAIC,KAAK,CAAC,YAAY,CAAC,CAAC;YAAE,OAAO;cAAED,QAAQ,CAAC,CAAC;YAAE;UAAE,CAAC;UAAEL,OAAO,EAAE;QAAO,CAAC,CAAE;QACtOO,MAAM,EAAE,CAAC;UAAET,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,OAAO;UAAEC,OAAO,EAAE;QAAO,CAAC,CAAE;QAChEQ,GAAG,EAAE,CAAC;UAAEV,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,OAAO;UAAEC,OAAO,EAAE;QAAO,CAAC,CAAE;QAC7DS,KAAK,EAAE,CAAC;UAAEX,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAO,CAAC,EAAS;UAAEU,OAAO,EAAE,mBAAmB;UAAEX,OAAO,EAAE,WAAW;UAAEC,OAAO,EAAE;QAAO,CAAC,CAAE;QACjJW,IAAI,EAAE,CAAC;UAAEb,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,OAAO;UAAEC,OAAO,EAAE;QAAW,CAAC,CAAC;QACjEY,SAAS,EAAE,CAAC;UAAEd,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,OAAO;UAAEC,OAAO,EAAE;QAAO,CAAC,CAAE;QACnEa,OAAO,EAAE,CAAC;UAAEf,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,OAAO;UAAEC,OAAO,EAAE;QAAO,CAAC;MACnE,CAAC;MAGDc,UAAU,EAAE,KAAK,CAAE;IAGrB,CAAC;EACH,CAAC;;EACDC,OAAOA,CAAA,EAAG,CAAC,CAAC;EACZC,OAAOA,CAAA,EAAG,CAEV,CAAC;EACDC,OAAO,EAAE;IACPC,KAAKA,CAAA,EAAG;MACN,IAAIC,IAAG,GAAI,IAAI;MAEf,IAAIA,IAAI,CAAClC,UAAU,CAACC,QAAO,IAAK,EAAE,EAAE;QAClCiC,IAAI,CAACC,QAAQ,CAAC;UACZrB,OAAO,EAAE,OAAO;UAChBsB,IAAI,EAAE;QACR,CAAC,CAAC;QACF;MACF;MACA,IAAIF,IAAI,CAAClC,UAAU,CAACE,QAAO,IAAK,EAAE,EAAE;QAClCgC,IAAI,CAACC,QAAQ,CAAC;UACZrB,OAAO,EAAE,OAAO;UAChBsB,IAAI,EAAE;QACR,CAAC,CAAC;QACF;MACF;MAEA,IAAI,CAACC,OAAM,GAAI,IAAI;MACpB,IAAIC,IAAG,GAAIJ,IAAI,CAAClC,UAAU,CAACG,KAAK,EAAE;MACvC,IAAImC,IAAG,IAAK,KAAK,EAAE;QACb,IAAIC,GAAE,GAAI7C,IAAG,GAAI,cAAc;QAC/B,IAAI,CAACU,WAAW,CAACoC,KAAI,GAAI,IAAI,CAACxC,UAAU,CAACC,QAAQ;QACjD,IAAI,CAACG,WAAW,CAACqC,aAAY,GAAI,IAAI,CAACzC,UAAU,CAACE,QAAQ;QACzDT,OAAO,CAACiD,IAAI,CAACH,GAAG,EAAE,IAAI,CAACnC,WAAW,CAAC,CAACuC,IAAI,CAAEC,GAAG,IAAK;UAChD,IAAI,CAACP,OAAM,GAAI,KAAK;UACpB,IAAIO,GAAG,CAACC,IAAG,IAAK,GAAG,EAAE;YACnBC,OAAO,CAACC,GAAG,CAACC,IAAI,CAACC,SAAS,CAACL,GAAG,CAACM,OAAO,CAAC,CAAC;YACxCC,cAAc,CAACC,OAAO,CAAC,MAAM,EAAEJ,IAAI,CAACC,SAAS,CAACL,GAAG,CAACM,OAAO,CAAC,CAAC;YAC3DC,cAAc,CAACC,OAAO,CAAC,WAAW,EAAER,GAAG,CAACM,OAAO,CAACV,KAAK,CAAC;YACtDW,cAAc,CAACC,OAAO,CAAC,MAAM,EAAE,KAAK,CAAC;YACrC,IAAI,CAACC,OAAO,CAACC,IAAI,CAAC,OAAO,CAAC;UAC5B,OAAO;YACL,IAAI,CAACnB,QAAQ,CAAC;cACZrB,OAAO,EAAE8B,GAAG,CAACW,GAAG;cAChBnB,IAAI,EAAE;YACR,CAAC,CAAC;UACJ;QACF,CAAC,CAAC;MACE,OACL,IAAIE,IAAG,IAAK,MAAM,EAAE;QACnB,IAAIC,GAAE,GAAI7C,IAAG,GAAI,gBAAgB;QACjC,IAAI,CAACU,WAAW,CAACoD,GAAE,GAAI,IAAI,CAACxD,UAAU,CAACC,QAAQ;QAC/C,IAAI,CAACG,WAAW,CAACF,QAAO,GAAI,IAAI,CAACF,UAAU,CAACE,QAAQ;QACpDT,OAAO,CAACiD,IAAI,CAACH,GAAG,EAAE,IAAI,CAACnC,WAAW,CAAC,CAACuC,IAAI,CAAEC,GAAG,IAAK;UAChD,IAAI,CAACP,OAAM,GAAI,KAAK;UACpB,IAAIO,GAAG,CAACC,IAAG,IAAK,GAAG,EAAE;YACnBC,OAAO,CAACC,GAAG,CAACC,IAAI,CAACC,SAAS,CAACL,GAAG,CAACM,OAAO,CAAC,CAAC;YACxCC,cAAc,CAACC,OAAO,CAAC,MAAM,EAAEJ,IAAI,CAACC,SAAS,CAACL,GAAG,CAACM,OAAO,CAAC,CAAC;YAC3DC,cAAc,CAACC,OAAO,CAAC,WAAW,EAAER,GAAG,CAACM,OAAO,CAACM,GAAG,CAAC;YACpDL,cAAc,CAACC,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC;YACtC,IAAI,CAACC,OAAO,CAACC,IAAI,CAAC,OAAO,CAAC;UAC5B,OAAO;YACL,IAAI,CAACnB,QAAQ,CAAC;cACZrB,OAAO,EAAE8B,GAAG,CAACW,GAAG;cAChBnB,IAAI,EAAE;YACR,CAAC,CAAC;UACJ;QACF,CAAC,CAAC;MACE,OACL,IAAIE,IAAG,IAAK,KAAK,EAAE;QAClB,IAAIC,GAAE,GAAI7C,IAAG,GAAI,kBAAkB;QACnC,IAAI,CAACU,WAAW,CAACqD,GAAE,GAAI,IAAI,CAACzD,UAAU,CAACC,QAAQ;QAC/C,IAAI,CAACG,WAAW,CAACF,QAAO,GAAI,IAAI,CAACF,UAAU,CAACE,QAAQ;QACpDT,OAAO,CAACiD,IAAI,CAACH,GAAG,EAAE,IAAI,CAACnC,WAAW,CAAC,CAACuC,IAAI,CAAEC,GAAG,IAAK;UAChD,IAAI,CAACP,OAAM,GAAI,KAAK;UACpB,IAAIO,GAAG,CAACC,IAAG,IAAK,GAAG,EAAE;YACnBC,OAAO,CAACC,GAAG,CAACC,IAAI,CAACC,SAAS,CAACL,GAAG,CAACM,OAAO,CAAC,CAAC;YACxCC,cAAc,CAACC,OAAO,CAAC,MAAM,EAAEJ,IAAI,CAACC,SAAS,CAACL,GAAG,CAACM,OAAO,CAAC,CAAC;YAC3DC,cAAc,CAACC,OAAO,CAAC,WAAW,EAAER,GAAG,CAACM,OAAO,CAACO,GAAG,CAAC;YACpDN,cAAc,CAACC,OAAO,CAAC,MAAM,EAAE,KAAK,CAAC;YACrC,IAAI,CAACC,OAAO,CAACC,IAAI,CAAC,OAAO,CAAC;UAC5B,OAAO;YACL,IAAI,CAACnB,QAAQ,CAAC;cACZrB,OAAO,EAAE8B,GAAG,CAACW,GAAG;cAChBnB,IAAI,EAAE;YACR,CAAC,CAAC;UACJ;QACF,CAAC,CAAC;MACE,OACL,IAAIE,IAAG,IAAK,IAAI,EAAE;QACjB,IAAIC,GAAE,GAAI7C,IAAG,GAAI,gBAAgB;QACjC,IAAI,CAACU,WAAW,CAACQ,GAAE,GAAI,IAAI,CAACZ,UAAU,CAACC,QAAQ;QAC/C,IAAI,CAACG,WAAW,CAACF,QAAO,GAAI,IAAI,CAACF,UAAU,CAACE,QAAQ;QACpDT,OAAO,CAACiD,IAAI,CAACH,GAAG,EAAE,IAAI,CAACnC,WAAW,CAAC,CAACuC,IAAI,CAAEC,GAAG,IAAK;UAChD,IAAI,CAACP,OAAM,GAAI,KAAK;UACpB,IAAIO,GAAG,CAACC,IAAG,IAAK,GAAG,EAAE;YACnBC,OAAO,CAACC,GAAG,CAACC,IAAI,CAACC,SAAS,CAACL,GAAG,CAACM,OAAO,CAAC,CAAC;YACxCC,cAAc,CAACC,OAAO,CAAC,MAAM,EAAEJ,IAAI,CAACC,SAAS,CAACL,GAAG,CAACM,OAAO,CAAC,CAAC;YAC3DC,cAAc,CAACC,OAAO,CAAC,WAAW,EAAER,GAAG,CAACM,OAAO,CAACtC,GAAG,CAAC;YACpDuC,cAAc,CAACC,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC;YACpC,IAAI,CAACC,OAAO,CAACC,IAAI,CAAC,OAAO,CAAC;UAC5B,OAAO;YACL,IAAI,CAACnB,QAAQ,CAAC;cACZrB,OAAO,EAAE8B,GAAG,CAACW,GAAG;cAChBnB,IAAI,EAAE;YACR,CAAC,CAAC;UACJ;QACF,CAAC,CAAC;MACE;IAGN,CAAC;IAEDsB,KAAKA,CAAA,EAAG;MACR,IAAI,CAACpD,WAAU,GAAI,IAAI;MACvB,IAAI,CAACD,GAAE,GAAI,IAAI;MACf,IAAI,CAACsD,OAAM,GAAI,IAAI;MACnB,IAAI,CAACC,KAAI,GAAI,IAAI,CAACjD,QAAQ;MAC1B,IAAI,CAACkD,mBAAmB,CAAC,CAAC,EAAE;MAC5B,IAAI,CAACC,SAAS,CAAC,MAAM;QACjB,IAAI,CAACC,KAAK,CAAC,aAAa,CAAC,CAACC,WAAW,CAAC,CAAC;MAC3C,CAAC,CAAC;IACN,CAAC;IAED;IACAC,GAAGA,CAAA,EAAG;MACF;MACA,IAAI,CAACF,KAAK,CAAC,aAAa,CAAC,CAACG,QAAQ,CAAEC,KAAK,IAAK;QAE1C,IAAIA,KAAK,EAAE;UACP,IAAI5B,GAAE,GAAI7C,IAAG,GAAI,cAAc,EAAE;UACjC,IAAI,CAACmC,UAAS,GAAI,IAAI,EAAE;UACxBpC,OAAO,CAACiD,IAAI,CAACH,GAAG,EAAE,IAAI,CAAChC,QAAQ,CAAC,CAACoC,IAAI,CAAEC,GAAG,IAAK;YAAE;YAC7C,IAAIA,GAAG,CAACC,IAAG,IAAK,GAAG,EAAE;cACjB,IAAI,CAACV,QAAQ,CAAC;gBACVrB,OAAO,EAAE,eAAe;gBACxBsB,IAAI,EAAE,SAAS;gBACfgC,MAAM,EAAE;cACZ,CAAC,CAAC;cACF,IAAI,CAAC9D,WAAU,GAAI,KAAK,EAAE;cAC1B,IAAI,CAACuB,UAAS,GAAI,KAAK,EAAE;cACzB,IAAI,CAACkC,KAAK,CAAC,aAAa,CAAC,CAACC,WAAW,CAAC,CAAC,EAAE;cACzC,IAAI,CAACD,KAAK,CAAC,aAAa,CAAC,CAACM,aAAa,CAAC,CAAC;YAC7C,OACK,IAAIzB,GAAG,CAACC,IAAG,IAAK,GAAG,EAAE;cACtB,IAAI,CAACV,QAAQ,CAAC;gBACVrB,OAAO,EAAE8B,GAAG,CAACW,GAAG;gBAChBnB,IAAI,EAAE,OAAO;gBACbgC,MAAM,EAAE;cACZ,CAAC,CAAC;YACN,OACK;cACD,IAAI,CAACjC,QAAQ,CAAC;gBACVrB,OAAO,EAAE,OAAO;gBAChBsB,IAAI,EAAE,OAAO;gBACbgC,MAAM,EAAE;cACZ,CAAC,CAAC;YACN;UACJ,CAAC,CAAC;QACN;MACJ,CAAC,CAAC;IACN,CAAC;IACD;IACAP,mBAAmBA,CAAA,EAAG;MACpB,IAAIS,IAAG,GAAI,CAAC,CAAC;MACb,IAAI/B,GAAE,GAAI7C,IAAG,GAAI,gDAAgD;MACjED,OAAO,CAACiD,IAAI,CAACH,GAAG,EAAE+B,IAAI,CAAC,CAAC3B,IAAI,CAAEC,GAAG,IAAK;QACpC,IAAI,CAACpC,gBAAe,GAAIoC,GAAG,CAACM,OAAO;MACrC,CAAC,CAAC;IACJ,CAAC;IAED;IACAqB,gBAAgBA,CAAA,EAAG;MACjB,IAAI,CAAChE,QAAQ,CAACmB,IAAG,GAAI,EAAE,EAAE;MACzB,IAAI,CAAC8C,mBAAmB,CAAC,CAAC;IAC5B,CAAC;IAED;IACAC,cAAcA,CAAA,EAAG;MACf,IAAI,CAAClE,QAAQ,CAACmB,IAAG,GAAI,EAAE,EAAE;MACzB,IAAI,CAAC8C,mBAAmB,CAAC,CAAC;IAC5B,CAAC;IAED;IACAA,mBAAmBA,CAAA,EAAG;MACpB,IAAI,CAAC,IAAI,CAACjE,QAAQ,CAACmE,IAAG,IAAK,CAAC,IAAI,CAACnE,QAAQ,CAACgB,GAAG,EAAE;QAC7C,IAAI,CAACb,qBAAoB,GAAI,EAAE;QAC/B;MACF;MAEA,IAAI4D,IAAG,GAAI;QACTI,IAAI,EAAE,IAAI,CAACnE,QAAQ,CAACmE,IAAI;QACxBC,SAAS,EAAE,IAAI,CAACpE,QAAQ,CAACgB;MAC3B,CAAC;MACD,IAAIgB,GAAE,GAAI7C,IAAG,GAAI,oCAAoC;MACrDD,OAAO,CAACiD,IAAI,CAACH,GAAG,EAAE+B,IAAI,CAAC,CAAC3B,IAAI,CAAEC,GAAG,IAAK;QACpC,IAAIA,GAAG,CAACC,IAAG,IAAK,GAAG,EAAE;UACnB,IAAI,CAACnC,qBAAoB,GAAIkC,GAAG,CAACM,OAAO;QAC1C,OAAO;UACL,IAAI,CAACxC,qBAAoB,GAAI,EAAE;UAC/B,IAAI,CAACyB,QAAQ,CAAC;YACZrB,OAAO,EAAE8B,GAAG,CAACW,GAAE,IAAK,UAAU;YAC9BnB,IAAI,EAAE,OAAO;YACbgC,MAAM,EAAE;UACV,CAAC,CAAC;QACJ;MACF,CAAC,CAAC,CAACQ,KAAK,CAAC,MAAM;QACb,IAAI,CAAClE,qBAAoB,GAAI,EAAE;QAC/B,IAAI,CAACyB,QAAQ,CAAC;UACZrB,OAAO,EAAE,UAAU;UACnBsB,IAAI,EAAE,OAAO;UACbgC,MAAM,EAAE;QACV,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;EAME;AACF,CAAC"}]}