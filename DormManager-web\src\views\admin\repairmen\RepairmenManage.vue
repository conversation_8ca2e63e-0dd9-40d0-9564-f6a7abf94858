<template>
    <div style="width: 100%;line-height: 30px;text-align: left;">
      <el-col  :span="24"  style="padding-bottom: 0px; margin-left: 10px">
<el-form :inline="true" :model="filters" >
<el-form-item>
<el-input v-model="filters.rno" placeholder="账号"  size="small"></el-input>
</el-form-item>
<el-form-item>
<el-input v-model="filters.rnname" placeholder="姓名"  size="small"></el-input>
</el-form-item>
<el-form-item>
<el-input v-model="filters.phone" placeholder="手机号码"  size="small"></el-input>
</el-form-item>
<el-form-item label="维修类型" prop="typeid">
<el-select v-model="filters.typeid" placeholder="请选择"  size="small">
<el-option label="全部" value=""></el-option>
<el-option v-for="item in repairtypeList" :key="item.typeid" :label="item.typename" :value="item.typeid"></el-option>
</el-select>
</el-form-item>
<el-form-item>
<el-button type="primary" size="small" @click="query" icon="el-icon-search">搜索</el-button>
</el-form-item>
 </el-form>
</el-col>

<el-table :data="datalist" border stripe style="width: 100%"  v-loading="listLoading"   highlight-current-row   max-height="600"     size="small">
<el-table-column prop="rno" label="账号"  align="center"></el-table-column>
<el-table-column prop="password" label="密码"  align="center"></el-table-column>
<el-table-column prop="rnname" label="姓名"  align="center"></el-table-column>
<el-table-column prop="age" label="年龄"  align="center"></el-table-column>
<el-table-column prop="phone" label="手机号码"  align="center"></el-table-column>
<el-table-column prop="typename" label="维修类型"  align="center"></el-table-column>
<el-table-column prop="addtime" label="添加时间"  align="center"></el-table-column>
<el-table-column label="操作" min-width="200" align="center">
<template #default="scope">
<el-button type="primary" size="mini" @click="handleShow(scope.$index, scope.row)" icon="el-icon-zoom-in" style=" padding: 3px 6px 3px 6px;">详情</el-button>
<el-button type="success" size="mini" @click="handleEdit(scope.$index, scope.row)" icon="el-icon-edit" style=" padding: 3px 6px 3px 6px;">编辑</el-button>
<el-button type="danger" size="mini" @click="handleDelete(scope.$index, scope.row)" icon="el-icon-delete" style=" padding: 3px 6px 3px 6px;">删除</el-button>
</template>
</el-table-column>
</el-table>
<el-pagination  @current-change="handleCurrentChange" :current-page="page.currentPage" :page-size="page.pageSize" 
 background layout="total, prev, pager, next, jumper" :total="page.totalCount" 
 style="float: right; margin: 10px 20px 0 0"></el-pagination>

    </div>
</template>

<script>
import request, { base } from "../../../../utils/http";
export default {
  name: 'repairmen',
  components: {
    
  },  
    data() {
      return {
               filters: {
          //列表查询参数
          rno: '',
          rnname: '',
          phone: '',
          typeid: '',
        },

        page: {
          currentPage: 1, // 当前页
          pageSize: 10, // 每页显示条目个数
          totalCount: 0, // 总条目数
        },
        isClear: false,      
        repairtypeList: [], //维修类型

        listLoading: false, //列表加载状态
        btnLoading: false, //保存按钮加载状态
        datalist: [], //表格数据  
    
      };
    },
    created() {
      this.getDatas();
      this.getrepairtypeList();
    },

 
    methods: {    

              
       // 删除维修员
        handleDelete(index, row) {
          this.$confirm("确认删除该记录吗?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          })
            .then(() => {
              this.listLoading = true;
              let url = base + "/repairmen/del?id=" + row.rno;
              request.post(url).then((res) => {
                this.listLoading = false;
             
                this.$message({
                  message: "删除成功",
                  type: "success",
                  offset: 320,
                });
                this.getDatas();
              });
            })
            .catch(() => { });
        },
                
        // 分页
        handleCurrentChange(val) {
          this.page.currentPage = val;
          this.getDatas();
        },     
     
        //获取列表数据
        getDatas() {      
          let para = {
               rno:this.filters.rno,
   rnname:this.filters.rnname,
   phone:this.filters.phone,
   typeid:this.filters.typeid,

          };
          this.listLoading = true;
          let url = base + "/repairmen/list?currentPage=" + this.page.currentPage+ "&pageSize=" + this.page.pageSize;        
          request.post(url, para).then((res) => {   
            if (res.resdata.length > 0) {
              this.isPage = true;
            } else {
              this.isPage = false;
            }
            this.page.totalCount = res.count;
            this.datalist = res.resdata;
            this.listLoading = false;
          });
        },    
                 //查询
        query() {
          this.getDatas();
        },  
            
    getrepairtypeList() {
      let para = {};
      this.listLoading = true;
      let url = base + "/repairtype/list?currentPage=1&pageSize=1000";
      request.post(url, para).then((res) => {
        this.repairtypeList = res.resdata;
      });
    },
   
        // 查看
        handleShow(index, row) {
          this.$router.push({
            path: "/RepairmenDetail",
             query: {
                id: row.rno,
              },
          });
        },
    
        // 编辑
        handleEdit(index, row) {
          this.$router.push({
            path: "/RepairmenEdit",
             query: {
                id: row.rno,
              },
          });
        },
      },
}

</script>
<style scoped>
</style>
 

