{"version": 3, "names": ["_core", "require", "_helper<PERSON>lugin<PERSON><PERSON>s", "_template", "DefineAccessorHelper", "template", "expression", "ast", "_compact", "_default", "declare", "api", "options", "_api$assumption", "assertVersion", "setComputedProperties", "assumption", "loose", "pushComputedProps", "pushComputedPropsLoose", "pushComputedPropsSpec", "buildDefineAccessor", "state", "type", "obj", "key", "fn", "helper", "availableHelper", "addHelper", "file", "get", "id", "scope", "generateUidIdentifier", "push", "init", "set", "t", "cloneNode", "callExpression", "stringLiteral", "getValue", "prop", "isObjectProperty", "value", "isObjectMethod", "functionExpression", "params", "body", "generator", "async", "pushAssign", "objId", "expressionStatement", "assignmentExpression", "memberExpression", "computed", "isLiteral", "pushAccessorDefine", "computedProps", "initPropExpression", "kind", "isIdentifier", "name", "length", "info", "single", "toCom<PERSON><PERSON>ey", "visitor", "ObjectExpression", "exit", "path", "node", "parent", "hasComputed", "properties", "initProps", "foundComputed", "isSpreadElement", "generateUidIdentifierBasedOnNode", "objectExpression", "variableDeclaration", "variableDeclarator", "replaceWith", "replaceWithMultiple", "exports", "default"], "sources": ["../src/index.ts"], "sourcesContent": ["import { types as t } from \"@babel/core\";\nimport type { PluginPass } from \"@babel/core\";\nimport { declare } from \"@babel/helper-plugin-utils\";\nimport template from \"@babel/template\";\nimport type { Scope } from \"@babel/traverse\";\n\nexport interface Options {\n  loose?: boolean;\n}\n\ntype PropertyInfo = {\n  scope: Scope;\n  objId: t.Identifier;\n  body: t.Statement[];\n  computedProps: t.ObjectMember[];\n  initPropExpression: t.ObjectExpression;\n  state: PluginPass;\n};\n\nif (!process.env.BABEL_8_BREAKING) {\n  // eslint-disable-next-line no-var\n  var DefineAccessorHelper = template.expression.ast`\n    function (type, obj, key, fn) {\n      var desc = { configurable: true, enumerable: true };\n      desc[type] = fn;\n      return Object.defineProperty(obj, key, desc);\n    }\n  `;\n  // @ts-expect-error undocumented _compact node property\n  DefineAccessorHelper._compact = true;\n}\n\nexport default declare((api, options: Options) => {\n  api.assertVersion(7);\n\n  const setComputedProperties =\n    api.assumption(\"setComputedProperties\") ?? options.loose;\n\n  const pushComputedProps = setComputedProperties\n    ? pushComputedPropsLoose\n    : pushComputedPropsSpec;\n\n  function buildDefineAccessor(\n    state: PluginPass,\n    type: \"get\" | \"set\",\n    obj: t.Expression,\n    key: t.Expression,\n    fn: t.Expression,\n  ) {\n    if (process.env.BABEL_8_BREAKING) {\n      return t.callExpression(state.addHelper(\"defineAccessor\"), [\n        t.stringLiteral(type),\n        obj,\n        key,\n        fn,\n      ]);\n    } else {\n      let helper: t.Identifier;\n      if (state.availableHelper(\"defineAccessor\")) {\n        helper = state.addHelper(\"defineAccessor\");\n      } else {\n        // Fallback for @babel/helpers <= 7.20.6, manually add helper function\n        const file = state.file;\n        helper = file.get(\"fallbackDefineAccessorHelper\");\n        if (!helper) {\n          const id = file.scope.generateUidIdentifier(\"defineAccessor\");\n          file.scope.push({\n            id,\n            init: DefineAccessorHelper,\n          });\n          file.set(\"fallbackDefineAccessorHelper\", (helper = id));\n        }\n        helper = t.cloneNode(helper);\n      }\n\n      return t.callExpression(helper, [t.stringLiteral(type), obj, key, fn]);\n    }\n  }\n\n  /**\n   * Get value of an object member under object expression.\n   * Returns a function expression if prop is a ObjectMethod.\n   *\n   * @param {t.ObjectMember} prop\n   * @returns t.Expression\n   */\n  function getValue(prop: t.ObjectMember) {\n    if (t.isObjectProperty(prop)) {\n      return prop.value as t.Expression;\n    } else if (t.isObjectMethod(prop)) {\n      return t.functionExpression(\n        null,\n        prop.params,\n        prop.body,\n        prop.generator,\n        prop.async,\n      );\n    }\n  }\n\n  function pushAssign(\n    objId: t.Identifier,\n    prop: t.ObjectMember,\n    body: t.Statement[],\n  ) {\n    body.push(\n      t.expressionStatement(\n        t.assignmentExpression(\n          \"=\",\n          t.memberExpression(\n            t.cloneNode(objId),\n            prop.key,\n            prop.computed || t.isLiteral(prop.key),\n          ),\n          getValue(prop),\n        ),\n      ),\n    );\n  }\n\n  function pushAccessorDefine(\n    { body, computedProps, initPropExpression, objId, state }: PropertyInfo,\n    prop: t.ObjectMethod,\n  ) {\n    const kind = prop.kind as \"get\" | \"set\";\n    const key =\n      !prop.computed && t.isIdentifier(prop.key)\n        ? t.stringLiteral(prop.key.name)\n        : prop.key;\n    const value = getValue(prop);\n\n    if (computedProps.length === 1) {\n      return buildDefineAccessor(state, kind, initPropExpression, key, value);\n    } else {\n      body.push(\n        t.expressionStatement(\n          buildDefineAccessor(state, kind, t.cloneNode(objId), key, value),\n        ),\n      );\n    }\n  }\n\n  function pushComputedPropsLoose(info: PropertyInfo) {\n    for (const prop of info.computedProps) {\n      if (\n        t.isObjectMethod(prop) &&\n        (prop.kind === \"get\" || prop.kind === \"set\")\n      ) {\n        const single = pushAccessorDefine(info, prop);\n        if (single) return single;\n      } else {\n        pushAssign(t.cloneNode(info.objId), prop, info.body);\n      }\n    }\n  }\n\n  function pushComputedPropsSpec(info: PropertyInfo) {\n    const { objId, body, computedProps, state } = info;\n\n    for (const prop of computedProps) {\n      // PrivateName must not be in ObjectExpression\n      const key = t.toComputedKey(prop) as t.Expression;\n\n      if (\n        t.isObjectMethod(prop) &&\n        (prop.kind === \"get\" || prop.kind === \"set\")\n      ) {\n        const single = pushAccessorDefine(info, prop);\n        if (single) return single;\n      } else {\n        // the value of ObjectProperty in ObjectExpression must be an expression\n        const value = getValue(prop);\n        if (computedProps.length === 1) {\n          return t.callExpression(state.addHelper(\"defineProperty\"), [\n            info.initPropExpression,\n            key,\n            value,\n          ]);\n        } else {\n          body.push(\n            t.expressionStatement(\n              t.callExpression(state.addHelper(\"defineProperty\"), [\n                t.cloneNode(objId),\n                key,\n                value,\n              ]),\n            ),\n          );\n        }\n      }\n    }\n  }\n\n  return {\n    name: \"transform-computed-properties\",\n\n    visitor: {\n      ObjectExpression: {\n        exit(path, state) {\n          const { node, parent, scope } = path;\n          let hasComputed = false;\n          for (const prop of node.properties) {\n            // @ts-expect-error SpreadElement must not have computed property\n            hasComputed = prop.computed === true;\n            if (hasComputed) break;\n          }\n          if (!hasComputed) return;\n\n          // put all getters/setters into the first object expression as well as all initialisers up\n          // to the first computed property\n\n          const initProps: t.ObjectMember[] = [];\n          const computedProps: t.ObjectMember[] = [];\n          let foundComputed = false;\n\n          for (const prop of node.properties) {\n            if (t.isSpreadElement(prop)) {\n              continue;\n            }\n            if (prop.computed) {\n              foundComputed = true;\n            }\n\n            if (foundComputed) {\n              computedProps.push(prop);\n            } else {\n              initProps.push(prop);\n            }\n          }\n\n          const objId = scope.generateUidIdentifierBasedOnNode(parent);\n          const initPropExpression = t.objectExpression(initProps);\n          const body = [];\n\n          body.push(\n            t.variableDeclaration(\"var\", [\n              t.variableDeclarator(objId, initPropExpression),\n            ]),\n          );\n\n          const single = pushComputedProps({\n            scope,\n            objId,\n            body,\n            computedProps,\n            initPropExpression,\n            state,\n          });\n\n          if (single) {\n            path.replaceWith(single);\n          } else {\n            body.push(t.expressionStatement(t.cloneNode(objId)));\n            path.replaceWithMultiple(body);\n          }\n        },\n      },\n    },\n  };\n});\n"], "mappings": ";;;;;;AAAA,IAAAA,KAAA,GAAAC,OAAA;AAEA,IAAAC,kBAAA,GAAAD,OAAA;AACA,IAAAE,SAAA,GAAAF,OAAA;AAgBmC;EAEjC,IAAIG,oBAAoB,GAAGC,iBAAQ,CAACC,UAAU,CAACC,GAAI;AACrD;AACA;AACA;AACA;AACA;AACA,GAAG;EAEDH,oBAAoB,CAACI,QAAQ,GAAG,IAAI;AACtC;AAAC,IAAAC,QAAA,GAEc,IAAAC,0BAAO,EAAC,CAACC,GAAG,EAAEC,OAAgB,KAAK;EAAA,IAAAC,eAAA;EAChDF,GAAG,CAACG,aAAa,CAAC,CAAC,CAAC;EAEpB,MAAMC,qBAAqB,IAAAF,eAAA,GACzBF,GAAG,CAACK,UAAU,CAAC,uBAAuB,CAAC,YAAAH,eAAA,GAAID,OAAO,CAACK,KAAK;EAE1D,MAAMC,iBAAiB,GAAGH,qBAAqB,GAC3CI,sBAAsB,GACtBC,qBAAqB;EAEzB,SAASC,mBAAmBA,CAC1BC,KAAiB,EACjBC,IAAmB,EACnBC,GAAiB,EACjBC,GAAiB,EACjBC,EAAgB,EAChB;IAQO;MACL,IAAIC,MAAoB;MACxB,IAAIL,KAAK,CAACM,eAAe,CAAC,gBAAgB,CAAC,EAAE;QAC3CD,MAAM,GAAGL,KAAK,CAACO,SAAS,CAAC,gBAAgB,CAAC;MAC5C,CAAC,MAAM;QAEL,MAAMC,IAAI,GAAGR,KAAK,CAACQ,IAAI;QACvBH,MAAM,GAAGG,IAAI,CAACC,GAAG,CAAC,8BAA8B,CAAC;QACjD,IAAI,CAACJ,MAAM,EAAE;UACX,MAAMK,EAAE,GAAGF,IAAI,CAACG,KAAK,CAACC,qBAAqB,CAAC,gBAAgB,CAAC;UAC7DJ,IAAI,CAACG,KAAK,CAACE,IAAI,CAAC;YACdH,EAAE;YACFI,IAAI,EAAEhC;UACR,CAAC,CAAC;UACF0B,IAAI,CAACO,GAAG,CAAC,8BAA8B,EAAGV,MAAM,GAAGK,EAAG,CAAC;QACzD;QACAL,MAAM,GAAGW,WAAC,CAACC,SAAS,CAACZ,MAAM,CAAC;MAC9B;MAEA,OAAOW,WAAC,CAACE,cAAc,CAACb,MAAM,EAAE,CAACW,WAAC,CAACG,aAAa,CAAClB,IAAI,CAAC,EAAEC,GAAG,EAAEC,GAAG,EAAEC,EAAE,CAAC,CAAC;IACxE;EACF;EASA,SAASgB,QAAQA,CAACC,IAAoB,EAAE;IACtC,IAAIL,WAAC,CAACM,gBAAgB,CAACD,IAAI,CAAC,EAAE;MAC5B,OAAOA,IAAI,CAACE,KAAK;IACnB,CAAC,MAAM,IAAIP,WAAC,CAACQ,cAAc,CAACH,IAAI,CAAC,EAAE;MACjC,OAAOL,WAAC,CAACS,kBAAkB,CACzB,IAAI,EACJJ,IAAI,CAACK,MAAM,EACXL,IAAI,CAACM,IAAI,EACTN,IAAI,CAACO,SAAS,EACdP,IAAI,CAACQ,KACP,CAAC;IACH;EACF;EAEA,SAASC,UAAUA,CACjBC,KAAmB,EACnBV,IAAoB,EACpBM,IAAmB,EACnB;IACAA,IAAI,CAACd,IAAI,CACPG,WAAC,CAACgB,mBAAmB,CACnBhB,WAAC,CAACiB,oBAAoB,CACpB,GAAG,EACHjB,WAAC,CAACkB,gBAAgB,CAChBlB,WAAC,CAACC,SAAS,CAACc,KAAK,CAAC,EAClBV,IAAI,CAAClB,GAAG,EACRkB,IAAI,CAACc,QAAQ,IAAInB,WAAC,CAACoB,SAAS,CAACf,IAAI,CAAClB,GAAG,CACvC,CAAC,EACDiB,QAAQ,CAACC,IAAI,CACf,CACF,CACF,CAAC;EACH;EAEA,SAASgB,kBAAkBA,CACzB;IAAEV,IAAI;IAAEW,aAAa;IAAEC,kBAAkB;IAAER,KAAK;IAAE/B;EAAoB,CAAC,EACvEqB,IAAoB,EACpB;IACA,MAAMmB,IAAI,GAAGnB,IAAI,CAACmB,IAAqB;IACvC,MAAMrC,GAAG,GACP,CAACkB,IAAI,CAACc,QAAQ,IAAInB,WAAC,CAACyB,YAAY,CAACpB,IAAI,CAAClB,GAAG,CAAC,GACtCa,WAAC,CAACG,aAAa,CAACE,IAAI,CAAClB,GAAG,CAACuC,IAAI,CAAC,GAC9BrB,IAAI,CAAClB,GAAG;IACd,MAAMoB,KAAK,GAAGH,QAAQ,CAACC,IAAI,CAAC;IAE5B,IAAIiB,aAAa,CAACK,MAAM,KAAK,CAAC,EAAE;MAC9B,OAAO5C,mBAAmB,CAACC,KAAK,EAAEwC,IAAI,EAAED,kBAAkB,EAAEpC,GAAG,EAAEoB,KAAK,CAAC;IACzE,CAAC,MAAM;MACLI,IAAI,CAACd,IAAI,CACPG,WAAC,CAACgB,mBAAmB,CACnBjC,mBAAmB,CAACC,KAAK,EAAEwC,IAAI,EAAExB,WAAC,CAACC,SAAS,CAACc,KAAK,CAAC,EAAE5B,GAAG,EAAEoB,KAAK,CACjE,CACF,CAAC;IACH;EACF;EAEA,SAAS1B,sBAAsBA,CAAC+C,IAAkB,EAAE;IAClD,KAAK,MAAMvB,IAAI,IAAIuB,IAAI,CAACN,aAAa,EAAE;MACrC,IACEtB,WAAC,CAACQ,cAAc,CAACH,IAAI,CAAC,KACrBA,IAAI,CAACmB,IAAI,KAAK,KAAK,IAAInB,IAAI,CAACmB,IAAI,KAAK,KAAK,CAAC,EAC5C;QACA,MAAMK,MAAM,GAAGR,kBAAkB,CAACO,IAAI,EAAEvB,IAAI,CAAC;QAC7C,IAAIwB,MAAM,EAAE,OAAOA,MAAM;MAC3B,CAAC,MAAM;QACLf,UAAU,CAACd,WAAC,CAACC,SAAS,CAAC2B,IAAI,CAACb,KAAK,CAAC,EAAEV,IAAI,EAAEuB,IAAI,CAACjB,IAAI,CAAC;MACtD;IACF;EACF;EAEA,SAAS7B,qBAAqBA,CAAC8C,IAAkB,EAAE;IACjD,MAAM;MAAEb,KAAK;MAAEJ,IAAI;MAAEW,aAAa;MAAEtC;IAAM,CAAC,GAAG4C,IAAI;IAElD,KAAK,MAAMvB,IAAI,IAAIiB,aAAa,EAAE;MAEhC,MAAMnC,GAAG,GAAGa,WAAC,CAAC8B,aAAa,CAACzB,IAAI,CAAiB;MAEjD,IACEL,WAAC,CAACQ,cAAc,CAACH,IAAI,CAAC,KACrBA,IAAI,CAACmB,IAAI,KAAK,KAAK,IAAInB,IAAI,CAACmB,IAAI,KAAK,KAAK,CAAC,EAC5C;QACA,MAAMK,MAAM,GAAGR,kBAAkB,CAACO,IAAI,EAAEvB,IAAI,CAAC;QAC7C,IAAIwB,MAAM,EAAE,OAAOA,MAAM;MAC3B,CAAC,MAAM;QAEL,MAAMtB,KAAK,GAAGH,QAAQ,CAACC,IAAI,CAAC;QAC5B,IAAIiB,aAAa,CAACK,MAAM,KAAK,CAAC,EAAE;UAC9B,OAAO3B,WAAC,CAACE,cAAc,CAAClB,KAAK,CAACO,SAAS,CAAC,gBAAgB,CAAC,EAAE,CACzDqC,IAAI,CAACL,kBAAkB,EACvBpC,GAAG,EACHoB,KAAK,CACN,CAAC;QACJ,CAAC,MAAM;UACLI,IAAI,CAACd,IAAI,CACPG,WAAC,CAACgB,mBAAmB,CACnBhB,WAAC,CAACE,cAAc,CAAClB,KAAK,CAACO,SAAS,CAAC,gBAAgB,CAAC,EAAE,CAClDS,WAAC,CAACC,SAAS,CAACc,KAAK,CAAC,EAClB5B,GAAG,EACHoB,KAAK,CACN,CACH,CACF,CAAC;QACH;MACF;IACF;EACF;EAEA,OAAO;IACLmB,IAAI,EAAE,+BAA+B;IAErCK,OAAO,EAAE;MACPC,gBAAgB,EAAE;QAChBC,IAAIA,CAACC,IAAI,EAAElD,KAAK,EAAE;UAChB,MAAM;YAAEmD,IAAI;YAAEC,MAAM;YAAEzC;UAAM,CAAC,GAAGuC,IAAI;UACpC,IAAIG,WAAW,GAAG,KAAK;UACvB,KAAK,MAAMhC,IAAI,IAAI8B,IAAI,CAACG,UAAU,EAAE;YAElCD,WAAW,GAAGhC,IAAI,CAACc,QAAQ,KAAK,IAAI;YACpC,IAAIkB,WAAW,EAAE;UACnB;UACA,IAAI,CAACA,WAAW,EAAE;UAKlB,MAAME,SAA2B,GAAG,EAAE;UACtC,MAAMjB,aAA+B,GAAG,EAAE;UAC1C,IAAIkB,aAAa,GAAG,KAAK;UAEzB,KAAK,MAAMnC,IAAI,IAAI8B,IAAI,CAACG,UAAU,EAAE;YAClC,IAAItC,WAAC,CAACyC,eAAe,CAACpC,IAAI,CAAC,EAAE;cAC3B;YACF;YACA,IAAIA,IAAI,CAACc,QAAQ,EAAE;cACjBqB,aAAa,GAAG,IAAI;YACtB;YAEA,IAAIA,aAAa,EAAE;cACjBlB,aAAa,CAACzB,IAAI,CAACQ,IAAI,CAAC;YAC1B,CAAC,MAAM;cACLkC,SAAS,CAAC1C,IAAI,CAACQ,IAAI,CAAC;YACtB;UACF;UAEA,MAAMU,KAAK,GAAGpB,KAAK,CAAC+C,gCAAgC,CAACN,MAAM,CAAC;UAC5D,MAAMb,kBAAkB,GAAGvB,WAAC,CAAC2C,gBAAgB,CAACJ,SAAS,CAAC;UACxD,MAAM5B,IAAI,GAAG,EAAE;UAEfA,IAAI,CAACd,IAAI,CACPG,WAAC,CAAC4C,mBAAmB,CAAC,KAAK,EAAE,CAC3B5C,WAAC,CAAC6C,kBAAkB,CAAC9B,KAAK,EAAEQ,kBAAkB,CAAC,CAChD,CACH,CAAC;UAED,MAAMM,MAAM,GAAGjD,iBAAiB,CAAC;YAC/Be,KAAK;YACLoB,KAAK;YACLJ,IAAI;YACJW,aAAa;YACbC,kBAAkB;YAClBvC;UACF,CAAC,CAAC;UAEF,IAAI6C,MAAM,EAAE;YACVK,IAAI,CAACY,WAAW,CAACjB,MAAM,CAAC;UAC1B,CAAC,MAAM;YACLlB,IAAI,CAACd,IAAI,CAACG,WAAC,CAACgB,mBAAmB,CAAChB,WAAC,CAACC,SAAS,CAACc,KAAK,CAAC,CAAC,CAAC;YACpDmB,IAAI,CAACa,mBAAmB,CAACpC,IAAI,CAAC;UAChC;QACF;MACF;IACF;EACF,CAAC;AACH,CAAC,CAAC;AAAAqC,OAAA,CAAAC,OAAA,GAAA9E,QAAA"}