{"remainingRequest": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\admin\\student\\MyDormitory.vue?vue&type=template&id=05e4597b&scoped=true", "dependencies": [{"path": "I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\admin\\student\\MyDormitory.vue", "mtime": 1749046559931}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\babel.config.js", "mtime": 1749034452000}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749043011899}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749043012919}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749043013784}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749043011899}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749043013130}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_createElementVNode", "slot", "class", "style", "_hoisted_6", "_createElementBlock", "_createCommentVNode", "_createVNode", "_component_el_card", "_hoisted_1", "$data", "currentDormitory", "doro", "_hoisted_2", "_component_el_row", "gutter", "_component_el_col", "span", "_hoisted_3", "dbname", "_hoisted_4", "_hoisted_5", "_hoisted_7", "_hoisted_8", "_hoisted_9", "_component_el_button", "type", "onClick", "_cache", "$event", "showApplyForm", "_component_el_form", "model", "applyForm", "rules", "applyRules", "ref", "_component_el_form_item", "label", "prop", "_component_el_select", "dbid2", "placeholder", "onChange", "$options", "onBuildingChange", "_Fragment", "_renderList", "dormbuildingList", "item", "_createBlock", "_component_el_option", "key", "dbid", "value", "doro2", "disabled", "filteredDormitoryList", "_component_el_input", "<PERSON><PERSON><PERSON>", "rows", "submitApplication", "loading", "btnLoading", "resetApplyForm", "_hoisted_12", "_hoisted_13", "getApplicationHistory", "_component_el_table", "data", "applicationList", "_component_el_table_column", "width", "_hoisted_15", "_ctx", "scope", "row", "_toDisplayString", "_hoisted_16", "dbname2", "_hoisted_17", "_component_el_tag", "getStatusType", "reviewstatus", "listLoading"], "sources": ["I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\admin\\student\\MyDormitory.vue"], "sourcesContent": ["<template>\n  <div>\n    <!-- 当前宿舍信息 -->\n    <el-card class=\"box-card\" style=\"margin-bottom: 20px;\">\n      <div slot=\"header\" class=\"clearfix\">\n        <span style=\"font-size: 18px; font-weight: bold;\">我的宿舍信息</span>\n      </div>\n      <div v-if=\"currentDormitory.doro\">\n        <el-row :gutter=\"20\">\n          <el-col :span=\"24\">\n            <p><strong>宿舍楼：</strong>{{ currentDormitory.dbname }}</p>\n            <p><strong>宿舍编号：</strong>{{ currentDormitory.doro }}</p>\n          </el-col>\n       \n        </el-row>\n      </div>\n      <div v-else>\n        <p style=\"color: #999;\">暂未分配宿舍</p>\n      </div>\n    </el-card>\n\n    <!-- 申请更换宿舍 -->\n    <el-card class=\"box-card\" style=\"margin-bottom: 20px;\">\n      <div slot=\"header\" class=\"clearfix\">\n        <span style=\"font-size: 18px; font-weight: bold;\">申请更换宿舍</span>\n        <el-button style=\"float: right; padding: 3px 0\" type=\"text\" @click=\"showApplyForm = !showApplyForm\">\n          {{ showApplyForm ? '收起' : '展开' }}\n        </el-button>\n      </div>\n      <div v-show=\"showApplyForm\">\n        <el-form :model=\"applyForm\" :rules=\"applyRules\" ref=\"applyFormRef\" label-width=\"120px\">\n          <el-form-item label=\"新宿舍楼\" prop=\"dbid2\">\n            <el-select v-model=\"applyForm.dbid2\" placeholder=\"请选择宿舍楼\" @change=\"onBuildingChange\" style=\"width: 300px;\">\n              <el-option v-for=\"item in dormbuildingList\" :key=\"item.dbid\" :label=\"item.dbname\" :value=\"item.dbid\"></el-option>\n            </el-select>\n          </el-form-item>\n          <el-form-item label=\"新宿舍\" prop=\"doro2\">\n            <el-select v-model=\"applyForm.doro2\" placeholder=\"请先选择宿舍楼\" :disabled=\"!applyForm.dbid2\" style=\"width: 300px;\">\n              <el-option v-for=\"item in filteredDormitoryList\" :key=\"item.doro\" :label=\"item.doro\" :value=\"item.doro\"></el-option>\n            </el-select>\n          </el-form-item>\n          <el-form-item label=\"申请原因\" prop=\"applicationreason\">\n            <el-input type=\"textarea\" v-model=\"applyForm.applicationreason\" placeholder=\"请输入申请更换宿舍的原因\" :rows=\"4\" style=\"width: 500px;\"></el-input>\n          </el-form-item>\n          <el-form-item>\n            <el-button type=\"primary\" @click=\"submitApplication\" :loading=\"btnLoading\">提交申请</el-button>\n            <el-button @click=\"resetApplyForm\">重置</el-button>\n          </el-form-item>\n        </el-form>\n      </div>\n    </el-card>\n\n    <!-- 申请历史 -->\n    <el-card class=\"box-card\">\n      <div slot=\"header\" class=\"clearfix\">\n        <span style=\"font-size: 18px; font-weight: bold;\">申请历史</span>\n        <el-button style=\"float: right; padding: 3px 0\" type=\"text\" @click=\"getApplicationHistory\">刷新</el-button>\n      </div>\n      <el-table :data=\"applicationList\" style=\"width: 100%\" v-loading=\"listLoading\">\n        <el-table-column prop=\"submissiontime\" label=\"申请时间\" width=\"150\"></el-table-column>\n        <el-table-column label=\"原宿舍\" width=\"150\">\n          <template slot-scope=\"scope\">\n            {{ scope.row.dbname }} - {{ scope.row.doro }}\n          </template>\n        </el-table-column>\n        <el-table-column label=\"目标宿舍\" width=\"150\">\n          <template slot-scope=\"scope\">\n            {{ scope.row.dbname2 }} - {{ scope.row.doro2 }}\n          </template>\n        </el-table-column>\n        <el-table-column prop=\"applicationreason\" label=\"申请原因\" show-overflow-tooltip></el-table-column>\n        <el-table-column prop=\"reviewstatus\" label=\"审核状态\" width=\"100\">\n          <template slot-scope=\"scope\">\n            <el-tag :type=\"getStatusType(scope.row.reviewstatus)\">{{ scope.row.reviewstatus }}</el-tag>\n          </template>\n        </el-table-column>\n        <el-table-column prop=\"reviewresponse\" label=\"审核回复\" show-overflow-tooltip></el-table-column>\n      </el-table>\n    </el-card>\n  </div>\n</template>\n\n<script>\nimport request, { base } from \"../../../../utils/http\";\n\nexport default {\n  name: 'MyDormitory',\n  data() {\n    return {\n      currentDormitory: {}, // 当前宿舍信息\n      showApplyForm: false, // 是否显示申请表单\n      btnLoading: false, // 提交按钮加载状态\n      listLoading: false, // 列表加载状态\n      \n      // 申请表单\n      applyForm: {\n        sno: '',\n        dbid: null, // 原宿舍楼ID\n        doro: '', // 原宿舍编号\n        dbid2: null, // 新宿舍楼ID\n        doro2: '', // 新宿舍编号\n        applicationreason: ''\n      },\n      \n      // 表单验证规则\n      applyRules: {\n        dbid2: [{ required: true, message: '请选择新宿舍楼', trigger: 'change' }],\n        doro2: [{ required: true, message: '请选择新宿舍', trigger: 'change' }],\n        applicationreason: [{ required: true, message: '请输入申请原因', trigger: 'blur' }]\n      },\n      \n      // 数据列表\n      dormbuildingList: [], // 宿舍楼列表\n      filteredDormitoryList: [], // 过滤后的宿舍列表\n      applicationList: [], // 申请历史列表\n      \n      // 学生信息\n      studentInfo: {}\n    };\n  },\n  \n  created() {\n    this.getStudentInfo();\n    this.getDormbuildingList();\n    this.getApplicationHistory();\n  },\n  \n  methods: {\n    // 获取学生信息和当前宿舍信息\n    getStudentInfo() {\n      const user = JSON.parse(sessionStorage.getItem(\"user\"));\n      this.studentInfo = user;\n      this.applyForm.sno = user.sno;\n      \n      let url = base + \"/student/get?id=\" + user.sno;\n      request.post(url, {}).then((res) => {\n        if (res.code == 200) {\n          this.currentDormitory = res.resdata;\n          // 设置原宿舍信息\n          this.applyForm.dbid = res.resdata.dbid;\n          this.applyForm.doro = res.resdata.doro;\n        }\n      });\n    },\n    \n    // 获取宿舍楼列表\n    getDormbuildingList() {\n      let url = base + \"/dormbuilding/list?currentPage=1&pageSize=1000\";\n      request.post(url, {}).then((res) => {\n        this.dormbuildingList = res.resdata;\n      });\n    },\n    \n    // 宿舍楼变化时的处理\n    onBuildingChange() {\n      this.applyForm.doro2 = ''; // 清空宿舍选择\n      this.updateDormitoryList();\n    },\n    \n    // 更新宿舍列表（根据宿舍楼和学生性别过滤）\n    updateDormitoryList() {\n      if (!this.applyForm.dbid2 || !this.studentInfo.sex) {\n        this.filteredDormitoryList = [];\n        return;\n      }\n\n      let para = {\n        dbid: this.applyForm.dbid2,\n        dorgender: this.studentInfo.sex\n      };\n      let url = base + \"/dormitory/listByBuildingAndGender\";\n      request.post(url, para).then((res) => {\n        if (res.code == 200) {\n          // 过滤掉当前宿舍\n          this.filteredDormitoryList = res.resdata.filter(item => \n            !(item.dbid == this.applyForm.dbid && item.doro == this.applyForm.doro)\n          );\n        } else {\n          this.filteredDormitoryList = [];\n          this.$message({\n            message: res.msg || \"获取宿舍列表失败\",\n            type: \"error\"\n          });\n        }\n      }).catch(() => {\n        this.filteredDormitoryList = [];\n        this.$message({\n          message: \"获取宿舍列表失败\",\n          type: \"error\"\n        });\n      });\n    },\n    \n    // 提交申请\n    submitApplication() {\n      this.$refs.applyFormRef.validate((valid) => {\n        if (valid) {\n          // 检查是否选择了不同的宿舍\n          if (this.applyForm.dbid == this.applyForm.dbid2 && this.applyForm.doro == this.applyForm.doro2) {\n            this.$message({\n              message: \"新宿舍不能与当前宿舍相同\",\n              type: \"warning\"\n            });\n            return;\n          }\n          \n          this.btnLoading = true;\n          let url = base + \"/dormitorychange/apply\";\n          request.post(url, this.applyForm).then((res) => {\n            if (res.code == 200) {\n              this.$message({\n                message: \"申请提交成功，请等待审核\",\n                type: \"success\"\n              });\n              this.resetApplyForm();\n              this.showApplyForm = false;\n              this.getApplicationHistory(); // 刷新申请历史\n            } else {\n              this.$message({\n                message: res.msg || \"申请提交失败\",\n                type: \"error\"\n              });\n            }\n            this.btnLoading = false;\n          }).catch(() => {\n            this.$message({\n              message: \"申请提交失败\",\n              type: \"error\"\n            });\n            this.btnLoading = false;\n          });\n        }\n      });\n    },\n    \n    // 重置申请表单\n    resetApplyForm() {\n      this.$refs.applyFormRef.resetFields();\n      this.applyForm.dbid2 = null;\n      this.applyForm.doro2 = '';\n      this.applyForm.applicationreason = '';\n      this.filteredDormitoryList = [];\n    },\n    \n    // 获取申请历史\n    getApplicationHistory() {\n      this.listLoading = true;\n      let para = {\n        sno: this.studentInfo.sno\n      };\n      let url = base + \"/dormitorychange/list?currentPage=1&pageSize=100\";\n      request.post(url, para).then((res) => {\n        if (res.code == 200) {\n          this.applicationList = res.resdata;\n        }\n        this.listLoading = false;\n      }).catch(() => {\n        this.listLoading = false;\n      });\n    },\n    \n    // 获取状态标签类型\n    getStatusType(status) {\n      switch (status) {\n        case '待审核':\n          return 'warning';\n        case '审核通过':\n          return 'success';\n        case '审核不通过':\n          return 'danger';\n        default:\n          return 'info';\n      }\n    }\n  }\n};\n</script>\n\n<style scoped>\n.box-card {\n  margin-bottom: 20px;\n}\n\n.clearfix:before,\n.clearfix:after {\n  display: table;\n  content: \"\";\n}\n\n.clearfix:after {\n  clear: both;\n}\n</style>\n"], "mappings": ";;gEAIMA,mBAAA,CAEM;EAFDC,IAAI,EAAC,QAAQ;EAACC,KAAK,EAAC;iBACvBF,mBAAA,CAA+D;EAAzDG,KAA2C,EAA3C;IAAA;IAAA;EAAA;AAA2C,GAAC,QAAM,E;;;;gEAKjDH,mBAAA,CAAqB,gBAAb,MAAI;gEACZA,mBAAA,CAAsB,gBAAd,OAAK;;;;gEAMpBA,mBAAA,CAAkC;EAA/BG,KAAoB,EAApB;IAAA;EAAA;AAAoB,GAAC,QAAM;oBAA9BC,UAAkC,C;;EAM/BH,IAAI,EAAC,QAAQ;EAACC,KAAK,EAAC;;gEACvBF,mBAAA,CAA+D;EAAzDG,KAA2C,EAA3C;IAAA;IAAA;EAAA;AAA2C,GAAC,QAAM;kDAqBuB,MAAI;kDAC5C,IAAE;;EAQtCF,IAAI,EAAC,QAAQ;EAACC,KAAK,EAAC;;iEACvBF,mBAAA,CAA6D;EAAvDG,KAA2C,EAA3C;IAAA;IAAA;EAAA;AAA2C,GAAC,MAAI;kDACqC,IAAE;;EAKjF,YAAU,EAAC;AAAO;;EAKlB,YAAU,EAAC;AAAO;;EAMlB,YAAU,EAAC;AAAO;;;;;;;;;;;;;;;uBAvEpCE,mBAAA,CA8EM,cA7EJC,mBAAA,YAAe,EACfC,YAAA,CAgBUC,kBAAA;IAhBDN,KAAK,EAAC,UAAU;IAACC,KAA4B,EAA5B;MAAA;IAAA;;sBACxB,MAEM,CAFNM,UAEM,EACKC,KAAA,CAAAC,gBAAgB,CAACC,IAAI,I,cAAhCP,mBAAA,CAQM,OAAAQ,UAAA,GAPJN,YAAA,CAMSO,iBAAA;MANAC,MAAM,EAAE;IAAE;wBACjB,MAGS,CAHTR,YAAA,CAGSS,iBAAA;QAHAC,IAAI,EAAE;MAAE;0BACf,MAAyD,CAAzDjB,mBAAA,CAAyD,YAAtDkB,UAAqB,E,kCAAGR,KAAA,CAAAC,gBAAgB,CAACQ,MAAM,iB,GAClDnB,mBAAA,CAAwD,YAArDoB,UAAsB,E,kCAAGV,KAAA,CAAAC,gBAAgB,CAACC,IAAI,iB;;;;;;2BAKvDP,mBAAA,CAEM,OAAAgB,UAAA,EAAAC,UAAA,G;;MAGRhB,mBAAA,YAAe,EACfC,YAAA,CA4BUC,kBAAA;IA5BDN,KAAK,EAAC,UAAU;IAACC,KAA4B,EAA5B;MAAA;IAAA;;sBACxB,MAKM,CALNH,mBAAA,CAKM,OALNuB,UAKM,GAJJC,UAA+D,EAC/DjB,YAAA,CAEYkB,oBAAA;MAFDtB,KAAoC,EAApC;QAAA;QAAA;MAAA,CAAoC;MAACuB,IAAI,EAAC,MAAM;MAAEC,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEnB,KAAA,CAAAoB,aAAa,IAAIpB,KAAA,CAAAoB,aAAa;;wBAChG,MAAiC,C,kCAA9BpB,KAAA,CAAAoB,aAAa,+B;;;0BAGpB9B,mBAAA,CAoBM,cAnBJO,YAAA,CAkBUwB,kBAAA;MAlBAC,KAAK,EAAEtB,KAAA,CAAAuB,SAAS;MAAGC,KAAK,EAAExB,KAAA,CAAAyB,UAAU;MAAEC,GAAG,EAAC,cAAc;MAAC,aAAW,EAAC;;wBAC7E,MAIe,CAJf7B,YAAA,CAIe8B,uBAAA;QAJDC,KAAK,EAAC,MAAM;QAACC,IAAI,EAAC;;0BAC9B,MAEY,CAFZhC,YAAA,CAEYiC,oBAAA;sBAFQ9B,KAAA,CAAAuB,SAAS,CAACQ,KAAK;qEAAf/B,KAAA,CAAAuB,SAAS,CAACQ,KAAK,GAAAZ,MAAA;UAAEa,WAAW,EAAC,QAAQ;UAAEC,QAAM,EAAEC,QAAA,CAAAC,gBAAgB;UAAE1C,KAAqB,EAArB;YAAA;UAAA;;4BACxE,MAAgC,E,kBAA3CE,mBAAA,CAAiHyC,SAAA,QAAAC,WAAA,CAAvFrC,KAAA,CAAAsC,gBAAgB,EAAxBC,IAAI;iCAAtBC,YAAA,CAAiHC,oBAAA;cAApEC,GAAG,EAAEH,IAAI,CAACI,IAAI;cAAGf,KAAK,EAAEW,IAAI,CAAC9B,MAAM;cAAGmC,KAAK,EAAEL,IAAI,CAACI;;;;;;;UAGnG9C,YAAA,CAIe8B,uBAAA;QAJDC,KAAK,EAAC,KAAK;QAACC,IAAI,EAAC;;0BAC7B,MAEY,CAFZhC,YAAA,CAEYiC,oBAAA;sBAFQ9B,KAAA,CAAAuB,SAAS,CAACsB,KAAK;qEAAf7C,KAAA,CAAAuB,SAAS,CAACsB,KAAK,GAAA1B,MAAA;UAAEa,WAAW,EAAC,SAAS;UAAEc,QAAQ,GAAG9C,KAAA,CAAAuB,SAAS,CAACQ,KAAK;UAAEtC,KAAqB,EAArB;YAAA;UAAA;;4BAC3E,MAAqC,E,kBAAhDE,mBAAA,CAAoHyC,SAAA,QAAAC,WAAA,CAA1FrC,KAAA,CAAA+C,qBAAqB,EAA7BR,IAAI;iCAAtBC,YAAA,CAAoHC,oBAAA;cAAlEC,GAAG,EAAEH,IAAI,CAACrC,IAAI;cAAG0B,KAAK,EAAEW,IAAI,CAACrC,IAAI;cAAG0C,KAAK,EAAEL,IAAI,CAACrC;;;;;;;UAGtGL,YAAA,CAEe8B,uBAAA;QAFDC,KAAK,EAAC,MAAM;QAACC,IAAI,EAAC;;0BAC9B,MAAsI,CAAtIhC,YAAA,CAAsImD,mBAAA;UAA5HhC,IAAI,EAAC,UAAU;sBAAUhB,KAAA,CAAAuB,SAAS,CAAC0B,iBAAiB;qEAA3BjD,KAAA,CAAAuB,SAAS,CAAC0B,iBAAiB,GAAA9B,MAAA;UAAEa,WAAW,EAAC,cAAc;UAAEkB,IAAI,EAAE,CAAC;UAAEzD,KAAqB,EAArB;YAAA;UAAA;;;UAEvGI,YAAA,CAGe8B,uBAAA;0BAFb,MAA2F,CAA3F9B,YAAA,CAA2FkB,oBAAA;UAAhFC,IAAI,EAAC,SAAS;UAAEC,OAAK,EAAEiB,QAAA,CAAAiB,iBAAiB;UAAGC,OAAO,EAAEpD,KAAA,CAAAqD;;4BAAY,MAAI,C;;mDAC/ExD,YAAA,CAAiDkB,oBAAA;UAArCE,OAAK,EAAEiB,QAAA,CAAAoB;QAAc;4BAAE,MAAE,C;;;;;;;6EAjB9BtD,KAAA,CAAAoB,aAAa,E;;MAuB5BxB,mBAAA,UAAa,EACbC,YAAA,CAyBUC,kBAAA;IAzBDN,KAAK,EAAC;EAAU;sBACvB,MAGM,CAHNF,mBAAA,CAGM,OAHNiE,WAGM,GAFJC,WAA6D,EAC7D3D,YAAA,CAAyGkB,oBAAA;MAA9FtB,KAAoC,EAApC;QAAA;QAAA;MAAA,CAAoC;MAACuB,IAAI,EAAC,MAAM;MAAEC,OAAK,EAAEiB,QAAA,CAAAuB;;wBAAuB,MAAE,C;;sDAE/F5D,YAAA,CAmBW6D,mBAAA;MAnBAC,IAAI,EAAE3D,KAAA,CAAA4D,eAAe;MAAEnE,KAAmB,EAAnB;QAAA;MAAA;;wBAChC,MAAkF,CAAlFI,YAAA,CAAkFgE,0BAAA;QAAjEhC,IAAI,EAAC,gBAAgB;QAACD,KAAK,EAAC,MAAM;QAACkC,KAAK,EAAC;UAC1DjE,YAAA,CAIkBgE,0BAAA;QAJDjC,KAAK,EAAC,KAAK;QAACkC,KAAK,EAAC;;0BACjC,MAEW,CAFXxE,mBAAA,CAEW,YAFXyE,WAEW,G,kCADNC,IAAA,CAAAC,KAAK,CAACC,GAAG,CAACzD,MAAM,IAAG,KAAG,GAAA0D,gBAAA,CAAGH,IAAA,CAAAC,KAAK,CAACC,GAAG,CAAChE,IAAI,iB;;;UAG9CL,YAAA,CAIkBgE,0BAAA;QAJDjC,KAAK,EAAC,MAAM;QAACkC,KAAK,EAAC;;0BAClC,MAEW,CAFXxE,mBAAA,CAEW,YAFX8E,WAEW,G,kCADNJ,IAAA,CAAAC,KAAK,CAACC,GAAG,CAACG,OAAO,IAAG,KAAG,GAAAF,gBAAA,CAAGH,IAAA,CAAAC,KAAK,CAACC,GAAG,CAACrB,KAAK,iB;;;UAGhDhD,YAAA,CAA+FgE,0BAAA;QAA9EhC,IAAI,EAAC,mBAAmB;QAACD,KAAK,EAAC,MAAM;QAAC,uBAAqB,EAArB;UACvD/B,YAAA,CAIkBgE,0BAAA;QAJDhC,IAAI,EAAC,cAAc;QAACD,KAAK,EAAC,MAAM;QAACkC,KAAK,EAAC;;0BACtD,MAEW,CAFXxE,mBAAA,CAEW,YAFXgF,WAEW,GADTzE,YAAA,CAA2F0E,iBAAA;UAAlFvD,IAAI,EAAEkB,QAAA,CAAAsC,aAAa,CAACR,IAAA,CAAAC,KAAK,CAACC,GAAG,CAACO,YAAY;;4BAAG,MAA4B,C,kCAAzBT,IAAA,CAAAC,KAAK,CAACC,GAAG,CAACO,YAAY,iB;;;;;UAGnF5E,YAAA,CAA4FgE,0BAAA;QAA3EhC,IAAI,EAAC,gBAAgB;QAACD,KAAK,EAAC,MAAM;QAAC,uBAAqB,EAArB;;;uDAlBW5B,KAAA,CAAA0E,WAAW,E"}]}